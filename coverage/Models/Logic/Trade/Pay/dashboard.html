<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Logic/Trade/Pay</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Logic</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Trade</a></li>
         <li class="breadcrumb-item"><a href="index.html">Pay</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#23">App\Models\Logic\Trade\Pay\AD</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SG.php.html#20">App\Models\Logic\Trade\Pay\SG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#12">App\Models\Logic\Trade\Pay\ZHUOYIY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#12">App\Models\Logic\Trade\Pay\ZHUOYIQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZDC.php.html#18">App\Models\Logic\Trade\Pay\ZDC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGY.php.html#15">App\Models\Logic\Trade\Pay\YGY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YC.php.html#16">App\Models\Logic\Trade\Pay\YC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XMSK.php.html#19">App\Models\Logic\Trade\Pay\XMSK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#13">App\Models\Logic\Trade\Pay\ThirdParty</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Simple.php.html#20">App\Models\Logic\Trade\Pay\Simple</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZSH.php.html#19">App\Models\Logic\Trade\Pay\SQZSH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZL.php.html#15">App\Models\Logic\Trade\Pay\SQZL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHSX.php.html#12">App\Models\Logic\Trade\Pay\SHSX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SH.php.html#12">App\Models\Logic\Trade\Pay\SH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFFY.php.html#17">App\Models\Logic\Trade\Pay\SFFY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CNPC.php.html#14">App\Models\Logic\Trade\Pay\CNPC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAIC.php.html#18">App\Models\Logic\Trade\Pay\SAIC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Main.php.html#12">App\Models\Logic\Trade\Pay\Main</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MY.php.html#20">App\Models\Logic\Trade\Pay\MY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#20">App\Models\Logic\Trade\Pay\KY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KL.php.html#13">App\Models\Logic\Trade\Pay\KL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JHCX.php.html#18">App\Models\Logic\Trade\Pay\JHCX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSY.php.html#16">App\Models\Logic\Trade\Pay\HSY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HBKJ.php.html#14">App\Models\Logic\Trade\Pay\HBKJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GS.php.html#17">App\Models\Logic\Trade\Pay\GS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GDQP.php.html#20">App\Models\Logic\Trade\Pay\GDQP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FY.php.html#20">App\Models\Logic\Trade\Pay\FY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DT.php.html#22">App\Models\Logic\Trade\Pay\DT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DH.php.html#13">App\Models\Logic\Trade\Pay\DH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZWL.php.html#18">App\Models\Logic\Trade\Pay\ZWL</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#23">App\Models\Logic\Trade\Pay\AD</a></td><td class="text-right">1892</td></tr>
       <tr><td><a href="KY.php.html#20">App\Models\Logic\Trade\Pay\KY</a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="Simple.php.html#20">App\Models\Logic\Trade\Pay\Simple</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="MY.php.html#20">App\Models\Logic\Trade\Pay\MY</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="FY.php.html#20">App\Models\Logic\Trade\Pay\FY</a></td><td class="text-right">812</td></tr>
       <tr><td><a href="YGY.php.html#15">App\Models\Logic\Trade\Pay\YGY</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="SFFY.php.html#17">App\Models\Logic\Trade\Pay\SFFY</a></td><td class="text-right">600</td></tr>
       <tr><td><a href="DT.php.html#22">App\Models\Logic\Trade\Pay\DT</a></td><td class="text-right">552</td></tr>
       <tr><td><a href="GDQP.php.html#20">App\Models\Logic\Trade\Pay\GDQP</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="ZWL.php.html#18">App\Models\Logic\Trade\Pay\ZWL</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="ZDC.php.html#18">App\Models\Logic\Trade\Pay\ZDC</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="GS.php.html#17">App\Models\Logic\Trade\Pay\GS</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="SH.php.html#12">App\Models\Logic\Trade\Pay\SH</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="HSY.php.html#16">App\Models\Logic\Trade\Pay\HSY</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="YC.php.html#16">App\Models\Logic\Trade\Pay\YC</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="XMSK.php.html#19">App\Models\Logic\Trade\Pay\XMSK</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="SQZL.php.html#15">App\Models\Logic\Trade\Pay\SQZL</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="SHSX.php.html#12">App\Models\Logic\Trade\Pay\SHSX</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="SG.php.html#20">App\Models\Logic\Trade\Pay\SG</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="SAIC.php.html#18">App\Models\Logic\Trade\Pay\SAIC</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="JHCX.php.html#18">App\Models\Logic\Trade\Pay\JHCX</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="SQZSH.php.html#19">App\Models\Logic\Trade\Pay\SQZSH</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="ThirdParty.php.html#13">App\Models\Logic\Trade\Pay\ThirdParty</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="DH.php.html#13">App\Models\Logic\Trade\Pay\DH</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Main.php.html#12">App\Models\Logic\Trade\Pay\Main</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="CNPC.php.html#14">App\Models\Logic\Trade\Pay\CNPC</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#12">App\Models\Logic\Trade\Pay\ZHUOYIQ</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#12">App\Models\Logic\Trade\Pay\ZHUOYIY</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="HBKJ.php.html#14">App\Models\Logic\Trade\Pay\HBKJ</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="KL.php.html#13">App\Models\Logic\Trade\Pay\KL</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#32"><abbr title="App\Models\Logic\Trade\Pay\AD::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Simple.php.html#100"><abbr title="App\Models\Logic\Trade\Pay\Simple::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFFY.php.html#26"><abbr title="App\Models\Logic\Trade\Pay\SFFY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFFY.php.html#58"><abbr title="App\Models\Logic\Trade\Pay\SFFY::payByNew">payByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFFY.php.html#96"><abbr title="App\Models\Logic\Trade\Pay\SFFY::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SG.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\SG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SH.php.html#21"><abbr title="App\Models\Logic\Trade\Pay\SH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHSX.php.html#21"><abbr title="App\Models\Logic\Trade\Pay\SHSX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZL.php.html#27"><abbr title="App\Models\Logic\Trade\Pay\SQZL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZSH.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\SQZSH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Simple.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\Simple::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Simple.php.html#64"><abbr title="App\Models\Logic\Trade\Pay\Simple::payByNew">payByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Simple.php.html#133"><abbr title="App\Models\Logic\Trade\Pay\Simple::getPayResult">getPayResult</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Main.php.html#68"><abbr title="App\Models\Logic\Trade\Pay\Main::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#17"><abbr title="App\Models\Logic\Trade\Pay\ThirdParty::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#23"><abbr title="App\Models\Logic\Trade\Pay\ThirdParty::checkOrder">checkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#38"><abbr title="App\Models\Logic\Trade\Pay\ThirdParty::checkOrderStatusByCenterForVerification">checkOrderStatusByCenterForVerification</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XMSK.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\XMSK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YC.php.html#28"><abbr title="App\Models\Logic\Trade\Pay\YC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGY.php.html#24"><abbr title="App\Models\Logic\Trade\Pay\YGY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGY.php.html#58"><abbr title="App\Models\Logic\Trade\Pay\YGY::payByNew">payByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGY.php.html#95"><abbr title="App\Models\Logic\Trade\Pay\YGY::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZDC.php.html#27"><abbr title="App\Models\Logic\Trade\Pay\ZDC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#20"><abbr title="App\Models\Logic\Trade\Pay\ZHUOYIQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#20"><abbr title="App\Models\Logic\Trade\Pay\ZHUOYIY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAIC.php.html#30"><abbr title="App\Models\Logic\Trade\Pay\SAIC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Main.php.html#50"><abbr title="App\Models\Logic\Trade\Pay\Main::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AD.php.html#128"><abbr title="App\Models\Logic\Trade\Pay\AD::payByNew">payByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GDQP.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\GDQP::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AD.php.html#165"><abbr title="App\Models\Logic\Trade\Pay\AD::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AD.php.html#196"><abbr title="App\Models\Logic\Trade\Pay\AD::getPayResult">getPayResult</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AD.php.html#253"><abbr title="App\Models\Logic\Trade\Pay\AD::getPushOrderType">getPushOrderType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CNPC.php.html#23"><abbr title="App\Models\Logic\Trade\Pay\CNPC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DH.php.html#22"><abbr title="App\Models\Logic\Trade\Pay\DH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DT.php.html#34"><abbr title="App\Models\Logic\Trade\Pay\DT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FY.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\FY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FY.php.html#73"><abbr title="App\Models\Logic\Trade\Pay\FY::payByNew">payByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FY.php.html#101"><abbr title="App\Models\Logic\Trade\Pay\FY::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FY.php.html#134"><abbr title="App\Models\Logic\Trade\Pay\FY::getPayResult">getPayResult</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GS.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\GS::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MY.php.html#154"><abbr title="App\Models\Logic\Trade\Pay\MY::getPayResult">getPayResult</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HBKJ.php.html#23"><abbr title="App\Models\Logic\Trade\Pay\HBKJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSY.php.html#28"><abbr title="App\Models\Logic\Trade\Pay\HSY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JHCX.php.html#30"><abbr title="App\Models\Logic\Trade\Pay\JHCX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KL.php.html#22"><abbr title="App\Models\Logic\Trade\Pay\KL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\KY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#64"><abbr title="App\Models\Logic\Trade\Pay\KY::payByNew">payByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#103"><abbr title="App\Models\Logic\Trade\Pay\KY::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#139"><abbr title="App\Models\Logic\Trade\Pay\KY::getPayResult">getPayResult</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MY.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\MY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MY.php.html#78"><abbr title="App\Models\Logic\Trade\Pay\MY::payByNew">payByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MY.php.html#121"><abbr title="App\Models\Logic\Trade\Pay\MY::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZWL.php.html#28"><abbr title="App\Models\Logic\Trade\Pay\ZWL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DT.php.html#34"><abbr title="App\Models\Logic\Trade\Pay\DT::handle">handle</abbr></a></td><td class="text-right">552</td></tr>
       <tr><td><a href="GDQP.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\GDQP::handle">handle</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="ZWL.php.html#28"><abbr title="App\Models\Logic\Trade\Pay\ZWL::handle">handle</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="ZDC.php.html#27"><abbr title="App\Models\Logic\Trade\Pay\ZDC::handle">handle</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="GS.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\GS::handle">handle</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="SH.php.html#21"><abbr title="App\Models\Logic\Trade\Pay\SH::handle">handle</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="HSY.php.html#28"><abbr title="App\Models\Logic\Trade\Pay\HSY::handle">handle</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="AD.php.html#32"><abbr title="App\Models\Logic\Trade\Pay\AD::handle">handle</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="SG.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\SG::handle">handle</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="SAIC.php.html#30"><abbr title="App\Models\Logic\Trade\Pay\SAIC::handle">handle</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="SHSX.php.html#21"><abbr title="App\Models\Logic\Trade\Pay\SHSX::handle">handle</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="SQZL.php.html#27"><abbr title="App\Models\Logic\Trade\Pay\SQZL::handle">handle</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="YC.php.html#28"><abbr title="App\Models\Logic\Trade\Pay\YC::handle">handle</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="XMSK.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\XMSK::handle">handle</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="JHCX.php.html#30"><abbr title="App\Models\Logic\Trade\Pay\JHCX::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="SQZSH.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\SQZSH::handle">handle</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="KY.php.html#103"><abbr title="App\Models\Logic\Trade\Pay\KY::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Simple.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\Simple::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="MY.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\MY::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="FY.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\FY::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="KY.php.html#29"><abbr title="App\Models\Logic\Trade\Pay\KY::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="YGY.php.html#24"><abbr title="App\Models\Logic\Trade\Pay\YGY::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="DH.php.html#22"><abbr title="App\Models\Logic\Trade\Pay\DH::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="KY.php.html#64"><abbr title="App\Models\Logic\Trade\Pay\KY::payByNew">payByNew</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="SFFY.php.html#96"><abbr title="App\Models\Logic\Trade\Pay\SFFY::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Simple.php.html#100"><abbr title="App\Models\Logic\Trade\Pay\Simple::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Simple.php.html#64"><abbr title="App\Models\Logic\Trade\Pay\Simple::payByNew">payByNew</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="YGY.php.html#58"><abbr title="App\Models\Logic\Trade\Pay\YGY::payByNew">payByNew</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="YGY.php.html#95"><abbr title="App\Models\Logic\Trade\Pay\YGY::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="AD.php.html#165"><abbr title="App\Models\Logic\Trade\Pay\AD::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="SFFY.php.html#58"><abbr title="App\Models\Logic\Trade\Pay\SFFY::payByNew">payByNew</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="MY.php.html#121"><abbr title="App\Models\Logic\Trade\Pay\MY::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="AD.php.html#128"><abbr title="App\Models\Logic\Trade\Pay\AD::payByNew">payByNew</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="MY.php.html#78"><abbr title="App\Models\Logic\Trade\Pay\MY::payByNew">payByNew</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="FY.php.html#101"><abbr title="App\Models\Logic\Trade\Pay\FY::cancelByNew">cancelByNew</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="SFFY.php.html#26"><abbr title="App\Models\Logic\Trade\Pay\SFFY::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="CNPC.php.html#23"><abbr title="App\Models\Logic\Trade\Pay\CNPC::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Simple.php.html#133"><abbr title="App\Models\Logic\Trade\Pay\Simple::getPayResult">getPayResult</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AD.php.html#196"><abbr title="App\Models\Logic\Trade\Pay\AD::getPayResult">getPayResult</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Main.php.html#68"><abbr title="App\Models\Logic\Trade\Pay\Main::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="MY.php.html#154"><abbr title="App\Models\Logic\Trade\Pay\MY::getPayResult">getPayResult</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="KY.php.html#139"><abbr title="App\Models\Logic\Trade\Pay\KY::getPayResult">getPayResult</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="FY.php.html#134"><abbr title="App\Models\Logic\Trade\Pay\FY::getPayResult">getPayResult</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="HBKJ.php.html#23"><abbr title="App\Models\Logic\Trade\Pay\HBKJ::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="FY.php.html#73"><abbr title="App\Models\Logic\Trade\Pay\FY::payByNew">payByNew</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AD.php.html#253"><abbr title="App\Models\Logic\Trade\Pay\AD::getPushOrderType">getPushOrderType</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ThirdParty.php.html#38"><abbr title="App\Models\Logic\Trade\Pay\ThirdParty::checkOrderStatusByCenterForVerification">checkOrderStatusByCenterForVerification</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#20"><abbr title="App\Models\Logic\Trade\Pay\ZHUOYIY::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#20"><abbr title="App\Models\Logic\Trade\Pay\ZHUOYIQ::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="KL.php.html#22"><abbr title="App\Models\Logic\Trade\Pay\KL::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ThirdParty.php.html#23"><abbr title="App\Models\Logic\Trade\Pay\ThirdParty::checkOrder">checkOrder</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Main.php.html#50"><abbr title="App\Models\Logic\Trade\Pay\Main::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:21:05 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([30,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([53,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,43,"<a href=\"AD.php.html#23\">App\\Models\\Logic\\Trade\\Pay\\AD<\/a>"],[0,7,"<a href=\"CNPC.php.html#14\">App\\Models\\Logic\\Trade\\Pay\\CNPC<\/a>"],[0,9,"<a href=\"DH.php.html#13\">App\\Models\\Logic\\Trade\\Pay\\DH<\/a>"],[0,23,"<a href=\"DT.php.html#22\">App\\Models\\Logic\\Trade\\Pay\\DT<\/a>"],[0,28,"<a href=\"FY.php.html#20\">App\\Models\\Logic\\Trade\\Pay\\FY<\/a>"],[0,18,"<a href=\"GDQP.php.html#20\">App\\Models\\Logic\\Trade\\Pay\\GDQP<\/a>"],[0,17,"<a href=\"GS.php.html#17\">App\\Models\\Logic\\Trade\\Pay\\GS<\/a>"],[0,5,"<a href=\"HBKJ.php.html#14\">App\\Models\\Logic\\Trade\\Pay\\HBKJ<\/a>"],[0,16,"<a href=\"HSY.php.html#16\">App\\Models\\Logic\\Trade\\Pay\\HSY<\/a>"],[0,14,"<a href=\"JHCX.php.html#18\">App\\Models\\Logic\\Trade\\Pay\\JHCX<\/a>"],[0,4,"<a href=\"KL.php.html#13\">App\\Models\\Logic\\Trade\\Pay\\KL<\/a>"],[0,33,"<a href=\"KY.php.html#20\">App\\Models\\Logic\\Trade\\Pay\\KY<\/a>"],[0,31,"<a href=\"MY.php.html#20\">App\\Models\\Logic\\Trade\\Pay\\MY<\/a>"],[0,8,"<a href=\"Main.php.html#12\">App\\Models\\Logic\\Trade\\Pay\\Main<\/a>"],[0,15,"<a href=\"SAIC.php.html#18\">App\\Models\\Logic\\Trade\\Pay\\SAIC<\/a>"],[0,24,"<a href=\"SFFY.php.html#17\">App\\Models\\Logic\\Trade\\Pay\\SFFY<\/a>"],[0,15,"<a href=\"SG.php.html#20\">App\\Models\\Logic\\Trade\\Pay\\SG<\/a>"],[0,16,"<a href=\"SH.php.html#12\">App\\Models\\Logic\\Trade\\Pay\\SH<\/a>"],[0,15,"<a href=\"SHSX.php.html#12\">App\\Models\\Logic\\Trade\\Pay\\SHSX<\/a>"],[0,15,"<a href=\"SQZL.php.html#15\">App\\Models\\Logic\\Trade\\Pay\\SQZL<\/a>"],[0,13,"<a href=\"SQZSH.php.html#19\">App\\Models\\Logic\\Trade\\Pay\\SQZSH<\/a>"],[0,31,"<a href=\"Simple.php.html#20\">App\\Models\\Logic\\Trade\\Pay\\Simple<\/a>"],[0,9,"<a href=\"ThirdParty.php.html#13\">App\\Models\\Logic\\Trade\\Pay\\ThirdParty<\/a>"],[0,15,"<a href=\"XMSK.php.html#19\">App\\Models\\Logic\\Trade\\Pay\\XMSK<\/a>"],[0,15,"<a href=\"YC.php.html#16\">App\\Models\\Logic\\Trade\\Pay\\YC<\/a>"],[0,25,"<a href=\"YGY.php.html#15\">App\\Models\\Logic\\Trade\\Pay\\YGY<\/a>"],[0,17,"<a href=\"ZDC.php.html#18\">App\\Models\\Logic\\Trade\\Pay\\ZDC<\/a>"],[0,5,"<a href=\"ZHUOYIQ.php.html#12\">App\\Models\\Logic\\Trade\\Pay\\ZHUOYIQ<\/a>"],[0,5,"<a href=\"ZHUOYIY.php.html#12\">App\\Models\\Logic\\Trade\\Pay\\ZHUOYIY<\/a>"],[0,17,"<a href=\"ZWL.php.html#18\">App\\Models\\Logic\\Trade\\Pay\\ZWL<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,16,"<a href=\"AD.php.html#32\">App\\Models\\Logic\\Trade\\Pay\\AD::handle<\/a>"],[0,8,"<a href=\"AD.php.html#128\">App\\Models\\Logic\\Trade\\Pay\\AD::payByNew<\/a>"],[0,8,"<a href=\"AD.php.html#165\">App\\Models\\Logic\\Trade\\Pay\\AD::cancelByNew<\/a>"],[0,6,"<a href=\"AD.php.html#196\">App\\Models\\Logic\\Trade\\Pay\\AD::getPayResult<\/a>"],[0,5,"<a href=\"AD.php.html#253\">App\\Models\\Logic\\Trade\\Pay\\AD::getPushOrderType<\/a>"],[0,7,"<a href=\"CNPC.php.html#23\">App\\Models\\Logic\\Trade\\Pay\\CNPC::handle<\/a>"],[0,9,"<a href=\"DH.php.html#22\">App\\Models\\Logic\\Trade\\Pay\\DH::handle<\/a>"],[0,23,"<a href=\"DT.php.html#34\">App\\Models\\Logic\\Trade\\Pay\\DT::handle<\/a>"],[0,9,"<a href=\"FY.php.html#29\">App\\Models\\Logic\\Trade\\Pay\\FY::handle<\/a>"],[0,5,"<a href=\"FY.php.html#73\">App\\Models\\Logic\\Trade\\Pay\\FY::payByNew<\/a>"],[0,8,"<a href=\"FY.php.html#101\">App\\Models\\Logic\\Trade\\Pay\\FY::cancelByNew<\/a>"],[0,6,"<a href=\"FY.php.html#134\">App\\Models\\Logic\\Trade\\Pay\\FY::getPayResult<\/a>"],[0,18,"<a href=\"GDQP.php.html#29\">App\\Models\\Logic\\Trade\\Pay\\GDQP::handle<\/a>"],[0,17,"<a href=\"GS.php.html#29\">App\\Models\\Logic\\Trade\\Pay\\GS::handle<\/a>"],[0,5,"<a href=\"HBKJ.php.html#23\">App\\Models\\Logic\\Trade\\Pay\\HBKJ::handle<\/a>"],[0,16,"<a href=\"HSY.php.html#28\">App\\Models\\Logic\\Trade\\Pay\\HSY::handle<\/a>"],[0,14,"<a href=\"JHCX.php.html#30\">App\\Models\\Logic\\Trade\\Pay\\JHCX::handle<\/a>"],[0,4,"<a href=\"KL.php.html#22\">App\\Models\\Logic\\Trade\\Pay\\KL::handle<\/a>"],[0,9,"<a href=\"KY.php.html#29\">App\\Models\\Logic\\Trade\\Pay\\KY::handle<\/a>"],[0,9,"<a href=\"KY.php.html#64\">App\\Models\\Logic\\Trade\\Pay\\KY::payByNew<\/a>"],[0,9,"<a href=\"KY.php.html#103\">App\\Models\\Logic\\Trade\\Pay\\KY::cancelByNew<\/a>"],[0,6,"<a href=\"KY.php.html#139\">App\\Models\\Logic\\Trade\\Pay\\KY::getPayResult<\/a>"],[0,9,"<a href=\"MY.php.html#29\">App\\Models\\Logic\\Trade\\Pay\\MY::handle<\/a>"],[0,8,"<a href=\"MY.php.html#78\">App\\Models\\Logic\\Trade\\Pay\\MY::payByNew<\/a>"],[0,8,"<a href=\"MY.php.html#121\">App\\Models\\Logic\\Trade\\Pay\\MY::cancelByNew<\/a>"],[0,6,"<a href=\"MY.php.html#154\">App\\Models\\Logic\\Trade\\Pay\\MY::getPayResult<\/a>"],[0,2,"<a href=\"Main.php.html#50\">App\\Models\\Logic\\Trade\\Pay\\Main::__construct<\/a>"],[0,6,"<a href=\"Main.php.html#68\">App\\Models\\Logic\\Trade\\Pay\\Main::handle<\/a>"],[0,15,"<a href=\"SAIC.php.html#30\">App\\Models\\Logic\\Trade\\Pay\\SAIC::handle<\/a>"],[0,8,"<a href=\"SFFY.php.html#26\">App\\Models\\Logic\\Trade\\Pay\\SFFY::handle<\/a>"],[0,8,"<a href=\"SFFY.php.html#58\">App\\Models\\Logic\\Trade\\Pay\\SFFY::payByNew<\/a>"],[0,8,"<a href=\"SFFY.php.html#96\">App\\Models\\Logic\\Trade\\Pay\\SFFY::cancelByNew<\/a>"],[0,15,"<a href=\"SG.php.html#29\">App\\Models\\Logic\\Trade\\Pay\\SG::handle<\/a>"],[0,16,"<a href=\"SH.php.html#21\">App\\Models\\Logic\\Trade\\Pay\\SH::handle<\/a>"],[0,15,"<a href=\"SHSX.php.html#21\">App\\Models\\Logic\\Trade\\Pay\\SHSX::handle<\/a>"],[0,15,"<a href=\"SQZL.php.html#27\">App\\Models\\Logic\\Trade\\Pay\\SQZL::handle<\/a>"],[0,13,"<a href=\"SQZSH.php.html#29\">App\\Models\\Logic\\Trade\\Pay\\SQZSH::handle<\/a>"],[0,9,"<a href=\"Simple.php.html#29\">App\\Models\\Logic\\Trade\\Pay\\Simple::handle<\/a>"],[0,8,"<a href=\"Simple.php.html#64\">App\\Models\\Logic\\Trade\\Pay\\Simple::payByNew<\/a>"],[0,8,"<a href=\"Simple.php.html#100\">App\\Models\\Logic\\Trade\\Pay\\Simple::cancelByNew<\/a>"],[0,6,"<a href=\"Simple.php.html#133\">App\\Models\\Logic\\Trade\\Pay\\Simple::getPayResult<\/a>"],[0,1,"<a href=\"ThirdParty.php.html#17\">App\\Models\\Logic\\Trade\\Pay\\ThirdParty::__construct<\/a>"],[0,3,"<a href=\"ThirdParty.php.html#23\">App\\Models\\Logic\\Trade\\Pay\\ThirdParty::checkOrder<\/a>"],[0,5,"<a href=\"ThirdParty.php.html#38\">App\\Models\\Logic\\Trade\\Pay\\ThirdParty::checkOrderStatusByCenterForVerification<\/a>"],[0,15,"<a href=\"XMSK.php.html#29\">App\\Models\\Logic\\Trade\\Pay\\XMSK::handle<\/a>"],[0,15,"<a href=\"YC.php.html#28\">App\\Models\\Logic\\Trade\\Pay\\YC::handle<\/a>"],[0,9,"<a href=\"YGY.php.html#24\">App\\Models\\Logic\\Trade\\Pay\\YGY::handle<\/a>"],[0,8,"<a href=\"YGY.php.html#58\">App\\Models\\Logic\\Trade\\Pay\\YGY::payByNew<\/a>"],[0,8,"<a href=\"YGY.php.html#95\">App\\Models\\Logic\\Trade\\Pay\\YGY::cancelByNew<\/a>"],[0,17,"<a href=\"ZDC.php.html#27\">App\\Models\\Logic\\Trade\\Pay\\ZDC::handle<\/a>"],[0,5,"<a href=\"ZHUOYIQ.php.html#20\">App\\Models\\Logic\\Trade\\Pay\\ZHUOYIQ::handle<\/a>"],[0,5,"<a href=\"ZHUOYIY.php.html#20\">App\\Models\\Logic\\Trade\\Pay\\ZHUOYIY::handle<\/a>"],[0,17,"<a href=\"ZWL.php.html#28\">App\\Models\\Logic\\Trade\\Pay\\ZWL::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
