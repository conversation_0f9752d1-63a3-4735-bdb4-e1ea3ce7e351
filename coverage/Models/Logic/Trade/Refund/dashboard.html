<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Logic/Trade/Refund</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Logic</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Trade</a></li>
         <li class="breadcrumb-item"><a href="index.html">Refund</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CY.php.html#11">App\Models\Logic\Trade\Refund\CY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SC.php.html#11">App\Models\Logic\Trade\Refund\SC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHYK.php.html#11">App\Models\Logic\Trade\Refund\ZHYK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZDC.php.html#11">App\Models\Logic\Trade\Refund\ZDC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YUNDATONG.php.html#11">App\Models\Logic\Trade\Refund\YUNDATONG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XYN.php.html#10">App\Models\Logic\Trade\Refund\XYN</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XY.php.html#12">App\Models\Logic\Trade\Refund\XY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WJY.php.html#15">App\Models\Logic\Trade\Refund\WJY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TBJX.php.html#11">App\Models\Logic\Trade\Refund\TBJX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZSH.php.html#14">App\Models\Logic\Trade\Refund\SQZSH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZL.php.html#10">App\Models\Logic\Trade\Refund\SQZL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SP.php.html#11">App\Models\Logic\Trade\Refund\SP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SG.php.html#14">App\Models\Logic\Trade\Refund\SG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QK.php.html#11">App\Models\Logic\Trade\Refund\QK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CYLNG.php.html#11">App\Models\Logic\Trade\Refund\CYLNG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MTLSY.php.html#10">App\Models\Logic\Trade\Refund\MTLSY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTX.php.html#11">App\Models\Logic\Trade\Refund\JTX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JT.php.html#11">App\Models\Logic\Trade\Refund\JT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JQ.php.html#11">App\Models\Logic\Trade\Refund\JQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JH.php.html#11">App\Models\Logic\Trade\Refund\JH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HYT.php.html#11">App\Models\Logic\Trade\Refund\HYT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSY.php.html#11">App\Models\Logic\Trade\Refund\HSY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GDQP.php.html#12">App\Models\Logic\Trade\Refund\GDQP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GB.php.html#15">App\Models\Logic\Trade\Refund\GB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GAODENG.php.html#11">App\Models\Logic\Trade\Refund\GAODENG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DIANDI.php.html#11">App\Models\Logic\Trade\Refund\DIANDI</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CZB.php.html#11">App\Models\Logic\Trade\Refund\CZB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZY.php.html#11">App\Models\Logic\Trade\Refund\ZY</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="JQ.php.html#11">App\Models\Logic\Trade\Refund\JQ</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="ZDC.php.html#11">App\Models\Logic\Trade\Refund\ZDC</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="SC.php.html#11">App\Models\Logic\Trade\Refund\SC</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="XYN.php.html#10">App\Models\Logic\Trade\Refund\XYN</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SQZSH.php.html#14">App\Models\Logic\Trade\Refund\SQZSH</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="YUNDATONG.php.html#11">App\Models\Logic\Trade\Refund\YUNDATONG</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TBJX.php.html#11">App\Models\Logic\Trade\Refund\TBJX</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="SQZL.php.html#10">App\Models\Logic\Trade\Refund\SQZL</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ZHYK.php.html#11">App\Models\Logic\Trade\Refund\ZHYK</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SG.php.html#14">App\Models\Logic\Trade\Refund\SG</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MTLSY.php.html#10">App\Models\Logic\Trade\Refund\MTLSY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="GDQP.php.html#12">App\Models\Logic\Trade\Refund\GDQP</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="GB.php.html#15">App\Models\Logic\Trade\Refund\GB</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CZB.php.html#11">App\Models\Logic\Trade\Refund\CZB</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JT.php.html#11">App\Models\Logic\Trade\Refund\JT</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JTX.php.html#11">App\Models\Logic\Trade\Refund\JTX</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CYLNG.php.html#11">App\Models\Logic\Trade\Refund\CYLNG</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QK.php.html#11">App\Models\Logic\Trade\Refund\QK</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JH.php.html#11">App\Models\Logic\Trade\Refund\JH</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="HYT.php.html#11">App\Models\Logic\Trade\Refund\HYT</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SP.php.html#11">App\Models\Logic\Trade\Refund\SP</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="HSY.php.html#11">App\Models\Logic\Trade\Refund\HSY</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WJY.php.html#15">App\Models\Logic\Trade\Refund\WJY</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="XY.php.html#12">App\Models\Logic\Trade\Refund\XY</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="GAODENG.php.html#11">App\Models\Logic\Trade\Refund\GAODENG</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DIANDI.php.html#11">App\Models\Logic\Trade\Refund\DIANDI</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CY.php.html#11">App\Models\Logic\Trade\Refund\CY</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CY.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\CY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SC.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\SC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHYK.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\ZHYK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZDC.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\ZDC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YUNDATONG.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\YUNDATONG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XYN.php.html#18"><abbr title="App\Models\Logic\Trade\Refund\XYN::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XY.php.html#20"><abbr title="App\Models\Logic\Trade\Refund\XY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WJY.php.html#23"><abbr title="App\Models\Logic\Trade\Refund\WJY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TBJX.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\TBJX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZSH.php.html#22"><abbr title="App\Models\Logic\Trade\Refund\SQZSH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZL.php.html#18"><abbr title="App\Models\Logic\Trade\Refund\SQZL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SP.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\SP::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SG.php.html#23"><abbr title="App\Models\Logic\Trade\Refund\SG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QK.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\QK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CYLNG.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\CYLNG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MTLSY.php.html#18"><abbr title="App\Models\Logic\Trade\Refund\MTLSY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTX.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\JTX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JT.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\JT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JQ.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\JQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JH.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\JH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HYT.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\HYT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSY.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\HSY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GDQP.php.html#17"><abbr title="App\Models\Logic\Trade\Refund\GDQP::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GB.php.html#23"><abbr title="App\Models\Logic\Trade\Refund\GB::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GAODENG.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\GAODENG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DIANDI.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\DIANDI::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CZB.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\CZB::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZY.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\ZY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="JQ.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\JQ::handle">handle</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="ZDC.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\ZDC::handle">handle</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="SC.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\SC::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="XYN.php.html#18"><abbr title="App\Models\Logic\Trade\Refund\XYN::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SQZSH.php.html#22"><abbr title="App\Models\Logic\Trade\Refund\SQZSH::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="YUNDATONG.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\YUNDATONG::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TBJX.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\TBJX::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="SQZL.php.html#18"><abbr title="App\Models\Logic\Trade\Refund\SQZL::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ZHYK.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\ZHYK::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SG.php.html#23"><abbr title="App\Models\Logic\Trade\Refund\SG::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MTLSY.php.html#18"><abbr title="App\Models\Logic\Trade\Refund\MTLSY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="GDQP.php.html#17"><abbr title="App\Models\Logic\Trade\Refund\GDQP::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="GB.php.html#23"><abbr title="App\Models\Logic\Trade\Refund\GB::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CZB.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\CZB::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JT.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\JT::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JTX.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\JTX::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CYLNG.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\CYLNG::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QK.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\QK::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JH.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\JH::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="HYT.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\HYT::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SP.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\SP::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="HSY.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\HSY::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WJY.php.html#23"><abbr title="App\Models\Logic\Trade\Refund\WJY::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="XY.php.html#20"><abbr title="App\Models\Logic\Trade\Refund\XY::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="GAODENG.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\GAODENG::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DIANDI.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\DIANDI::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CY.php.html#19"><abbr title="App\Models\Logic\Trade\Refund\CY::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:21:05 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([28,0,0,0,0,0,0,0,0,0,0,1], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([28,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"CY.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\CY<\/a>"],[0,2,"<a href=\"CYLNG.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\CYLNG<\/a>"],[0,3,"<a href=\"CZB.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\CZB<\/a>"],[0,2,"<a href=\"DIANDI.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\DIANDI<\/a>"],[0,2,"<a href=\"GAODENG.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\GAODENG<\/a>"],[0,3,"<a href=\"GB.php.html#15\">App\\Models\\Logic\\Trade\\Refund\\GB<\/a>"],[0,3,"<a href=\"GDQP.php.html#12\">App\\Models\\Logic\\Trade\\Refund\\GDQP<\/a>"],[0,2,"<a href=\"HSY.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\HSY<\/a>"],[0,2,"<a href=\"HYT.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\HYT<\/a>"],[0,2,"<a href=\"JH.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\JH<\/a>"],[0,18,"<a href=\"JQ.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\JQ<\/a>"],[0,2,"<a href=\"JT.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\JT<\/a>"],[0,2,"<a href=\"JTX.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\JTX<\/a>"],[0,3,"<a href=\"MTLSY.php.html#10\">App\\Models\\Logic\\Trade\\Refund\\MTLSY<\/a>"],[0,2,"<a href=\"QK.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\QK<\/a>"],[0,8,"<a href=\"SC.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\SC<\/a>"],[0,3,"<a href=\"SG.php.html#14\">App\\Models\\Logic\\Trade\\Refund\\SG<\/a>"],[0,2,"<a href=\"SP.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\SP<\/a>"],[0,4,"<a href=\"SQZL.php.html#10\">App\\Models\\Logic\\Trade\\Refund\\SQZL<\/a>"],[0,5,"<a href=\"SQZSH.php.html#14\">App\\Models\\Logic\\Trade\\Refund\\SQZSH<\/a>"],[0,4,"<a href=\"TBJX.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\TBJX<\/a>"],[100,0,"<a href=\"ThirdParty.php.html#9\">App\\Models\\Logic\\Trade\\Refund\\ThirdParty<\/a>"],[0,2,"<a href=\"WJY.php.html#15\">App\\Models\\Logic\\Trade\\Refund\\WJY<\/a>"],[0,2,"<a href=\"XY.php.html#12\">App\\Models\\Logic\\Trade\\Refund\\XY<\/a>"],[0,5,"<a href=\"XYN.php.html#10\">App\\Models\\Logic\\Trade\\Refund\\XYN<\/a>"],[0,4,"<a href=\"YUNDATONG.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\YUNDATONG<\/a>"],[0,18,"<a href=\"ZDC.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\ZDC<\/a>"],[0,3,"<a href=\"ZHYK.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\ZHYK<\/a>"],[0,1,"<a href=\"ZY.php.html#11\">App\\Models\\Logic\\Trade\\Refund\\ZY<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"CY.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\CY::handle<\/a>"],[0,2,"<a href=\"CYLNG.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\CYLNG::handle<\/a>"],[0,3,"<a href=\"CZB.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\CZB::handle<\/a>"],[0,2,"<a href=\"DIANDI.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\DIANDI::handle<\/a>"],[0,2,"<a href=\"GAODENG.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\GAODENG::handle<\/a>"],[0,3,"<a href=\"GB.php.html#23\">App\\Models\\Logic\\Trade\\Refund\\GB::handle<\/a>"],[0,3,"<a href=\"GDQP.php.html#17\">App\\Models\\Logic\\Trade\\Refund\\GDQP::handle<\/a>"],[0,2,"<a href=\"HSY.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\HSY::handle<\/a>"],[0,2,"<a href=\"HYT.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\HYT::handle<\/a>"],[0,2,"<a href=\"JH.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\JH::handle<\/a>"],[0,18,"<a href=\"JQ.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\JQ::handle<\/a>"],[0,2,"<a href=\"JT.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\JT::handle<\/a>"],[0,2,"<a href=\"JTX.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\JTX::handle<\/a>"],[0,3,"<a href=\"MTLSY.php.html#18\">App\\Models\\Logic\\Trade\\Refund\\MTLSY::handle<\/a>"],[0,2,"<a href=\"QK.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\QK::handle<\/a>"],[0,8,"<a href=\"SC.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\SC::handle<\/a>"],[0,3,"<a href=\"SG.php.html#23\">App\\Models\\Logic\\Trade\\Refund\\SG::handle<\/a>"],[0,2,"<a href=\"SP.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\SP::handle<\/a>"],[0,4,"<a href=\"SQZL.php.html#18\">App\\Models\\Logic\\Trade\\Refund\\SQZL::handle<\/a>"],[0,5,"<a href=\"SQZSH.php.html#22\">App\\Models\\Logic\\Trade\\Refund\\SQZSH::handle<\/a>"],[0,4,"<a href=\"TBJX.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\TBJX::handle<\/a>"],[0,2,"<a href=\"WJY.php.html#23\">App\\Models\\Logic\\Trade\\Refund\\WJY::handle<\/a>"],[0,2,"<a href=\"XY.php.html#20\">App\\Models\\Logic\\Trade\\Refund\\XY::handle<\/a>"],[0,5,"<a href=\"XYN.php.html#18\">App\\Models\\Logic\\Trade\\Refund\\XYN::handle<\/a>"],[0,4,"<a href=\"YUNDATONG.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\YUNDATONG::handle<\/a>"],[0,18,"<a href=\"ZDC.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\ZDC::handle<\/a>"],[0,3,"<a href=\"ZHYK.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\ZHYK::handle<\/a>"],[0,1,"<a href=\"ZY.php.html#19\">App\\Models\\Logic\\Trade\\Refund\\ZY::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
