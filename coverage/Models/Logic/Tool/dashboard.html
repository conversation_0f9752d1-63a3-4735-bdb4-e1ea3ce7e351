<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Logic/Tool</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Logic</a></li>
         <li class="breadcrumb-item"><a href="index.html">Tool</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="GetBillCheckResult.php.html#14">App\Models\Logic\Tool\GetBillCheckResult</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetBillCheckResult/SFFY.php.html#13">App\Models\Logic\Tool\GetBillCheckResult\SFFY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetBillCheckResult/ThirdParty.php.html#8">App\Models\Logic\Tool\GetBillCheckResult\ThirdParty</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetDriverInfo.php.html#14">App\Models\Logic\Tool\GetDriverInfo</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetDriverInfo/ThirdParty.php.html#8">App\Models\Logic\Tool\GetDriverInfo\ThirdParty</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetDriverInfo/ZEY.php.html#12">App\Models\Logic\Tool\GetDriverInfo\ZEY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PullOilStation.php.html#48">App\Models\Logic\Tool\PullOilStation</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushBill.php.html#17">App\Models\Logic\Tool\PushBill</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushBillPreCheck/SFFY.php.html#7">App\Models\Logic\Tool\PushBillPreCheck\SFFY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushBillPreCheck/ThirdParty.php.html#8">App\Models\Logic\Tool\PushBillPreCheck\ThirdParty</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushOilStation.php.html#13">App\Models\Logic\Tool\PushOilStation</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="PullOilStation.php.html#48">App\Models\Logic\Tool\PullOilStation</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="PushBill.php.html#17">App\Models\Logic\Tool\PushBill</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="GetBillCheckResult.php.html#14">App\Models\Logic\Tool\GetBillCheckResult</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="GetBillCheckResult/SFFY.php.html#13">App\Models\Logic\Tool\GetBillCheckResult\SFFY</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="GetDriverInfo.php.html#14">App\Models\Logic\Tool\GetDriverInfo</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="PushOilStation.php.html#13">App\Models\Logic\Tool\PushOilStation</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="GetDriverInfo/ZEY.php.html#12">App\Models\Logic\Tool\GetDriverInfo\ZEY</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="PushBillPreCheck/SFFY.php.html#7">App\Models\Logic\Tool\PushBillPreCheck\SFFY</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="GetBillCheckResult.php.html#20"><abbr title="App\Models\Logic\Tool\GetBillCheckResult::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PullOilStation.php.html#203"><abbr title="App\Models\Logic\Tool\PullOilStation::checkHasJobMapped">checkHasJobMapped</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushOilStation.php.html#17"><abbr title="App\Models\Logic\Tool\PushOilStation::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushBillPreCheck/ThirdParty.php.html#10"><abbr title="App\Models\Logic\Tool\PushBillPreCheck\ThirdParty::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushBillPreCheck/SFFY.php.html#13"><abbr title="App\Models\Logic\Tool\PushBillPreCheck\SFFY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushBill.php.html#52"><abbr title="App\Models\Logic\Tool\PushBill::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushBill.php.html#35"><abbr title="App\Models\Logic\Tool\PushBill::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PullOilStation.php.html#215"><abbr title="App\Models\Logic\Tool\PullOilStation::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PullOilStation.php.html#194"><abbr title="App\Models\Logic\Tool\PullOilStation::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetBillCheckResult.php.html#32"><abbr title="App\Models\Logic\Tool\GetBillCheckResult::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetDriverInfo/ZEY.php.html#21"><abbr title="App\Models\Logic\Tool\GetDriverInfo\ZEY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetDriverInfo/ThirdParty.php.html#10"><abbr title="App\Models\Logic\Tool\GetDriverInfo\ThirdParty::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetDriverInfo.php.html#32"><abbr title="App\Models\Logic\Tool\GetDriverInfo::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetDriverInfo.php.html#20"><abbr title="App\Models\Logic\Tool\GetDriverInfo::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetBillCheckResult/ThirdParty.php.html#10"><abbr title="App\Models\Logic\Tool\GetBillCheckResult\ThirdParty::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetBillCheckResult/SFFY.php.html#36"><abbr title="App\Models\Logic\Tool\GetBillCheckResult\SFFY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushOilStation.php.html#30"><abbr title="App\Models\Logic\Tool\PushOilStation::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="PushBill.php.html#52"><abbr title="App\Models\Logic\Tool\PushBill::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="GetBillCheckResult/SFFY.php.html#36"><abbr title="App\Models\Logic\Tool\GetBillCheckResult\SFFY::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="PullOilStation.php.html#215"><abbr title="App\Models\Logic\Tool\PullOilStation::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="GetBillCheckResult.php.html#32"><abbr title="App\Models\Logic\Tool\GetBillCheckResult::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="GetDriverInfo.php.html#32"><abbr title="App\Models\Logic\Tool\GetDriverInfo::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="GetDriverInfo/ZEY.php.html#21"><abbr title="App\Models\Logic\Tool\GetDriverInfo\ZEY::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="PushOilStation.php.html#30"><abbr title="App\Models\Logic\Tool\PushOilStation::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="PullOilStation.php.html#194"><abbr title="App\Models\Logic\Tool\PullOilStation::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PushBill.php.html#35"><abbr title="App\Models\Logic\Tool\PushBill::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PushBillPreCheck/SFFY.php.html#13"><abbr title="App\Models\Logic\Tool\PushBillPreCheck\SFFY::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:47:38 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([11,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([17,0,0,0,0,0,0,0,0,0,0,3], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,6,"<a href=\"GetBillCheckResult.php.html#14\">App\\Models\\Logic\\Tool\\GetBillCheckResult<\/a>"],[0,6,"<a href=\"GetBillCheckResult\/SFFY.php.html#13\">App\\Models\\Logic\\Tool\\GetBillCheckResult\\SFFY<\/a>"],[0,2,"<a href=\"GetBillCheckResult\/ThirdParty.php.html#8\">App\\Models\\Logic\\Tool\\GetBillCheckResult\\ThirdParty<\/a>"],[0,5,"<a href=\"GetDriverInfo.php.html#14\">App\\Models\\Logic\\Tool\\GetDriverInfo<\/a>"],[0,2,"<a href=\"GetDriverInfo\/ThirdParty.php.html#8\">App\\Models\\Logic\\Tool\\GetDriverInfo\\ThirdParty<\/a>"],[0,4,"<a href=\"GetDriverInfo\/ZEY.php.html#12\">App\\Models\\Logic\\Tool\\GetDriverInfo\\ZEY<\/a>"],[0,9,"<a href=\"PullOilStation.php.html#48\">App\\Models\\Logic\\Tool\\PullOilStation<\/a>"],[0,9,"<a href=\"PushBill.php.html#17\">App\\Models\\Logic\\Tool\\PushBill<\/a>"],[0,2,"<a href=\"PushBillPreCheck\/SFFY.php.html#7\">App\\Models\\Logic\\Tool\\PushBillPreCheck\\SFFY<\/a>"],[0,2,"<a href=\"PushBillPreCheck\/ThirdParty.php.html#8\">App\\Models\\Logic\\Tool\\PushBillPreCheck\\ThirdParty<\/a>"],[0,5,"<a href=\"PushOilStation.php.html#13\">App\\Models\\Logic\\Tool\\PushOilStation<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"GetBillCheckResult.php.html#20\">App\\Models\\Logic\\Tool\\GetBillCheckResult::__construct<\/a>"],[0,5,"<a href=\"GetBillCheckResult.php.html#32\">App\\Models\\Logic\\Tool\\GetBillCheckResult::handle<\/a>"],[0,6,"<a href=\"GetBillCheckResult\/SFFY.php.html#36\">App\\Models\\Logic\\Tool\\GetBillCheckResult\\SFFY::handle<\/a>"],[0,1,"<a href=\"GetBillCheckResult\/ThirdParty.php.html#10\">App\\Models\\Logic\\Tool\\GetBillCheckResult\\ThirdParty::__construct<\/a>"],[100,1,"<a href=\"GetBillCheckResult\/ThirdParty.php.html#15\">App\\Models\\Logic\\Tool\\GetBillCheckResult\\ThirdParty::handle<\/a>"],[0,1,"<a href=\"GetDriverInfo.php.html#20\">App\\Models\\Logic\\Tool\\GetDriverInfo::__construct<\/a>"],[0,4,"<a href=\"GetDriverInfo.php.html#32\">App\\Models\\Logic\\Tool\\GetDriverInfo::handle<\/a>"],[0,1,"<a href=\"GetDriverInfo\/ThirdParty.php.html#10\">App\\Models\\Logic\\Tool\\GetDriverInfo\\ThirdParty::__construct<\/a>"],[100,1,"<a href=\"GetDriverInfo\/ThirdParty.php.html#15\">App\\Models\\Logic\\Tool\\GetDriverInfo\\ThirdParty::handle<\/a>"],[0,4,"<a href=\"GetDriverInfo\/ZEY.php.html#21\">App\\Models\\Logic\\Tool\\GetDriverInfo\\ZEY::handle<\/a>"],[0,2,"<a href=\"PullOilStation.php.html#194\">App\\Models\\Logic\\Tool\\PullOilStation::__construct<\/a>"],[0,1,"<a href=\"PullOilStation.php.html#203\">App\\Models\\Logic\\Tool\\PullOilStation::checkHasJobMapped<\/a>"],[0,6,"<a href=\"PullOilStation.php.html#215\">App\\Models\\Logic\\Tool\\PullOilStation::handle<\/a>"],[0,2,"<a href=\"PushBill.php.html#35\">App\\Models\\Logic\\Tool\\PushBill::__construct<\/a>"],[0,7,"<a href=\"PushBill.php.html#52\">App\\Models\\Logic\\Tool\\PushBill::handle<\/a>"],[0,2,"<a href=\"PushBillPreCheck\/SFFY.php.html#13\">App\\Models\\Logic\\Tool\\PushBillPreCheck\\SFFY::handle<\/a>"],[0,1,"<a href=\"PushBillPreCheck\/ThirdParty.php.html#10\">App\\Models\\Logic\\Tool\\PushBillPreCheck\\ThirdParty::__construct<\/a>"],[100,1,"<a href=\"PushBillPreCheck\/ThirdParty.php.html#15\">App\\Models\\Logic\\Tool\\PushBillPreCheck\\ThirdParty::handle<\/a>"],[0,1,"<a href=\"PushOilStation.php.html#17\">App\\Models\\Logic\\Tool\\PushOilStation::__construct<\/a>"],[0,4,"<a href=\"PushOilStation.php.html#30\">App\\Models\\Logic\\Tool\\PushOilStation::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
