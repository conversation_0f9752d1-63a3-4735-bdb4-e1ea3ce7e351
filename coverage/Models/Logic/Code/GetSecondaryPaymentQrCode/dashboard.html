<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Logic/Code/GetSecondaryPaymentQrCode</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Logic</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Code</a></li>
         <li class="breadcrumb-item"><a href="index.html">GetSecondaryPaymentQrCode</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CNPC.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\CNPC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SC.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\SC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#15">App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZHUOYIY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#15">App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZHUOYIQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZDC.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZDC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YC.php.html#17">App\Models\Logic\Code\GetSecondaryPaymentQrCode\YC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XMSK.php.html#20">App\Models\Logic\Code\GetSecondaryPaymentQrCode\XMSK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WJY.php.html#13">App\Models\Logic\Code\GetSecondaryPaymentQrCode\WJY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#9">App\Models\Logic\Code\GetSecondaryPaymentQrCode\ThirdParty</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZL.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\SQZL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHSX.php.html#17">App\Models\Logic\Code\GetSecondaryPaymentQrCode\SHSX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SH.php.html#17">App\Models\Logic\Code\GetSecondaryPaymentQrCode\SH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAIC.php.html#14">App\Models\Logic\Code\GetSecondaryPaymentQrCode\SAIC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CY.php.html#14">App\Models\Logic\Code\GetSecondaryPaymentQrCode\CY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTX.php.html#14">App\Models\Logic\Code\GetSecondaryPaymentQrCode\JTX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JT.php.html#14">App\Models\Logic\Code\GetSecondaryPaymentQrCode\JT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JHCX.php.html#17">App\Models\Logic\Code\GetSecondaryPaymentQrCode\JHCX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSY.php.html#18">App\Models\Logic\Code\GetSecondaryPaymentQrCode\HSY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HBKJ.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\HBKJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GS.php.html#22">App\Models\Logic\Code\GetSecondaryPaymentQrCode\GS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GDQP.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\GDQP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DT.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\DT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DH.php.html#14">App\Models\Logic\Code\GetSecondaryPaymentQrCode\DH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CYLNG.php.html#14">App\Models\Logic\Code\GetSecondaryPaymentQrCode\CYLNG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZY.php.html#15">App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZY</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="XMSK.php.html#20">App\Models\Logic\Code\GetSecondaryPaymentQrCode\XMSK</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="GS.php.html#22">App\Models\Logic\Code\GetSecondaryPaymentQrCode\GS</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="JHCX.php.html#17">App\Models\Logic\Code\GetSecondaryPaymentQrCode\JHCX</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="HSY.php.html#18">App\Models\Logic\Code\GetSecondaryPaymentQrCode\HSY</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WJY.php.html#13">App\Models\Logic\Code\GetSecondaryPaymentQrCode\WJY</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ZY.php.html#15">App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZY</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SH.php.html#17">App\Models\Logic\Code\GetSecondaryPaymentQrCode\SH</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="DT.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\DT</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZDC.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZDC</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SHSX.php.html#17">App\Models\Logic\Code\GetSecondaryPaymentQrCode\SHSX</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#15">App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZHUOYIY</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#15">App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZHUOYIQ</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="CNPC.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\CNPC</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="HBKJ.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\HBKJ</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="GDQP.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\GDQP</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="SC.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\SC</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CY.php.html#14">App\Models\Logic\Code\GetSecondaryPaymentQrCode\CY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SQZL.php.html#16">App\Models\Logic\Code\GetSecondaryPaymentQrCode\SQZL</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YC.php.html#17">App\Models\Logic\Code\GetSecondaryPaymentQrCode\YC</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DH.php.html#14">App\Models\Logic\Code\GetSecondaryPaymentQrCode\DH</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CYLNG.php.html#14">App\Models\Logic\Code\GetSecondaryPaymentQrCode\CYLNG</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JTX.php.html#14">App\Models\Logic\Code\GetSecondaryPaymentQrCode\JTX</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JT.php.html#14">App\Models\Logic\Code\GetSecondaryPaymentQrCode\JT</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SAIC.php.html#14">App\Models\Logic\Code\GetSecondaryPaymentQrCode\SAIC</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CNPC.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\CNPC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SC.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\SC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#24"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZHUOYIY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#24"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZHUOYIQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZDC.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZDC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YC.php.html#26"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\YC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XMSK.php.html#29"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\XMSK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WJY.php.html#21"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\WJY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#11"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\ThirdParty::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZL.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\SQZL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHSX.php.html#26"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\SHSX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SH.php.html#26"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\SH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAIC.php.html#23"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\SAIC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CY.php.html#23"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\CY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTX.php.html#23"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\JTX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JT.php.html#23"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\JT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JHCX.php.html#26"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\JHCX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSY.php.html#27"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\HSY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HBKJ.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\HBKJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GS.php.html#31"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\GS::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GDQP.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\GDQP::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DT.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\DT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DH.php.html#23"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\DH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CYLNG.php.html#23"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\CYLNG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZY.php.html#24"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="XMSK.php.html#29"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\XMSK::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="GS.php.html#31"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\GS::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="JHCX.php.html#26"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\JHCX::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="HSY.php.html#27"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\HSY::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WJY.php.html#21"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\WJY::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ZY.php.html#24"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZY::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SH.php.html#26"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\SH::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="DT.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\DT::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZDC.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZDC::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SHSX.php.html#26"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\SHSX::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#24"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZHUOYIY::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#24"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\ZHUOYIQ::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="CNPC.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\CNPC::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="HBKJ.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\HBKJ::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="GDQP.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\GDQP::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="SC.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\SC::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CY.php.html#23"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\CY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SQZL.php.html#25"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\SQZL::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YC.php.html#26"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\YC::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DH.php.html#23"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\DH::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CYLNG.php.html#23"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\CYLNG::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JTX.php.html#23"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\JTX::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JT.php.html#23"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\JT::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SAIC.php.html#23"><abbr title="App\Models\Logic\Code\GetSecondaryPaymentQrCode\SAIC::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:21:05 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([25,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([25,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,4,"<a href=\"CNPC.php.html#16\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\CNPC<\/a>"],[0,3,"<a href=\"CY.php.html#14\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\CY<\/a>"],[0,3,"<a href=\"CYLNG.php.html#14\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\CYLNG<\/a>"],[0,3,"<a href=\"DH.php.html#14\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\DH<\/a>"],[0,5,"<a href=\"DT.php.html#16\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\DT<\/a>"],[0,4,"<a href=\"GDQP.php.html#16\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\GDQP<\/a>"],[0,7,"<a href=\"GS.php.html#22\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\GS<\/a>"],[0,4,"<a href=\"HBKJ.php.html#16\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\HBKJ<\/a>"],[0,6,"<a href=\"HSY.php.html#18\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\HSY<\/a>"],[0,7,"<a href=\"JHCX.php.html#17\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\JHCX<\/a>"],[0,2,"<a href=\"JT.php.html#14\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\JT<\/a>"],[0,2,"<a href=\"JTX.php.html#14\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\JTX<\/a>"],[0,2,"<a href=\"SAIC.php.html#14\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\SAIC<\/a>"],[0,3,"<a href=\"SC.php.html#16\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\SC<\/a>"],[0,5,"<a href=\"SH.php.html#17\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\SH<\/a>"],[0,5,"<a href=\"SHSX.php.html#17\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\SHSX<\/a>"],[0,3,"<a href=\"SQZL.php.html#16\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\SQZL<\/a>"],[0,1,"<a href=\"ThirdParty.php.html#9\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\ThirdParty<\/a>"],[0,6,"<a href=\"WJY.php.html#13\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\WJY<\/a>"],[0,9,"<a href=\"XMSK.php.html#20\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\XMSK<\/a>"],[0,3,"<a href=\"YC.php.html#17\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\YC<\/a>"],[0,5,"<a href=\"ZDC.php.html#16\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\ZDC<\/a>"],[0,4,"<a href=\"ZHUOYIQ.php.html#15\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\ZHUOYIQ<\/a>"],[0,4,"<a href=\"ZHUOYIY.php.html#15\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\ZHUOYIY<\/a>"],[0,5,"<a href=\"ZY.php.html#15\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\ZY<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,4,"<a href=\"CNPC.php.html#25\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\CNPC::handle<\/a>"],[0,3,"<a href=\"CY.php.html#23\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\CY::handle<\/a>"],[0,3,"<a href=\"CYLNG.php.html#23\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\CYLNG::handle<\/a>"],[0,3,"<a href=\"DH.php.html#23\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\DH::handle<\/a>"],[0,5,"<a href=\"DT.php.html#25\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\DT::handle<\/a>"],[0,4,"<a href=\"GDQP.php.html#25\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\GDQP::handle<\/a>"],[0,7,"<a href=\"GS.php.html#31\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\GS::handle<\/a>"],[0,4,"<a href=\"HBKJ.php.html#25\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\HBKJ::handle<\/a>"],[0,6,"<a href=\"HSY.php.html#27\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\HSY::handle<\/a>"],[0,7,"<a href=\"JHCX.php.html#26\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\JHCX::handle<\/a>"],[0,2,"<a href=\"JT.php.html#23\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\JT::handle<\/a>"],[0,2,"<a href=\"JTX.php.html#23\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\JTX::handle<\/a>"],[0,2,"<a href=\"SAIC.php.html#23\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\SAIC::handle<\/a>"],[0,3,"<a href=\"SC.php.html#25\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\SC::handle<\/a>"],[0,5,"<a href=\"SH.php.html#26\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\SH::handle<\/a>"],[0,5,"<a href=\"SHSX.php.html#26\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\SHSX::handle<\/a>"],[0,3,"<a href=\"SQZL.php.html#25\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\SQZL::handle<\/a>"],[0,1,"<a href=\"ThirdParty.php.html#11\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\ThirdParty::__construct<\/a>"],[0,6,"<a href=\"WJY.php.html#21\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\WJY::handle<\/a>"],[0,9,"<a href=\"XMSK.php.html#29\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\XMSK::handle<\/a>"],[0,3,"<a href=\"YC.php.html#26\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\YC::handle<\/a>"],[0,5,"<a href=\"ZDC.php.html#25\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\ZDC::handle<\/a>"],[0,4,"<a href=\"ZHUOYIQ.php.html#24\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\ZHUOYIQ::handle<\/a>"],[0,4,"<a href=\"ZHUOYIY.php.html#24\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\ZHUOYIY::handle<\/a>"],[0,5,"<a href=\"ZY.php.html#24\">App\\Models\\Logic\\Code\\GetSecondaryPaymentQrCode\\ZY::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
