<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Logic/Code/Parse</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Logic</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Code</a></li>
         <li class="breadcrumb-item"><a href="index.html">Parse</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#13">App\Models\Logic\Code\Parse\AD</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHENGMAN.php.html#12">App\Models\Logic\Code\Parse\SHENGMAN</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WZYT.php.html#13">App\Models\Logic\Code\Parse\WZYT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WSY.php.html#12">App\Models\Logic\Code\Parse\WSY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TC.php.html#14">App\Models\Logic\Code\Parse\TC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQ.php.html#10">App\Models\Logic\Code\Parse\SQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SP.php.html#13">App\Models\Logic\Code\Parse\SP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SM.php.html#15">App\Models\Logic\Code\Parse\SM</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFSX.php.html#13">App\Models\Logic\Code\Parse\SFSX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XM.php.html#12">App\Models\Logic\Code\Parse\XM</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RY.php.html#12">App\Models\Logic\Code\Parse\RY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RRS.php.html#12">App\Models\Logic\Code\Parse\RRS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RQ.php.html#12">App\Models\Logic\Code\Parse\RQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QDMY.php.html#13">App\Models\Logic\Code\Parse\QDMY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PCKJ.php.html#12">App\Models\Logic\Code\Parse\PCKJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MYCF.php.html#12">App\Models\Logic\Code\Parse\MYCF</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XC.php.html#14">App\Models\Logic\Code\Parse\XC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XYDS.php.html#12">App\Models\Logic\Code\Parse\XYDS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MY.php.html#12">App\Models\Logic\Code\Parse\MY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZJKA.php.html#12">App\Models\Logic\Code\Parse\ZJKA</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_BJ.php.html#12">App\Models\Logic\Code\Parse\ZZ_BJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_AH.php.html#12">App\Models\Logic\Code\Parse\ZZ_AH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ.php.html#12">App\Models\Logic\Code\Parse\ZZ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZTO.php.html#12">App\Models\Logic\Code\Parse\ZTO</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZT.php.html#12">App\Models\Logic\Code\Parse\ZT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZLGX.php.html#13">App\Models\Logic\Code\Parse\ZLGX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZJ.php.html#12">App\Models\Logic\Code\Parse\ZJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YB.php.html#12">App\Models\Logic\Code\Parse\YB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZEY.php.html#14">App\Models\Logic\Code\Parse\ZEY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YXT.php.html#12">App\Models\Logic\Code\Parse\YXT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YLZ.php.html#12">App\Models\Logic\Code\Parse\YLZ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGY.php.html#13">App\Models\Logic\Code\Parse\YGY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGJ.php.html#12">App\Models\Logic\Code\Parse\YGJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YBT.php.html#13">App\Models\Logic\Code\Parse\YBT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MYB.php.html#14">App\Models\Logic\Code\Parse\MYB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MK.php.html#14">App\Models\Logic\Code\Parse\MK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AJSW.php.html#12">App\Models\Logic\Code\Parse\AJSW</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CIEC.php.html#12">App\Models\Logic\Code\Parse\CIEC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FY.php.html#12">App\Models\Logic\Code\Parse\FY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#12">App\Models\Logic\Code\Parse\DESP2H8BBD</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2C637M.php.html#12">App\Models\Logic\Code\Parse\DESP2C637M</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#12">App\Models\Logic\Code\Parse\DESP2A6LA9</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DDE.php.html#14">App\Models\Logic\Code\Parse\DDE</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CN.php.html#14">App\Models\Logic\Code\Parse\CN</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CHTX.php.html#12">App\Models\Logic\Code\Parse\CHTX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBDW.php.html#12">App\Models\Logic\Code\Parse\GBDW</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CFT.php.html#12">App\Models\Logic\Code\Parse\CFT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CFHY.php.html#12">App\Models\Logic\Code\Parse\CFHY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Basic.php.html#7">App\Models\Logic\Code\Parse\Basic</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BQ.php.html#12">App\Models\Logic\Code\Parse\BQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BDT_ORG.php.html#12">App\Models\Logic\Code\Parse\BDT_ORG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BBSP.php.html#14">App\Models\Logic\Code\Parse\BBSP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="G7.php.html#12">App\Models\Logic\Code\Parse\G7</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GHC.php.html#12">App\Models\Logic\Code\Parse\GHC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MB.php.html#12">App\Models\Logic\Code\Parse\MB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JDWC.php.html#12">App\Models\Logic\Code\Parse\JDWC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LT.php.html#12">App\Models\Logic\Code\Parse\LT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LHYS.php.html#14">App\Models\Logic\Code\Parse\LHYS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LF.php.html#12">App\Models\Logic\Code\Parse\LF</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#12">App\Models\Logic\Code\Parse\KY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTXY.php.html#13">App\Models\Logic\Code\Parse\JTXY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JF.php.html#12">App\Models\Logic\Code\Parse\JF</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HZ.php.html#13">App\Models\Logic\Code\Parse\HZ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HG.php.html#15">App\Models\Logic\Code\Parse\HG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HYT_ORG.php.html#15">App\Models\Logic\Code\Parse\HYT_ORG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HYJY.php.html#12">App\Models\Logic\Code\Parse\HYJY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSL.php.html#13">App\Models\Logic\Code\Parse\HSL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HR.php.html#12">App\Models\Logic\Code\Parse\HR</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HLJH.php.html#12">App\Models\Logic\Code\Parse\HLJH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HK.php.html#12">App\Models\Logic\Code\Parse\HK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_TJ.php.html#12">App\Models\Logic\Code\Parse\ZZ_TJ</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="RRS.php.html#12">App\Models\Logic\Code\Parse\RRS</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="CFT.php.html#12">App\Models\Logic\Code\Parse\CFT</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="RY.php.html#12">App\Models\Logic\Code\Parse\RY</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="WSY.php.html#12">App\Models\Logic\Code\Parse\WSY</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="XYDS.php.html#12">App\Models\Logic\Code\Parse\XYDS</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="HK.php.html#12">App\Models\Logic\Code\Parse\HK</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="BQ.php.html#12">App\Models\Logic\Code\Parse\BQ</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="YXT.php.html#12">App\Models\Logic\Code\Parse\YXT</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="HG.php.html#15">App\Models\Logic\Code\Parse\HG</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="CFHY.php.html#12">App\Models\Logic\Code\Parse\CFHY</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ZT.php.html#12">App\Models\Logic\Code\Parse\ZT</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SHENGMAN.php.html#12">App\Models\Logic\Code\Parse\SHENGMAN</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ZTO.php.html#12">App\Models\Logic\Code\Parse\ZTO</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="YLZ.php.html#12">App\Models\Logic\Code\Parse\YLZ</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="MYCF.php.html#12">App\Models\Logic\Code\Parse\MYCF</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="KY.php.html#12">App\Models\Logic\Code\Parse\KY</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="G7.php.html#12">App\Models\Logic\Code\Parse\G7</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BBSP.php.html#14">App\Models\Logic\Code\Parse\BBSP</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SFSX.php.html#13">App\Models\Logic\Code\Parse\SFSX</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SM.php.html#15">App\Models\Logic\Code\Parse\SM</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="HZ.php.html#13">App\Models\Logic\Code\Parse\HZ</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SP.php.html#13">App\Models\Logic\Code\Parse\SP</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="JTXY.php.html#13">App\Models\Logic\Code\Parse\JTXY</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WZYT.php.html#13">App\Models\Logic\Code\Parse\WZYT</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="HSL.php.html#13">App\Models\Logic\Code\Parse\HSL</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="YBT.php.html#13">App\Models\Logic\Code\Parse\YBT</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="QDMY.php.html#13">App\Models\Logic\Code\Parse\QDMY</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZJ.php.html#12">App\Models\Logic\Code\Parse\ZJ</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZLGX.php.html#13">App\Models\Logic\Code\Parse\ZLGX</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AD.php.html#13">App\Models\Logic\Code\Parse\AD</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="YGY.php.html#13">App\Models\Logic\Code\Parse\YGY</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ZEY.php.html#14">App\Models\Logic\Code\Parse\ZEY</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="XM.php.html#12">App\Models\Logic\Code\Parse\XM</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="XC.php.html#14">App\Models\Logic\Code\Parse\XC</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ_AH.php.html#12">App\Models\Logic\Code\Parse\ZZ_AH</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ_BJ.php.html#12">App\Models\Logic\Code\Parse\ZZ_BJ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TC.php.html#14">App\Models\Logic\Code\Parse\TC</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZJKA.php.html#12">App\Models\Logic\Code\Parse\ZJKA</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ.php.html#12">App\Models\Logic\Code\Parse\ZZ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MK.php.html#14">App\Models\Logic\Code\Parse\MK</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RQ.php.html#12">App\Models\Logic\Code\Parse\RQ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HR.php.html#12">App\Models\Logic\Code\Parse\HR</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CIEC.php.html#12">App\Models\Logic\Code\Parse\CIEC</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CN.php.html#14">App\Models\Logic\Code\Parse\CN</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DDE.php.html#14">App\Models\Logic\Code\Parse\DDE</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#12">App\Models\Logic\Code\Parse\DESP2A6LA9</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DESP2C637M.php.html#12">App\Models\Logic\Code\Parse\DESP2C637M</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#12">App\Models\Logic\Code\Parse\DESP2H8BBD</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FY.php.html#12">App\Models\Logic\Code\Parse\FY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="GBDW.php.html#12">App\Models\Logic\Code\Parse\GBDW</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PCKJ.php.html#12">App\Models\Logic\Code\Parse\PCKJ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ_TJ.php.html#12">App\Models\Logic\Code\Parse\ZZ_TJ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HYT_ORG.php.html#15">App\Models\Logic\Code\Parse\HYT_ORG</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JF.php.html#12">App\Models\Logic\Code\Parse\JF</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="LHYS.php.html#14">App\Models\Logic\Code\Parse\LHYS</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="LT.php.html#12">App\Models\Logic\Code\Parse\LT</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AJSW.php.html#12">App\Models\Logic\Code\Parse\AJSW</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MY.php.html#12">App\Models\Logic\Code\Parse\MY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MYB.php.html#14">App\Models\Logic\Code\Parse\MYB</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HYJY.php.html#12">App\Models\Logic\Code\Parse\HYJY</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JDWC.php.html#12">App\Models\Logic\Code\Parse\JDWC</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="HLJH.php.html#12">App\Models\Logic\Code\Parse\HLJH</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="GHC.php.html#12">App\Models\Logic\Code\Parse\GHC</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="LF.php.html#12">App\Models\Logic\Code\Parse\LF</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="MB.php.html#12">App\Models\Logic\Code\Parse\MB</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CHTX.php.html#12">App\Models\Logic\Code\Parse\CHTX</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BDT_ORG.php.html#12">App\Models\Logic\Code\Parse\BDT_ORG</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="YGJ.php.html#12">App\Models\Logic\Code\Parse\YGJ</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#18"><abbr title="App\Models\Logic\Code\Parse\AD::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHENGMAN.php.html#17"><abbr title="App\Models\Logic\Code\Parse\SHENGMAN::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WZYT.php.html#21"><abbr title="App\Models\Logic\Code\Parse\WZYT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WSY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\WSY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TC.php.html#22"><abbr title="App\Models\Logic\Code\Parse\TC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQ.php.html#12"><abbr title="App\Models\Logic\Code\Parse\SQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SP.php.html#18"><abbr title="App\Models\Logic\Code\Parse\SP::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SM.php.html#23"><abbr title="App\Models\Logic\Code\Parse\SM::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFSX.php.html#21"><abbr title="App\Models\Logic\Code\Parse\SFSX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XM.php.html#17"><abbr title="App\Models\Logic\Code\Parse\XM::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\RY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RRS.php.html#17"><abbr title="App\Models\Logic\Code\Parse\RRS::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RQ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\RQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QDMY.php.html#21"><abbr title="App\Models\Logic\Code\Parse\QDMY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PCKJ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\PCKJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MYCF.php.html#17"><abbr title="App\Models\Logic\Code\Parse\MYCF::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XC.php.html#22"><abbr title="App\Models\Logic\Code\Parse\XC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XYDS.php.html#17"><abbr title="App\Models\Logic\Code\Parse\XYDS::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\MY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZJKA.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZJKA::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_BJ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZZ_BJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_AH.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZZ_AH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZZ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZTO.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZTO::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZT.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZLGX.php.html#21"><abbr title="App\Models\Logic\Code\Parse\ZLGX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZJ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YB.php.html#17"><abbr title="App\Models\Logic\Code\Parse\YB::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZEY.php.html#19"><abbr title="App\Models\Logic\Code\Parse\ZEY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YXT.php.html#17"><abbr title="App\Models\Logic\Code\Parse\YXT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YLZ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\YLZ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGY.php.html#18"><abbr title="App\Models\Logic\Code\Parse\YGY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGJ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\YGJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YBT.php.html#21"><abbr title="App\Models\Logic\Code\Parse\YBT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MYB.php.html#22"><abbr title="App\Models\Logic\Code\Parse\MYB::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MK.php.html#22"><abbr title="App\Models\Logic\Code\Parse\MK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AJSW.php.html#17"><abbr title="App\Models\Logic\Code\Parse\AJSW::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CIEC.php.html#17"><abbr title="App\Models\Logic\Code\Parse\CIEC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\FY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#17"><abbr title="App\Models\Logic\Code\Parse\DESP2H8BBD::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2C637M.php.html#17"><abbr title="App\Models\Logic\Code\Parse\DESP2C637M::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#17"><abbr title="App\Models\Logic\Code\Parse\DESP2A6LA9::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DDE.php.html#22"><abbr title="App\Models\Logic\Code\Parse\DDE::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CN.php.html#22"><abbr title="App\Models\Logic\Code\Parse\CN::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CHTX.php.html#17"><abbr title="App\Models\Logic\Code\Parse\CHTX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBDW.php.html#17"><abbr title="App\Models\Logic\Code\Parse\GBDW::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CFT.php.html#17"><abbr title="App\Models\Logic\Code\Parse\CFT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CFHY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\CFHY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Basic.php.html#11"><abbr title="App\Models\Logic\Code\Parse\Basic::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BQ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\BQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BDT_ORG.php.html#17"><abbr title="App\Models\Logic\Code\Parse\BDT_ORG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BBSP.php.html#22"><abbr title="App\Models\Logic\Code\Parse\BBSP::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="G7.php.html#20"><abbr title="App\Models\Logic\Code\Parse\G7::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GHC.php.html#20"><abbr title="App\Models\Logic\Code\Parse\GHC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MB.php.html#17"><abbr title="App\Models\Logic\Code\Parse\MB::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JDWC.php.html#17"><abbr title="App\Models\Logic\Code\Parse\JDWC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LT.php.html#17"><abbr title="App\Models\Logic\Code\Parse\LT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LHYS.php.html#22"><abbr title="App\Models\Logic\Code\Parse\LHYS::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LF.php.html#17"><abbr title="App\Models\Logic\Code\Parse\LF::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\KY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTXY.php.html#21"><abbr title="App\Models\Logic\Code\Parse\JTXY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JF.php.html#17"><abbr title="App\Models\Logic\Code\Parse\JF::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HZ.php.html#21"><abbr title="App\Models\Logic\Code\Parse\HZ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HG.php.html#23"><abbr title="App\Models\Logic\Code\Parse\HG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HYT_ORG.php.html#23"><abbr title="App\Models\Logic\Code\Parse\HYT_ORG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HYJY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\HYJY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSL.php.html#21"><abbr title="App\Models\Logic\Code\Parse\HSL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HR.php.html#17"><abbr title="App\Models\Logic\Code\Parse\HR::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HLJH.php.html#17"><abbr title="App\Models\Logic\Code\Parse\HLJH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HK.php.html#17"><abbr title="App\Models\Logic\Code\Parse\HK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_TJ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZZ_TJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="RRS.php.html#17"><abbr title="App\Models\Logic\Code\Parse\RRS::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="CFT.php.html#17"><abbr title="App\Models\Logic\Code\Parse\CFT::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="RY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\RY::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="WSY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\WSY::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="XYDS.php.html#17"><abbr title="App\Models\Logic\Code\Parse\XYDS::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="HK.php.html#17"><abbr title="App\Models\Logic\Code\Parse\HK::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="BQ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\BQ::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="YXT.php.html#17"><abbr title="App\Models\Logic\Code\Parse\YXT::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="HG.php.html#23"><abbr title="App\Models\Logic\Code\Parse\HG::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="CFHY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\CFHY::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ZT.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZT::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SHENGMAN.php.html#17"><abbr title="App\Models\Logic\Code\Parse\SHENGMAN::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ZTO.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZTO::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="YLZ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\YLZ::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="MYCF.php.html#17"><abbr title="App\Models\Logic\Code\Parse\MYCF::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="KY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\KY::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="G7.php.html#20"><abbr title="App\Models\Logic\Code\Parse\G7::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BBSP.php.html#22"><abbr title="App\Models\Logic\Code\Parse\BBSP::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SFSX.php.html#21"><abbr title="App\Models\Logic\Code\Parse\SFSX::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SM.php.html#23"><abbr title="App\Models\Logic\Code\Parse\SM::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="HZ.php.html#21"><abbr title="App\Models\Logic\Code\Parse\HZ::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SP.php.html#18"><abbr title="App\Models\Logic\Code\Parse\SP::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="JTXY.php.html#21"><abbr title="App\Models\Logic\Code\Parse\JTXY::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WZYT.php.html#21"><abbr title="App\Models\Logic\Code\Parse\WZYT::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="HSL.php.html#21"><abbr title="App\Models\Logic\Code\Parse\HSL::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="YBT.php.html#21"><abbr title="App\Models\Logic\Code\Parse\YBT::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="QDMY.php.html#21"><abbr title="App\Models\Logic\Code\Parse\QDMY::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZJ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZJ::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZLGX.php.html#21"><abbr title="App\Models\Logic\Code\Parse\ZLGX::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AD.php.html#18"><abbr title="App\Models\Logic\Code\Parse\AD::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="YGY.php.html#18"><abbr title="App\Models\Logic\Code\Parse\YGY::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ZEY.php.html#19"><abbr title="App\Models\Logic\Code\Parse\ZEY::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="XM.php.html#17"><abbr title="App\Models\Logic\Code\Parse\XM::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="XC.php.html#22"><abbr title="App\Models\Logic\Code\Parse\XC::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ_AH.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZZ_AH::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ_BJ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZZ_BJ::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TC.php.html#22"><abbr title="App\Models\Logic\Code\Parse\TC::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZJKA.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZJKA::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZZ::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MK.php.html#22"><abbr title="App\Models\Logic\Code\Parse\MK::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RQ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\RQ::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HR.php.html#17"><abbr title="App\Models\Logic\Code\Parse\HR::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CIEC.php.html#17"><abbr title="App\Models\Logic\Code\Parse\CIEC::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CN.php.html#22"><abbr title="App\Models\Logic\Code\Parse\CN::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DDE.php.html#22"><abbr title="App\Models\Logic\Code\Parse\DDE::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#17"><abbr title="App\Models\Logic\Code\Parse\DESP2A6LA9::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DESP2C637M.php.html#17"><abbr title="App\Models\Logic\Code\Parse\DESP2C637M::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#17"><abbr title="App\Models\Logic\Code\Parse\DESP2H8BBD::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\FY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="GBDW.php.html#17"><abbr title="App\Models\Logic\Code\Parse\GBDW::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PCKJ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\PCKJ::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ_TJ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\ZZ_TJ::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HYT_ORG.php.html#23"><abbr title="App\Models\Logic\Code\Parse\HYT_ORG::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JF.php.html#17"><abbr title="App\Models\Logic\Code\Parse\JF::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="LHYS.php.html#22"><abbr title="App\Models\Logic\Code\Parse\LHYS::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="LT.php.html#17"><abbr title="App\Models\Logic\Code\Parse\LT::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AJSW.php.html#17"><abbr title="App\Models\Logic\Code\Parse\AJSW::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\MY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MYB.php.html#22"><abbr title="App\Models\Logic\Code\Parse\MYB::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HYJY.php.html#17"><abbr title="App\Models\Logic\Code\Parse\HYJY::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JDWC.php.html#17"><abbr title="App\Models\Logic\Code\Parse\JDWC::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="HLJH.php.html#17"><abbr title="App\Models\Logic\Code\Parse\HLJH::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="GHC.php.html#20"><abbr title="App\Models\Logic\Code\Parse\GHC::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="LF.php.html#17"><abbr title="App\Models\Logic\Code\Parse\LF::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="MB.php.html#17"><abbr title="App\Models\Logic\Code\Parse\MB::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CHTX.php.html#17"><abbr title="App\Models\Logic\Code\Parse\CHTX::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BDT_ORG.php.html#17"><abbr title="App\Models\Logic\Code\Parse\BDT_ORG::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="YGJ.php.html#17"><abbr title="App\Models\Logic\Code\Parse\YGJ::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:47:38 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([71,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([71,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,5,"<a href=\"AD.php.html#13\">App\\Models\\Logic\\Code\\Parse\\AD<\/a>"],[0,3,"<a href=\"AJSW.php.html#12\">App\\Models\\Logic\\Code\\Parse\\AJSW<\/a>"],[0,5,"<a href=\"BBSP.php.html#14\">App\\Models\\Logic\\Code\\Parse\\BBSP<\/a>"],[0,2,"<a href=\"BDT_ORG.php.html#12\">App\\Models\\Logic\\Code\\Parse\\BDT_ORG<\/a>"],[0,10,"<a href=\"BQ.php.html#12\">App\\Models\\Logic\\Code\\Parse\\BQ<\/a>"],[0,1,"<a href=\"Basic.php.html#7\">App\\Models\\Logic\\Code\\Parse\\Basic<\/a>"],[0,7,"<a href=\"CFHY.php.html#12\">App\\Models\\Logic\\Code\\Parse\\CFHY<\/a>"],[0,10,"<a href=\"CFT.php.html#12\">App\\Models\\Logic\\Code\\Parse\\CFT<\/a>"],[0,2,"<a href=\"CHTX.php.html#12\">App\\Models\\Logic\\Code\\Parse\\CHTX<\/a>"],[0,3,"<a href=\"CIEC.php.html#12\">App\\Models\\Logic\\Code\\Parse\\CIEC<\/a>"],[0,3,"<a href=\"CN.php.html#14\">App\\Models\\Logic\\Code\\Parse\\CN<\/a>"],[0,3,"<a href=\"DDE.php.html#14\">App\\Models\\Logic\\Code\\Parse\\DDE<\/a>"],[0,3,"<a href=\"DESP2A6LA9.php.html#12\">App\\Models\\Logic\\Code\\Parse\\DESP2A6LA9<\/a>"],[0,3,"<a href=\"DESP2C637M.php.html#12\">App\\Models\\Logic\\Code\\Parse\\DESP2C637M<\/a>"],[0,3,"<a href=\"DESP2H8BBD.php.html#12\">App\\Models\\Logic\\Code\\Parse\\DESP2H8BBD<\/a>"],[0,3,"<a href=\"FY.php.html#12\">App\\Models\\Logic\\Code\\Parse\\FY<\/a>"],[0,6,"<a href=\"G7.php.html#12\">App\\Models\\Logic\\Code\\Parse\\G7<\/a>"],[0,3,"<a href=\"GBDW.php.html#12\">App\\Models\\Logic\\Code\\Parse\\GBDW<\/a>"],[0,2,"<a href=\"GHC.php.html#12\">App\\Models\\Logic\\Code\\Parse\\GHC<\/a>"],[0,7,"<a href=\"HG.php.html#15\">App\\Models\\Logic\\Code\\Parse\\HG<\/a>"],[0,10,"<a href=\"HK.php.html#12\">App\\Models\\Logic\\Code\\Parse\\HK<\/a>"],[0,2,"<a href=\"HLJH.php.html#12\">App\\Models\\Logic\\Code\\Parse\\HLJH<\/a>"],[0,3,"<a href=\"HR.php.html#12\">App\\Models\\Logic\\Code\\Parse\\HR<\/a>"],[0,5,"<a href=\"HSL.php.html#13\">App\\Models\\Logic\\Code\\Parse\\HSL<\/a>"],[0,2,"<a href=\"HYJY.php.html#12\">App\\Models\\Logic\\Code\\Parse\\HYJY<\/a>"],[0,3,"<a href=\"HYT_ORG.php.html#15\">App\\Models\\Logic\\Code\\Parse\\HYT_ORG<\/a>"],[0,5,"<a href=\"HZ.php.html#13\">App\\Models\\Logic\\Code\\Parse\\HZ<\/a>"],[0,2,"<a href=\"JDWC.php.html#12\">App\\Models\\Logic\\Code\\Parse\\JDWC<\/a>"],[0,3,"<a href=\"JF.php.html#12\">App\\Models\\Logic\\Code\\Parse\\JF<\/a>"],[0,5,"<a href=\"JTXY.php.html#13\">App\\Models\\Logic\\Code\\Parse\\JTXY<\/a>"],[0,6,"<a href=\"KY.php.html#12\">App\\Models\\Logic\\Code\\Parse\\KY<\/a>"],[0,2,"<a href=\"LF.php.html#12\">App\\Models\\Logic\\Code\\Parse\\LF<\/a>"],[0,3,"<a href=\"LHYS.php.html#14\">App\\Models\\Logic\\Code\\Parse\\LHYS<\/a>"],[0,3,"<a href=\"LT.php.html#12\">App\\Models\\Logic\\Code\\Parse\\LT<\/a>"],[0,2,"<a href=\"MB.php.html#12\">App\\Models\\Logic\\Code\\Parse\\MB<\/a>"],[0,3,"<a href=\"MK.php.html#14\">App\\Models\\Logic\\Code\\Parse\\MK<\/a>"],[0,3,"<a href=\"MY.php.html#12\">App\\Models\\Logic\\Code\\Parse\\MY<\/a>"],[0,3,"<a href=\"MYB.php.html#14\">App\\Models\\Logic\\Code\\Parse\\MYB<\/a>"],[0,6,"<a href=\"MYCF.php.html#12\">App\\Models\\Logic\\Code\\Parse\\MYCF<\/a>"],[0,3,"<a href=\"PCKJ.php.html#12\">App\\Models\\Logic\\Code\\Parse\\PCKJ<\/a>"],[0,5,"<a href=\"QDMY.php.html#13\">App\\Models\\Logic\\Code\\Parse\\QDMY<\/a>"],[0,3,"<a href=\"RQ.php.html#12\">App\\Models\\Logic\\Code\\Parse\\RQ<\/a>"],[0,10,"<a href=\"RRS.php.html#12\">App\\Models\\Logic\\Code\\Parse\\RRS<\/a>"],[0,10,"<a href=\"RY.php.html#12\">App\\Models\\Logic\\Code\\Parse\\RY<\/a>"],[0,5,"<a href=\"SFSX.php.html#13\">App\\Models\\Logic\\Code\\Parse\\SFSX<\/a>"],[0,6,"<a href=\"SHENGMAN.php.html#12\">App\\Models\\Logic\\Code\\Parse\\SHENGMAN<\/a>"],[0,5,"<a href=\"SM.php.html#15\">App\\Models\\Logic\\Code\\Parse\\SM<\/a>"],[0,5,"<a href=\"SP.php.html#13\">App\\Models\\Logic\\Code\\Parse\\SP<\/a>"],[0,1,"<a href=\"SQ.php.html#10\">App\\Models\\Logic\\Code\\Parse\\SQ<\/a>"],[0,3,"<a href=\"TC.php.html#14\">App\\Models\\Logic\\Code\\Parse\\TC<\/a>"],[0,10,"<a href=\"WSY.php.html#12\">App\\Models\\Logic\\Code\\Parse\\WSY<\/a>"],[0,5,"<a href=\"WZYT.php.html#13\">App\\Models\\Logic\\Code\\Parse\\WZYT<\/a>"],[0,3,"<a href=\"XC.php.html#14\">App\\Models\\Logic\\Code\\Parse\\XC<\/a>"],[0,3,"<a href=\"XM.php.html#12\">App\\Models\\Logic\\Code\\Parse\\XM<\/a>"],[0,10,"<a href=\"XYDS.php.html#12\">App\\Models\\Logic\\Code\\Parse\\XYDS<\/a>"],[0,1,"<a href=\"YB.php.html#12\">App\\Models\\Logic\\Code\\Parse\\YB<\/a>"],[0,5,"<a href=\"YBT.php.html#13\">App\\Models\\Logic\\Code\\Parse\\YBT<\/a>"],[0,2,"<a href=\"YGJ.php.html#12\">App\\Models\\Logic\\Code\\Parse\\YGJ<\/a>"],[0,4,"<a href=\"YGY.php.html#13\">App\\Models\\Logic\\Code\\Parse\\YGY<\/a>"],[0,6,"<a href=\"YLZ.php.html#12\">App\\Models\\Logic\\Code\\Parse\\YLZ<\/a>"],[0,7,"<a href=\"YXT.php.html#12\">App\\Models\\Logic\\Code\\Parse\\YXT<\/a>"],[0,4,"<a href=\"ZEY.php.html#14\">App\\Models\\Logic\\Code\\Parse\\ZEY<\/a>"],[0,5,"<a href=\"ZJ.php.html#12\">App\\Models\\Logic\\Code\\Parse\\ZJ<\/a>"],[0,3,"<a href=\"ZJKA.php.html#12\">App\\Models\\Logic\\Code\\Parse\\ZJKA<\/a>"],[0,5,"<a href=\"ZLGX.php.html#13\">App\\Models\\Logic\\Code\\Parse\\ZLGX<\/a>"],[0,6,"<a href=\"ZT.php.html#12\">App\\Models\\Logic\\Code\\Parse\\ZT<\/a>"],[0,6,"<a href=\"ZTO.php.html#12\">App\\Models\\Logic\\Code\\Parse\\ZTO<\/a>"],[0,3,"<a href=\"ZZ.php.html#12\">App\\Models\\Logic\\Code\\Parse\\ZZ<\/a>"],[0,3,"<a href=\"ZZ_AH.php.html#12\">App\\Models\\Logic\\Code\\Parse\\ZZ_AH<\/a>"],[0,3,"<a href=\"ZZ_BJ.php.html#12\">App\\Models\\Logic\\Code\\Parse\\ZZ_BJ<\/a>"],[0,3,"<a href=\"ZZ_TJ.php.html#12\">App\\Models\\Logic\\Code\\Parse\\ZZ_TJ<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,5,"<a href=\"AD.php.html#18\">App\\Models\\Logic\\Code\\Parse\\AD::handle<\/a>"],[0,3,"<a href=\"AJSW.php.html#17\">App\\Models\\Logic\\Code\\Parse\\AJSW::handle<\/a>"],[0,5,"<a href=\"BBSP.php.html#22\">App\\Models\\Logic\\Code\\Parse\\BBSP::handle<\/a>"],[0,2,"<a href=\"BDT_ORG.php.html#17\">App\\Models\\Logic\\Code\\Parse\\BDT_ORG::handle<\/a>"],[0,10,"<a href=\"BQ.php.html#17\">App\\Models\\Logic\\Code\\Parse\\BQ::handle<\/a>"],[0,1,"<a href=\"Basic.php.html#11\">App\\Models\\Logic\\Code\\Parse\\Basic::__construct<\/a>"],[0,7,"<a href=\"CFHY.php.html#17\">App\\Models\\Logic\\Code\\Parse\\CFHY::handle<\/a>"],[0,10,"<a href=\"CFT.php.html#17\">App\\Models\\Logic\\Code\\Parse\\CFT::handle<\/a>"],[0,2,"<a href=\"CHTX.php.html#17\">App\\Models\\Logic\\Code\\Parse\\CHTX::handle<\/a>"],[0,3,"<a href=\"CIEC.php.html#17\">App\\Models\\Logic\\Code\\Parse\\CIEC::handle<\/a>"],[0,3,"<a href=\"CN.php.html#22\">App\\Models\\Logic\\Code\\Parse\\CN::handle<\/a>"],[0,3,"<a href=\"DDE.php.html#22\">App\\Models\\Logic\\Code\\Parse\\DDE::handle<\/a>"],[0,3,"<a href=\"DESP2A6LA9.php.html#17\">App\\Models\\Logic\\Code\\Parse\\DESP2A6LA9::handle<\/a>"],[0,3,"<a href=\"DESP2C637M.php.html#17\">App\\Models\\Logic\\Code\\Parse\\DESP2C637M::handle<\/a>"],[0,3,"<a href=\"DESP2H8BBD.php.html#17\">App\\Models\\Logic\\Code\\Parse\\DESP2H8BBD::handle<\/a>"],[0,3,"<a href=\"FY.php.html#17\">App\\Models\\Logic\\Code\\Parse\\FY::handle<\/a>"],[0,6,"<a href=\"G7.php.html#20\">App\\Models\\Logic\\Code\\Parse\\G7::handle<\/a>"],[0,3,"<a href=\"GBDW.php.html#17\">App\\Models\\Logic\\Code\\Parse\\GBDW::handle<\/a>"],[0,2,"<a href=\"GHC.php.html#20\">App\\Models\\Logic\\Code\\Parse\\GHC::handle<\/a>"],[0,7,"<a href=\"HG.php.html#23\">App\\Models\\Logic\\Code\\Parse\\HG::handle<\/a>"],[0,10,"<a href=\"HK.php.html#17\">App\\Models\\Logic\\Code\\Parse\\HK::handle<\/a>"],[0,2,"<a href=\"HLJH.php.html#17\">App\\Models\\Logic\\Code\\Parse\\HLJH::handle<\/a>"],[0,3,"<a href=\"HR.php.html#17\">App\\Models\\Logic\\Code\\Parse\\HR::handle<\/a>"],[0,5,"<a href=\"HSL.php.html#21\">App\\Models\\Logic\\Code\\Parse\\HSL::handle<\/a>"],[0,2,"<a href=\"HYJY.php.html#17\">App\\Models\\Logic\\Code\\Parse\\HYJY::handle<\/a>"],[0,3,"<a href=\"HYT_ORG.php.html#23\">App\\Models\\Logic\\Code\\Parse\\HYT_ORG::handle<\/a>"],[0,5,"<a href=\"HZ.php.html#21\">App\\Models\\Logic\\Code\\Parse\\HZ::handle<\/a>"],[0,2,"<a href=\"JDWC.php.html#17\">App\\Models\\Logic\\Code\\Parse\\JDWC::handle<\/a>"],[0,3,"<a href=\"JF.php.html#17\">App\\Models\\Logic\\Code\\Parse\\JF::handle<\/a>"],[0,5,"<a href=\"JTXY.php.html#21\">App\\Models\\Logic\\Code\\Parse\\JTXY::handle<\/a>"],[0,6,"<a href=\"KY.php.html#17\">App\\Models\\Logic\\Code\\Parse\\KY::handle<\/a>"],[0,2,"<a href=\"LF.php.html#17\">App\\Models\\Logic\\Code\\Parse\\LF::handle<\/a>"],[0,3,"<a href=\"LHYS.php.html#22\">App\\Models\\Logic\\Code\\Parse\\LHYS::handle<\/a>"],[0,3,"<a href=\"LT.php.html#17\">App\\Models\\Logic\\Code\\Parse\\LT::handle<\/a>"],[0,2,"<a href=\"MB.php.html#17\">App\\Models\\Logic\\Code\\Parse\\MB::handle<\/a>"],[0,3,"<a href=\"MK.php.html#22\">App\\Models\\Logic\\Code\\Parse\\MK::handle<\/a>"],[0,3,"<a href=\"MY.php.html#17\">App\\Models\\Logic\\Code\\Parse\\MY::handle<\/a>"],[0,3,"<a href=\"MYB.php.html#22\">App\\Models\\Logic\\Code\\Parse\\MYB::handle<\/a>"],[0,6,"<a href=\"MYCF.php.html#17\">App\\Models\\Logic\\Code\\Parse\\MYCF::handle<\/a>"],[0,3,"<a href=\"PCKJ.php.html#17\">App\\Models\\Logic\\Code\\Parse\\PCKJ::handle<\/a>"],[0,5,"<a href=\"QDMY.php.html#21\">App\\Models\\Logic\\Code\\Parse\\QDMY::handle<\/a>"],[0,3,"<a href=\"RQ.php.html#17\">App\\Models\\Logic\\Code\\Parse\\RQ::handle<\/a>"],[0,10,"<a href=\"RRS.php.html#17\">App\\Models\\Logic\\Code\\Parse\\RRS::handle<\/a>"],[0,10,"<a href=\"RY.php.html#17\">App\\Models\\Logic\\Code\\Parse\\RY::handle<\/a>"],[0,5,"<a href=\"SFSX.php.html#21\">App\\Models\\Logic\\Code\\Parse\\SFSX::handle<\/a>"],[0,6,"<a href=\"SHENGMAN.php.html#17\">App\\Models\\Logic\\Code\\Parse\\SHENGMAN::handle<\/a>"],[0,5,"<a href=\"SM.php.html#23\">App\\Models\\Logic\\Code\\Parse\\SM::handle<\/a>"],[0,5,"<a href=\"SP.php.html#18\">App\\Models\\Logic\\Code\\Parse\\SP::handle<\/a>"],[0,1,"<a href=\"SQ.php.html#12\">App\\Models\\Logic\\Code\\Parse\\SQ::handle<\/a>"],[0,3,"<a href=\"TC.php.html#22\">App\\Models\\Logic\\Code\\Parse\\TC::handle<\/a>"],[0,10,"<a href=\"WSY.php.html#17\">App\\Models\\Logic\\Code\\Parse\\WSY::handle<\/a>"],[0,5,"<a href=\"WZYT.php.html#21\">App\\Models\\Logic\\Code\\Parse\\WZYT::handle<\/a>"],[0,3,"<a href=\"XC.php.html#22\">App\\Models\\Logic\\Code\\Parse\\XC::handle<\/a>"],[0,3,"<a href=\"XM.php.html#17\">App\\Models\\Logic\\Code\\Parse\\XM::handle<\/a>"],[0,10,"<a href=\"XYDS.php.html#17\">App\\Models\\Logic\\Code\\Parse\\XYDS::handle<\/a>"],[0,1,"<a href=\"YB.php.html#17\">App\\Models\\Logic\\Code\\Parse\\YB::handle<\/a>"],[0,5,"<a href=\"YBT.php.html#21\">App\\Models\\Logic\\Code\\Parse\\YBT::handle<\/a>"],[0,2,"<a href=\"YGJ.php.html#17\">App\\Models\\Logic\\Code\\Parse\\YGJ::handle<\/a>"],[0,4,"<a href=\"YGY.php.html#18\">App\\Models\\Logic\\Code\\Parse\\YGY::handle<\/a>"],[0,6,"<a href=\"YLZ.php.html#17\">App\\Models\\Logic\\Code\\Parse\\YLZ::handle<\/a>"],[0,7,"<a href=\"YXT.php.html#17\">App\\Models\\Logic\\Code\\Parse\\YXT::handle<\/a>"],[0,4,"<a href=\"ZEY.php.html#19\">App\\Models\\Logic\\Code\\Parse\\ZEY::handle<\/a>"],[0,5,"<a href=\"ZJ.php.html#17\">App\\Models\\Logic\\Code\\Parse\\ZJ::handle<\/a>"],[0,3,"<a href=\"ZJKA.php.html#17\">App\\Models\\Logic\\Code\\Parse\\ZJKA::handle<\/a>"],[0,5,"<a href=\"ZLGX.php.html#21\">App\\Models\\Logic\\Code\\Parse\\ZLGX::handle<\/a>"],[0,6,"<a href=\"ZT.php.html#17\">App\\Models\\Logic\\Code\\Parse\\ZT::handle<\/a>"],[0,6,"<a href=\"ZTO.php.html#17\">App\\Models\\Logic\\Code\\Parse\\ZTO::handle<\/a>"],[0,3,"<a href=\"ZZ.php.html#17\">App\\Models\\Logic\\Code\\Parse\\ZZ::handle<\/a>"],[0,3,"<a href=\"ZZ_AH.php.html#17\">App\\Models\\Logic\\Code\\Parse\\ZZ_AH::handle<\/a>"],[0,3,"<a href=\"ZZ_BJ.php.html#17\">App\\Models\\Logic\\Code\\Parse\\ZZ_BJ::handle<\/a>"],[0,3,"<a href=\"ZZ_TJ.php.html#17\">App\\Models\\Logic\\Code\\Parse\\ZZ_TJ::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
