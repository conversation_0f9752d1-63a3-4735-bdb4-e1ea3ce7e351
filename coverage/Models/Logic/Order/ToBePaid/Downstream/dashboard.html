<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Logic/Order/ToBePaid/Downstream</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Logic</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Order</a></li>
         <li class="breadcrumb-item"><a href="../index.html">ToBePaid</a></li>
         <li class="breadcrumb-item"><a href="index.html">Downstream</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#22">App\Models\Logic\Order\ToBePaid\Downstream\AD</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Main.php.html#12">App\Models\Logic\Order\ToBePaid\Downstream\Main</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZEY.php.html#24">App\Models\Logic\Order\ToBePaid\Downstream\ZEY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGY.php.html#27">App\Models\Logic\Order\ToBePaid\Downstream\YGY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sp.php.html#21">App\Models\Logic\Order\ToBePaid\Downstream\Sp</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Simple.php.html#21">App\Models\Logic\Order\ToBePaid\Downstream\Simple</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFSX.php.html#23">App\Models\Logic\Order\ToBePaid\Downstream\SFSX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFFY.php.html#23">App\Models\Logic\Order\ToBePaid\Downstream\SFFY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Rq.php.html#23">App\Models\Logic\Order\ToBePaid\Downstream\Rq</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#21">App\Models\Logic\Order\ToBePaid\Downstream\KY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#22">App\Models\Logic\Order\ToBePaid\Downstream\DESP2A6LA9</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HTX.php.html#18">App\Models\Logic\Order\ToBePaid\Downstream\HTX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HR.php.html#21">App\Models\Logic\Order\ToBePaid\Downstream\HR</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HLL.php.html#21">App\Models\Logic\Order\ToBePaid\Downstream\HLL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBDW.php.html#22">App\Models\Logic\Order\ToBePaid\Downstream\GBDW</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FY.php.html#22">App\Models\Logic\Order\ToBePaid\Downstream\FY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#22">App\Models\Logic\Order\ToBePaid\Downstream\DESP2H8BBD</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2C637M.php.html#22">App\Models\Logic\Order\ToBePaid\Downstream\DESP2C637M</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Zj.php.html#21">App\Models\Logic\Order\ToBePaid\Downstream\Zj</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="FY.php.html#22">App\Models\Logic\Order\ToBePaid\Downstream\FY</a></td><td class="text-right">812</td></tr>
       <tr><td><a href="SFSX.php.html#23">App\Models\Logic\Order\ToBePaid\Downstream\SFSX</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="SFFY.php.html#23">App\Models\Logic\Order\ToBePaid\Downstream\SFFY</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="KY.php.html#21">App\Models\Logic\Order\ToBePaid\Downstream\KY</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="GBDW.php.html#22">App\Models\Logic\Order\ToBePaid\Downstream\GBDW</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="HLL.php.html#21">App\Models\Logic\Order\ToBePaid\Downstream\HLL</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Rq.php.html#23">App\Models\Logic\Order\ToBePaid\Downstream\Rq</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Simple.php.html#21">App\Models\Logic\Order\ToBePaid\Downstream\Simple</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Zj.php.html#21">App\Models\Logic\Order\ToBePaid\Downstream\Zj</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ZEY.php.html#24">App\Models\Logic\Order\ToBePaid\Downstream\ZEY</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="HR.php.html#21">App\Models\Logic\Order\ToBePaid\Downstream\HR</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#22">App\Models\Logic\Order\ToBePaid\Downstream\DESP2A6LA9</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="HTX.php.html#18">App\Models\Logic\Order\ToBePaid\Downstream\HTX</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#22">App\Models\Logic\Order\ToBePaid\Downstream\DESP2H8BBD</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="DESP2C637M.php.html#22">App\Models\Logic\Order\ToBePaid\Downstream\DESP2C637M</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Sp.php.html#21">App\Models\Logic\Order\ToBePaid\Downstream\Sp</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="AD.php.html#22">App\Models\Logic\Order\ToBePaid\Downstream\AD</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="YGY.php.html#27">App\Models\Logic\Order\ToBePaid\Downstream\YGY</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Main.php.html#12">App\Models\Logic\Order\ToBePaid\Downstream\Main</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#30"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\AD::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Main.php.html#78"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\Main::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZEY.php.html#32"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\ZEY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGY.php.html#35"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\YGY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sp.php.html#29"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\Sp::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Simple.php.html#29"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\Simple::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFSX.php.html#31"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\SFSX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFFY.php.html#31"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\SFFY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Rq.php.html#31"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\Rq::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Main.php.html#59"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\Main::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#30"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\DESP2A6LA9::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#29"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\KY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HTX.php.html#27"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\HTX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HR.php.html#29"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\HR::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HLL.php.html#29"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\HLL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBDW.php.html#30"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\GBDW::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FY.php.html#30"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\FY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#30"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\DESP2H8BBD::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2C637M.php.html#30"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\DESP2C637M::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Zj.php.html#29"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\Zj::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="FY.php.html#30"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\FY::handle">handle</abbr></a></td><td class="text-right">812</td></tr>
       <tr><td><a href="SFSX.php.html#31"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\SFSX::handle">handle</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="SFFY.php.html#31"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\SFFY::handle">handle</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="KY.php.html#29"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\KY::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="GBDW.php.html#30"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\GBDW::handle">handle</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="HLL.php.html#29"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\HLL::handle">handle</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Rq.php.html#31"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\Rq::handle">handle</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Simple.php.html#29"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\Simple::handle">handle</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Zj.php.html#29"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\Zj::handle">handle</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ZEY.php.html#32"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\ZEY::handle">handle</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="HR.php.html#29"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\HR::handle">handle</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#30"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\DESP2A6LA9::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="HTX.php.html#27"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\HTX::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#30"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\DESP2H8BBD::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="DESP2C637M.php.html#30"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\DESP2C637M::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Sp.php.html#29"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\Sp::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="AD.php.html#30"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\AD::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="YGY.php.html#35"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\YGY::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Main.php.html#59"><abbr title="App\Models\Logic\Order\ToBePaid\Downstream\Main::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:21:05 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([19,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([20,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,9,"<a href=\"AD.php.html#22\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\AD<\/a>"],[0,10,"<a href=\"DESP2A6LA9.php.html#22\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\DESP2A6LA9<\/a>"],[0,10,"<a href=\"DESP2C637M.php.html#22\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\DESP2C637M<\/a>"],[0,10,"<a href=\"DESP2H8BBD.php.html#22\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\DESP2H8BBD<\/a>"],[0,28,"<a href=\"FY.php.html#22\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\FY<\/a>"],[0,13,"<a href=\"GBDW.php.html#22\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\GBDW<\/a>"],[0,13,"<a href=\"HLL.php.html#21\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\HLL<\/a>"],[0,11,"<a href=\"HR.php.html#21\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\HR<\/a>"],[0,10,"<a href=\"HTX.php.html#18\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\HTX<\/a>"],[0,14,"<a href=\"KY.php.html#21\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\KY<\/a>"],[0,3,"<a href=\"Main.php.html#12\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\Main<\/a>"],[0,12,"<a href=\"Rq.php.html#23\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\Rq<\/a>"],[0,17,"<a href=\"SFFY.php.html#23\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\SFFY<\/a>"],[0,17,"<a href=\"SFSX.php.html#23\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\SFSX<\/a>"],[0,12,"<a href=\"Simple.php.html#21\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\Simple<\/a>"],[0,10,"<a href=\"Sp.php.html#21\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\Sp<\/a>"],[0,7,"<a href=\"YGY.php.html#27\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\YGY<\/a>"],[0,11,"<a href=\"ZEY.php.html#24\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\ZEY<\/a>"],[0,12,"<a href=\"Zj.php.html#21\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\Zj<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,9,"<a href=\"AD.php.html#30\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\AD::handle<\/a>"],[0,10,"<a href=\"DESP2A6LA9.php.html#30\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\DESP2A6LA9::handle<\/a>"],[0,10,"<a href=\"DESP2C637M.php.html#30\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\DESP2C637M::handle<\/a>"],[0,10,"<a href=\"DESP2H8BBD.php.html#30\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\DESP2H8BBD::handle<\/a>"],[0,28,"<a href=\"FY.php.html#30\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\FY::handle<\/a>"],[0,13,"<a href=\"GBDW.php.html#30\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\GBDW::handle<\/a>"],[0,13,"<a href=\"HLL.php.html#29\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\HLL::handle<\/a>"],[0,11,"<a href=\"HR.php.html#29\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\HR::handle<\/a>"],[0,10,"<a href=\"HTX.php.html#27\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\HTX::handle<\/a>"],[0,14,"<a href=\"KY.php.html#29\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\KY::handle<\/a>"],[0,2,"<a href=\"Main.php.html#59\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\Main::__construct<\/a>"],[0,1,"<a href=\"Main.php.html#78\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\Main::handle<\/a>"],[0,12,"<a href=\"Rq.php.html#31\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\Rq::handle<\/a>"],[0,17,"<a href=\"SFFY.php.html#31\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\SFFY::handle<\/a>"],[0,17,"<a href=\"SFSX.php.html#31\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\SFSX::handle<\/a>"],[0,12,"<a href=\"Simple.php.html#29\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\Simple::handle<\/a>"],[0,10,"<a href=\"Sp.php.html#29\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\Sp::handle<\/a>"],[0,7,"<a href=\"YGY.php.html#35\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\YGY::handle<\/a>"],[0,11,"<a href=\"ZEY.php.html#32\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\ZEY::handle<\/a>"],[0,12,"<a href=\"Zj.php.html#29\">App\\Models\\Logic\\Order\\ToBePaid\\Downstream\\Zj::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
