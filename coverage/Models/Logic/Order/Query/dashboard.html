<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Logic/Order/Query</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Logic</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Order</a></li>
         <li class="breadcrumb-item"><a href="index.html">Query</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="FO.php.html#10">App\Models\Logic\Order\Query\FO</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GS.php.html#15">App\Models\Logic\Order\Query\GS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GSP.php.html#11">App\Models\Logic\Order\Query\GSP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KL.php.html#12">App\Models\Logic\Order\Query\KL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#13">App\Models\Logic\Order\Query\KY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MB.php.html#12">App\Models\Logic\Order\Query\MB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SC.php.html#23">App\Models\Logic\Order\Query\SC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SP.php.html#13">App\Models\Logic\Order\Query\SP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#9">App\Models\Logic\Order\Query\ThirdParty</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YC.php.html#14">App\Models\Logic\Order\Query\YC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGY.php.html#13">App\Models\Logic\Order\Query\YGY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZY.php.html#14">App\Models\Logic\Order\Query\ZY</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="SC.php.html#23">App\Models\Logic\Order\Query\SC</a></td><td class="text-right">756</td></tr>
       <tr><td><a href="YC.php.html#14">App\Models\Logic\Order\Query\YC</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="GS.php.html#15">App\Models\Logic\Order\Query\GS</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="KL.php.html#12">App\Models\Logic\Order\Query\KL</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="MB.php.html#12">App\Models\Logic\Order\Query\MB</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZY.php.html#14">App\Models\Logic\Order\Query\ZY</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="GSP.php.html#11">App\Models\Logic\Order\Query\GSP</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="KY.php.html#13">App\Models\Logic\Order\Query\KY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SP.php.html#13">App\Models\Logic\Order\Query\SP</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="YGY.php.html#13">App\Models\Logic\Order\Query\YGY</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="FO.php.html#19"><abbr title="App\Models\Logic\Order\Query\FO::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GS.php.html#24"><abbr title="App\Models\Logic\Order\Query\GS::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GSP.php.html#24"><abbr title="App\Models\Logic\Order\Query\GSP::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KL.php.html#21"><abbr title="App\Models\Logic\Order\Query\KL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#23"><abbr title="App\Models\Logic\Order\Query\KY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MB.php.html#30"><abbr title="App\Models\Logic\Order\Query\MB::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SC.php.html#32"><abbr title="App\Models\Logic\Order\Query\SC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SP.php.html#27"><abbr title="App\Models\Logic\Order\Query\SP::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#20"><abbr title="App\Models\Logic\Order\Query\ThirdParty::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YC.php.html#28"><abbr title="App\Models\Logic\Order\Query\YC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGY.php.html#30"><abbr title="App\Models\Logic\Order\Query\YGY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZY.php.html#30"><abbr title="App\Models\Logic\Order\Query\ZY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="SC.php.html#32"><abbr title="App\Models\Logic\Order\Query\SC::handle">handle</abbr></a></td><td class="text-right">756</td></tr>
       <tr><td><a href="YC.php.html#28"><abbr title="App\Models\Logic\Order\Query\YC::handle">handle</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="GS.php.html#24"><abbr title="App\Models\Logic\Order\Query\GS::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="KL.php.html#21"><abbr title="App\Models\Logic\Order\Query\KL::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="MB.php.html#30"><abbr title="App\Models\Logic\Order\Query\MB::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZY.php.html#30"><abbr title="App\Models\Logic\Order\Query\ZY::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="GSP.php.html#24"><abbr title="App\Models\Logic\Order\Query\GSP::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="KY.php.html#23"><abbr title="App\Models\Logic\Order\Query\KY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SP.php.html#27"><abbr title="App\Models\Logic\Order\Query\SP::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="YGY.php.html#30"><abbr title="App\Models\Logic\Order\Query\YGY::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:21:05 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([12,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([12,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"FO.php.html#10\">App\\Models\\Logic\\Order\\Query\\FO<\/a>"],[0,5,"<a href=\"GS.php.html#15\">App\\Models\\Logic\\Order\\Query\\GS<\/a>"],[0,3,"<a href=\"GSP.php.html#11\">App\\Models\\Logic\\Order\\Query\\GSP<\/a>"],[0,5,"<a href=\"KL.php.html#12\">App\\Models\\Logic\\Order\\Query\\KL<\/a>"],[0,3,"<a href=\"KY.php.html#13\">App\\Models\\Logic\\Order\\Query\\KY<\/a>"],[0,5,"<a href=\"MB.php.html#12\">App\\Models\\Logic\\Order\\Query\\MB<\/a>"],[0,27,"<a href=\"SC.php.html#23\">App\\Models\\Logic\\Order\\Query\\SC<\/a>"],[0,2,"<a href=\"SP.php.html#13\">App\\Models\\Logic\\Order\\Query\\SP<\/a>"],[0,1,"<a href=\"ThirdParty.php.html#9\">App\\Models\\Logic\\Order\\Query\\ThirdParty<\/a>"],[0,13,"<a href=\"YC.php.html#14\">App\\Models\\Logic\\Order\\Query\\YC<\/a>"],[0,2,"<a href=\"YGY.php.html#13\">App\\Models\\Logic\\Order\\Query\\YGY<\/a>"],[0,4,"<a href=\"ZY.php.html#14\">App\\Models\\Logic\\Order\\Query\\ZY<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"FO.php.html#19\">App\\Models\\Logic\\Order\\Query\\FO::handle<\/a>"],[0,5,"<a href=\"GS.php.html#24\">App\\Models\\Logic\\Order\\Query\\GS::handle<\/a>"],[0,3,"<a href=\"GSP.php.html#24\">App\\Models\\Logic\\Order\\Query\\GSP::handle<\/a>"],[0,5,"<a href=\"KL.php.html#21\">App\\Models\\Logic\\Order\\Query\\KL::handle<\/a>"],[0,3,"<a href=\"KY.php.html#23\">App\\Models\\Logic\\Order\\Query\\KY::handle<\/a>"],[0,5,"<a href=\"MB.php.html#30\">App\\Models\\Logic\\Order\\Query\\MB::handle<\/a>"],[0,27,"<a href=\"SC.php.html#32\">App\\Models\\Logic\\Order\\Query\\SC::handle<\/a>"],[0,2,"<a href=\"SP.php.html#27\">App\\Models\\Logic\\Order\\Query\\SP::handle<\/a>"],[0,1,"<a href=\"ThirdParty.php.html#20\">App\\Models\\Logic\\Order\\Query\\ThirdParty::handle<\/a>"],[0,13,"<a href=\"YC.php.html#28\">App\\Models\\Logic\\Order\\Query\\YC::handle<\/a>"],[0,2,"<a href=\"YGY.php.html#30\">App\\Models\\Logic\\Order\\Query\\YGY::handle<\/a>"],[0,4,"<a href=\"ZY.php.html#30\">App\\Models\\Logic\\Order\\Query\\ZY::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
