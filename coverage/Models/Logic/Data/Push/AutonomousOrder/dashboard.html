<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Logic/Data/Push/AutonomousOrder</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Logic</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Data</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Push</a></li>
         <li class="breadcrumb-item"><a href="index.html">AutonomousOrder</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CNPC.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\CNPC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SC.php.html#10">App\Models\Logic\Data\Push\AutonomousOrder\SC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZWL.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\ZWL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHYK.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\ZHYK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\ZHUOYIY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\ZHUOYIQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZDC.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\ZDC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YUNDATONG.php.html#16">App\Models\Logic\Data\Push\AutonomousOrder\YUNDATONG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YC.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\YC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XMSK.php.html#18">App\Models\Logic\Data\Push\AutonomousOrder\XMSK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#9">App\Models\Logic\Data\Push\AutonomousOrder\ThirdParty</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TBJX.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\TBJX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZSH.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\SQZSH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZL.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\SQZL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHSX.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\SHSX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SH.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\SH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAIC.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\SAIC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CY.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\CY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MTLSY.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\MTLSY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTX.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\JTX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JT.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\JT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JHCX.php.html#10">App\Models\Logic\Data\Push\AutonomousOrder\JHCX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSY.php.html#14">App\Models\Logic\Data\Push\AutonomousOrder\HSY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HBKJ.php.html#14">App\Models\Logic\Data\Push\AutonomousOrder\HBKJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GS.php.html#11">App\Models\Logic\Data\Push\AutonomousOrder\GS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GDQP.php.html#10">App\Models\Logic\Data\Push\AutonomousOrder\GDQP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GB.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\GB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GAODENG.php.html#16">App\Models\Logic\Data\Push\AutonomousOrder\GAODENG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EZT.php.html#14">App\Models\Logic\Data\Push\AutonomousOrder\EZT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DT.php.html#19">App\Models\Logic\Data\Push\AutonomousOrder\DT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DH.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\DH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CYLNG.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\CYLNG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZY.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\ZY</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DT.php.html#19">App\Models\Logic\Data\Push\AutonomousOrder\DT</a></td><td class="text-right">462</td></tr>
       <tr><td><a href="GAODENG.php.html#16">App\Models\Logic\Data\Push\AutonomousOrder\GAODENG</a></td><td class="text-right">380</td></tr>
       <tr><td><a href="ZY.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\ZY</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="GB.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\GB</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="XMSK.php.html#18">App\Models\Logic\Data\Push\AutonomousOrder\XMSK</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="EZT.php.html#14">App\Models\Logic\Data\Push\AutonomousOrder\EZT</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="HSY.php.html#14">App\Models\Logic\Data\Push\AutonomousOrder\HSY</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ZHYK.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\ZHYK</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="YUNDATONG.php.html#16">App\Models\Logic\Data\Push\AutonomousOrder\YUNDATONG</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ZWL.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\ZWL</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="SAIC.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\SAIC</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="JHCX.php.html#10">App\Models\Logic\Data\Push\AutonomousOrder\JHCX</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="MTLSY.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\MTLSY</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="SQZL.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\SQZL</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="SQZSH.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\SQZSH</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\ZHUOYIY</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\ZHUOYIQ</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="YC.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\YC</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CYLNG.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\CYLNG</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZDC.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\ZDC</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JT.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\JT</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CY.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\CY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JTX.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\JTX</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HBKJ.php.html#14">App\Models\Logic\Data\Push\AutonomousOrder\HBKJ</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TBJX.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\TBJX</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SHSX.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\SHSX</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SH.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\SH</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DH.php.html#15">App\Models\Logic\Data\Push\AutonomousOrder\DH</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CNPC.php.html#12">App\Models\Logic\Data\Push\AutonomousOrder\CNPC</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CNPC.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\CNPC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YC.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\YC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHSX.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\SHSX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZL.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\SQZL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZSH.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\SQZSH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TBJX.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\TBJX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#11"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ThirdParty::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XMSK.php.html#26"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\XMSK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XMSK.php.html#139"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\XMSK::getPlateNoFromPool">getPlateNoFromPool</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YC.php.html#50"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\YC::getOrderNo">getOrderNo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SC.php.html#18"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\SC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YUNDATONG.php.html#24"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\YUNDATONG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZDC.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZDC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZHUOYIQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZHUOYIY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHYK.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZHYK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZWL.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZWL::checkDriverRegisteredAndBoundCarPlateNo">checkDriverRegisteredAndBoundCarPlateNo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZWL.php.html#88"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZWL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SH.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\SH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAIC.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\SAIC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CY.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\CY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GAODENG.php.html#92"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\GAODENG::getPhoneFromPool">getPhoneFromPool</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CYLNG.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\CYLNG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DH.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\DH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DT.php.html#27"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\DT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DT.php.html#156"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\DT::getPhoneFromPool">getPhoneFromPool</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DT.php.html#207"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\DT::releasePhoneToPool">releasePhoneToPool</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EZT.php.html#22"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\EZT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GAODENG.php.html#24"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\GAODENG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GAODENG.php.html#122"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\GAODENG::getPlateNoFromPool">getPlateNoFromPool</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MTLSY.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\MTLSY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GB.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\GB::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GDQP.php.html#18"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\GDQP::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GS.php.html#19"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\GS::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HBKJ.php.html#22"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\HBKJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSY.php.html#22"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\HSY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JHCX.php.html#18"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\JHCX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JT.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\JT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTX.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\JTX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZY.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ZY.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZY::handle">handle</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="GB.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\GB::handle">handle</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="HSY.php.html#22"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\HSY::handle">handle</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="EZT.php.html#22"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\EZT::handle">handle</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ZHYK.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZHYK::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="YUNDATONG.php.html#24"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\YUNDATONG::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="DT.php.html#27"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\DT::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="DT.php.html#156"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\DT::getPhoneFromPool">getPhoneFromPool</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="GAODENG.php.html#24"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\GAODENG::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="SAIC.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\SAIC::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="SQZSH.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\SQZSH::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="SQZL.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\SQZL::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="JHCX.php.html#18"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\JHCX::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="MTLSY.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\MTLSY::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZHUOYIY::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="XMSK.php.html#26"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\XMSK::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZHUOYIQ::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="GAODENG.php.html#122"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\GAODENG::getPlateNoFromPool">getPlateNoFromPool</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZWL.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZWL::checkDriverRegisteredAndBoundCarPlateNo">checkDriverRegisteredAndBoundCarPlateNo</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="GAODENG.php.html#92"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\GAODENG::getPhoneFromPool">getPhoneFromPool</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="XMSK.php.html#139"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\XMSK::getPlateNoFromPool">getPlateNoFromPool</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZWL.php.html#88"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZWL::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ZDC.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\ZDC::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JTX.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\JTX::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CY.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\CY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DT.php.html#207"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\DT::releasePhoneToPool">releasePhoneToPool</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CYLNG.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\CYLNG::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JT.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\JT::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HBKJ.php.html#22"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\HBKJ::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DH.php.html#23"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\DH::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="YC.php.html#50"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\YC::getOrderNo">getOrderNo</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TBJX.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\TBJX::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SHSX.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\SHSX::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SH.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\SH::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CNPC.php.html#20"><abbr title="App\Models\Logic\Data\Push\AutonomousOrder\CNPC::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:21:05 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([33,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([40,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"CNPC.php.html#12\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\CNPC<\/a>"],[0,3,"<a href=\"CY.php.html#12\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\CY<\/a>"],[0,3,"<a href=\"CYLNG.php.html#12\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\CYLNG<\/a>"],[0,2,"<a href=\"DH.php.html#15\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\DH<\/a>"],[0,21,"<a href=\"DT.php.html#19\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\DT<\/a>"],[0,11,"<a href=\"EZT.php.html#14\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\EZT<\/a>"],[0,19,"<a href=\"GAODENG.php.html#16\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\GAODENG<\/a>"],[0,13,"<a href=\"GB.php.html#15\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\GB<\/a>"],[0,1,"<a href=\"GDQP.php.html#10\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\GDQP<\/a>"],[0,1,"<a href=\"GS.php.html#11\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\GS<\/a>"],[0,2,"<a href=\"HBKJ.php.html#14\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\HBKJ<\/a>"],[0,11,"<a href=\"HSY.php.html#14\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\HSY<\/a>"],[0,7,"<a href=\"JHCX.php.html#10\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\JHCX<\/a>"],[0,3,"<a href=\"JT.php.html#12\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\JT<\/a>"],[0,3,"<a href=\"JTX.php.html#12\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\JTX<\/a>"],[0,7,"<a href=\"MTLSY.php.html#15\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\MTLSY<\/a>"],[0,8,"<a href=\"SAIC.php.html#15\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\SAIC<\/a>"],[0,1,"<a href=\"SC.php.html#10\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\SC<\/a>"],[0,2,"<a href=\"SH.php.html#12\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\SH<\/a>"],[0,2,"<a href=\"SHSX.php.html#12\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\SHSX<\/a>"],[0,7,"<a href=\"SQZL.php.html#15\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\SQZL<\/a>"],[0,7,"<a href=\"SQZSH.php.html#15\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\SQZSH<\/a>"],[0,2,"<a href=\"TBJX.php.html#12\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\TBJX<\/a>"],[0,1,"<a href=\"ThirdParty.php.html#9\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ThirdParty<\/a>"],[0,11,"<a href=\"XMSK.php.html#18\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\XMSK<\/a>"],[0,3,"<a href=\"YC.php.html#12\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\YC<\/a>"],[0,10,"<a href=\"YUNDATONG.php.html#16\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\YUNDATONG<\/a>"],[0,3,"<a href=\"ZDC.php.html#12\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ZDC<\/a>"],[0,6,"<a href=\"ZHUOYIQ.php.html#15\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ZHUOYIQ<\/a>"],[0,6,"<a href=\"ZHUOYIY.php.html#15\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ZHUOYIY<\/a>"],[0,10,"<a href=\"ZHYK.php.html#15\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ZHYK<\/a>"],[0,9,"<a href=\"ZWL.php.html#15\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ZWL<\/a>"],[0,13,"<a href=\"ZY.php.html#15\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ZY<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"CNPC.php.html#20\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\CNPC::handle<\/a>"],[0,3,"<a href=\"CY.php.html#20\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\CY::handle<\/a>"],[0,3,"<a href=\"CYLNG.php.html#20\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\CYLNG::handle<\/a>"],[0,2,"<a href=\"DH.php.html#23\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\DH::handle<\/a>"],[0,9,"<a href=\"DT.php.html#27\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\DT::handle<\/a>"],[0,9,"<a href=\"DT.php.html#156\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\DT::getPhoneFromPool<\/a>"],[0,3,"<a href=\"DT.php.html#207\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\DT::releasePhoneToPool<\/a>"],[0,11,"<a href=\"EZT.php.html#22\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\EZT::handle<\/a>"],[0,9,"<a href=\"GAODENG.php.html#24\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\GAODENG::handle<\/a>"],[0,5,"<a href=\"GAODENG.php.html#92\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\GAODENG::getPhoneFromPool<\/a>"],[0,5,"<a href=\"GAODENG.php.html#122\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\GAODENG::getPlateNoFromPool<\/a>"],[0,13,"<a href=\"GB.php.html#23\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\GB::handle<\/a>"],[0,1,"<a href=\"GDQP.php.html#18\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\GDQP::handle<\/a>"],[0,1,"<a href=\"GS.php.html#19\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\GS::handle<\/a>"],[0,2,"<a href=\"HBKJ.php.html#22\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\HBKJ::handle<\/a>"],[0,11,"<a href=\"HSY.php.html#22\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\HSY::handle<\/a>"],[0,7,"<a href=\"JHCX.php.html#18\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\JHCX::handle<\/a>"],[0,3,"<a href=\"JT.php.html#20\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\JT::handle<\/a>"],[0,3,"<a href=\"JTX.php.html#20\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\JTX::handle<\/a>"],[0,7,"<a href=\"MTLSY.php.html#23\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\MTLSY::handle<\/a>"],[0,8,"<a href=\"SAIC.php.html#23\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\SAIC::handle<\/a>"],[0,1,"<a href=\"SC.php.html#18\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\SC::handle<\/a>"],[0,2,"<a href=\"SH.php.html#20\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\SH::handle<\/a>"],[0,2,"<a href=\"SHSX.php.html#20\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\SHSX::handle<\/a>"],[0,7,"<a href=\"SQZL.php.html#23\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\SQZL::handle<\/a>"],[0,7,"<a href=\"SQZSH.php.html#23\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\SQZSH::handle<\/a>"],[0,2,"<a href=\"TBJX.php.html#20\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\TBJX::handle<\/a>"],[0,1,"<a href=\"ThirdParty.php.html#11\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ThirdParty::__construct<\/a>"],[0,6,"<a href=\"XMSK.php.html#26\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\XMSK::handle<\/a>"],[0,5,"<a href=\"XMSK.php.html#139\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\XMSK::getPlateNoFromPool<\/a>"],[0,1,"<a href=\"YC.php.html#20\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\YC::handle<\/a>"],[0,2,"<a href=\"YC.php.html#50\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\YC::getOrderNo<\/a>"],[0,10,"<a href=\"YUNDATONG.php.html#24\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\YUNDATONG::handle<\/a>"],[0,3,"<a href=\"ZDC.php.html#20\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ZDC::handle<\/a>"],[0,6,"<a href=\"ZHUOYIQ.php.html#23\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ZHUOYIQ::handle<\/a>"],[0,6,"<a href=\"ZHUOYIY.php.html#23\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ZHUOYIY::handle<\/a>"],[0,10,"<a href=\"ZHYK.php.html#23\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ZHYK::handle<\/a>"],[0,5,"<a href=\"ZWL.php.html#20\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ZWL::checkDriverRegisteredAndBoundCarPlateNo<\/a>"],[0,4,"<a href=\"ZWL.php.html#88\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ZWL::handle<\/a>"],[0,13,"<a href=\"ZY.php.html#23\">App\\Models\\Logic\\Data\\Push\\AutonomousOrder\\ZY::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
