<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Logic/Data/Push/ToBePaid</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Logic</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Data</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Push</a></li>
         <li class="breadcrumb-item"><a href="index.html">ToBePaid</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#16">App\Models\Logic\Data\Push\ToBePaid\AD</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGJ.php.html#10">App\Models\Logic\Data\Push\ToBePaid\YGJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RRS.php.html#11">App\Models\Logic\Data\Push\ToBePaid\RRS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\RY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFSX.php.html#10">App\Models\Logic\Data\Push\ToBePaid\SFSX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHENGMAN.php.html#11">App\Models\Logic\Data\Push\ToBePaid\SHENGMAN</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SM.php.html#9">App\Models\Logic\Data\Push\ToBePaid\SM</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WSY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\WSY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WZYT.php.html#11">App\Models\Logic\Data\Push\ToBePaid\WZYT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XM.php.html#11">App\Models\Logic\Data\Push\ToBePaid\XM</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XYDS.php.html#11">App\Models\Logic\Data\Push\ToBePaid\XYDS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YBT.php.html#11">App\Models\Logic\Data\Push\ToBePaid\YBT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YLZ.php.html#11">App\Models\Logic\Data\Push\ToBePaid\YLZ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QDMY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\QDMY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YXT.php.html#11">App\Models\Logic\Data\Push\ToBePaid\YXT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZEY.php.html#12">App\Models\Logic\Data\Push\ToBePaid\ZEY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZJ.php.html#12">App\Models\Logic\Data\Push\ToBePaid\ZJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZJKA.php.html#9">App\Models\Logic\Data\Push\ToBePaid\ZJKA</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZLGX.php.html#10">App\Models\Logic\Data\Push\ToBePaid\ZLGX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZT.php.html#11">App\Models\Logic\Data\Push\ToBePaid\ZT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZTO.php.html#11">App\Models\Logic\Data\Push\ToBePaid\ZTO</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ.php.html#11">App\Models\Logic\Data\Push\ToBePaid\ZZ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_AH.php.html#11">App\Models\Logic\Data\Push\ToBePaid\ZZ_AH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_BJ.php.html#11">App\Models\Logic\Data\Push\ToBePaid\ZZ_BJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RQ.php.html#10">App\Models\Logic\Data\Push\ToBePaid\RQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PCKJ.php.html#10">App\Models\Logic\Data\Push\ToBePaid\PCKJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AJSW.php.html#11">App\Models\Logic\Data\Push\ToBePaid\AJSW</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HG.php.html#9">App\Models\Logic\Data\Push\ToBePaid\HG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BBSP.php.html#13">App\Models\Logic\Data\Push\ToBePaid\BBSP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BDT_ORG.php.html#10">App\Models\Logic\Data\Push\ToBePaid\BDT_ORG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BQ.php.html#11">App\Models\Logic\Data\Push\ToBePaid\BQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CFHY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\CFHY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CFT.php.html#11">App\Models\Logic\Data\Push\ToBePaid\CFT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CIEC.php.html#11">App\Models\Logic\Data\Push\ToBePaid\CIEC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#11">App\Models\Logic\Data\Push\ToBePaid\DESP2A6LA9</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2C637M.php.html#11">App\Models\Logic\Data\Push\ToBePaid\DESP2C637M</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#11">App\Models\Logic\Data\Push\ToBePaid\DESP2H8BBD</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBDW.php.html#11">App\Models\Logic\Data\Push\ToBePaid\GBDW</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HK.php.html#11">App\Models\Logic\Data\Push\ToBePaid\HK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MYCF.php.html#11">App\Models\Logic\Data\Push\ToBePaid\MYCF</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HR.php.html#11">App\Models\Logic\Data\Push\ToBePaid\HR</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSL.php.html#11">App\Models\Logic\Data\Push\ToBePaid\HSL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HZ.php.html#11">App\Models\Logic\Data\Push\ToBePaid\HZ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JF.php.html#11">App\Models\Logic\Data\Push\ToBePaid\JF</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTXY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\JTXY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\KY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LHYS.php.html#9">App\Models\Logic\Data\Push\ToBePaid\LHYS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LT.php.html#11">App\Models\Logic\Data\Push\ToBePaid\LT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MB.php.html#11">App\Models\Logic\Data\Push\ToBePaid\MB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\MY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_TJ.php.html#11">App\Models\Logic\Data\Push\ToBePaid\ZZ_TJ</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#16">App\Models\Logic\Data\Push\ToBePaid\AD</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ZJ.php.html#12">App\Models\Logic\Data\Push\ToBePaid\ZJ</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="RQ.php.html#10">App\Models\Logic\Data\Push\ToBePaid\RQ</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BBSP.php.html#13">App\Models\Logic\Data\Push\ToBePaid\BBSP</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="YBT.php.html#11">App\Models\Logic\Data\Push\ToBePaid\YBT</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\RY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SFSX.php.html#10">App\Models\Logic\Data\Push\ToBePaid\SFSX</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SHENGMAN.php.html#11">App\Models\Logic\Data\Push\ToBePaid\SHENGMAN</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SM.php.html#9">App\Models\Logic\Data\Push\ToBePaid\SM</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WSY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\WSY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WZYT.php.html#11">App\Models\Logic\Data\Push\ToBePaid\WZYT</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="XM.php.html#11">App\Models\Logic\Data\Push\ToBePaid\XM</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="XYDS.php.html#11">App\Models\Logic\Data\Push\ToBePaid\XYDS</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YXT.php.html#11">App\Models\Logic\Data\Push\ToBePaid\YXT</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YGJ.php.html#10">App\Models\Logic\Data\Push\ToBePaid\YGJ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YLZ.php.html#11">App\Models\Logic\Data\Push\ToBePaid\YLZ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZEY.php.html#12">App\Models\Logic\Data\Push\ToBePaid\ZEY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZJKA.php.html#9">App\Models\Logic\Data\Push\ToBePaid\ZJKA</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZLGX.php.html#10">App\Models\Logic\Data\Push\ToBePaid\ZLGX</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZT.php.html#11">App\Models\Logic\Data\Push\ToBePaid\ZT</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZTO.php.html#11">App\Models\Logic\Data\Push\ToBePaid\ZTO</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ.php.html#11">App\Models\Logic\Data\Push\ToBePaid\ZZ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ_AH.php.html#11">App\Models\Logic\Data\Push\ToBePaid\ZZ_AH</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ_BJ.php.html#11">App\Models\Logic\Data\Push\ToBePaid\ZZ_BJ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RRS.php.html#11">App\Models\Logic\Data\Push\ToBePaid\RRS</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PCKJ.php.html#10">App\Models\Logic\Data\Push\ToBePaid\PCKJ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QDMY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\QDMY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HK.php.html#11">App\Models\Logic\Data\Push\ToBePaid\HK</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BDT_ORG.php.html#10">App\Models\Logic\Data\Push\ToBePaid\BDT_ORG</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BQ.php.html#11">App\Models\Logic\Data\Push\ToBePaid\BQ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CFHY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\CFHY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CFT.php.html#11">App\Models\Logic\Data\Push\ToBePaid\CFT</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CIEC.php.html#11">App\Models\Logic\Data\Push\ToBePaid\CIEC</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#11">App\Models\Logic\Data\Push\ToBePaid\DESP2A6LA9</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DESP2C637M.php.html#11">App\Models\Logic\Data\Push\ToBePaid\DESP2C637M</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#11">App\Models\Logic\Data\Push\ToBePaid\DESP2H8BBD</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="GBDW.php.html#11">App\Models\Logic\Data\Push\ToBePaid\GBDW</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HG.php.html#9">App\Models\Logic\Data\Push\ToBePaid\HG</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HR.php.html#11">App\Models\Logic\Data\Push\ToBePaid\HR</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AJSW.php.html#11">App\Models\Logic\Data\Push\ToBePaid\AJSW</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HSL.php.html#11">App\Models\Logic\Data\Push\ToBePaid\HSL</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HZ.php.html#11">App\Models\Logic\Data\Push\ToBePaid\HZ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JF.php.html#11">App\Models\Logic\Data\Push\ToBePaid\JF</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JTXY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\JTXY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="KY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\KY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="LHYS.php.html#9">App\Models\Logic\Data\Push\ToBePaid\LHYS</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="LT.php.html#11">App\Models\Logic\Data\Push\ToBePaid\LT</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MB.php.html#11">App\Models\Logic\Data\Push\ToBePaid\MB</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MY.php.html#11">App\Models\Logic\Data\Push\ToBePaid\MY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MYCF.php.html#11">App\Models\Logic\Data\Push\ToBePaid\MYCF</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ_TJ.php.html#11">App\Models\Logic\Data\Push\ToBePaid\ZZ_TJ</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#25"><abbr title="App\Models\Logic\Data\Push\ToBePaid\AD::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGJ.php.html#18"><abbr title="App\Models\Logic\Data\Push\ToBePaid\YGJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RRS.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\RRS::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\RY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFSX.php.html#18"><abbr title="App\Models\Logic\Data\Push\ToBePaid\SFSX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHENGMAN.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\SHENGMAN::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SM.php.html#17"><abbr title="App\Models\Logic\Data\Push\ToBePaid\SM::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WSY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\WSY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WZYT.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\WZYT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XM.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\XM::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XYDS.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\XYDS::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YBT.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\YBT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YLZ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\YLZ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QDMY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\QDMY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YXT.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\YXT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZEY.php.html#21"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZEY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZJ.php.html#20"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZJKA.php.html#17"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZJKA::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZLGX.php.html#18"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZLGX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZT.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZTO.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZTO::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZZ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_AH.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZZ_AH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_BJ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZZ_BJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RQ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\RQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PCKJ.php.html#18"><abbr title="App\Models\Logic\Data\Push\ToBePaid\PCKJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AJSW.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\AJSW::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HG.php.html#17"><abbr title="App\Models\Logic\Data\Push\ToBePaid\HG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BBSP.php.html#21"><abbr title="App\Models\Logic\Data\Push\ToBePaid\BBSP::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BDT_ORG.php.html#18"><abbr title="App\Models\Logic\Data\Push\ToBePaid\BDT_ORG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BQ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\BQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CFHY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\CFHY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CFT.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\CFT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CIEC.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\CIEC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\DESP2A6LA9::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2C637M.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\DESP2C637M::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\DESP2H8BBD::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBDW.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\GBDW::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HK.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\HK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MYCF.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\MYCF::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HR.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\HR::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSL.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\HSL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HZ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\HZ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JF.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\JF::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTXY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\JTXY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\KY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LHYS.php.html#17"><abbr title="App\Models\Logic\Data\Push\ToBePaid\LHYS::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LT.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\LT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MB.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\MB::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\MY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_TJ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZZ_TJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#25"><abbr title="App\Models\Logic\Data\Push\ToBePaid\AD::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ZJ.php.html#20"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZJ::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="RQ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\RQ::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BBSP.php.html#21"><abbr title="App\Models\Logic\Data\Push\ToBePaid\BBSP::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="YBT.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\YBT::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\RY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SFSX.php.html#18"><abbr title="App\Models\Logic\Data\Push\ToBePaid\SFSX::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SHENGMAN.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\SHENGMAN::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SM.php.html#17"><abbr title="App\Models\Logic\Data\Push\ToBePaid\SM::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WSY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\WSY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WZYT.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\WZYT::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="XM.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\XM::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="XYDS.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\XYDS::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YXT.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\YXT::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YGJ.php.html#18"><abbr title="App\Models\Logic\Data\Push\ToBePaid\YGJ::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YLZ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\YLZ::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZEY.php.html#21"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZEY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZJKA.php.html#17"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZJKA::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZLGX.php.html#18"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZLGX::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZT.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZT::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZTO.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZTO::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZZ::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ_AH.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZZ_AH::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ_BJ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZZ_BJ::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RRS.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\RRS::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PCKJ.php.html#18"><abbr title="App\Models\Logic\Data\Push\ToBePaid\PCKJ::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QDMY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\QDMY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HK.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\HK::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BDT_ORG.php.html#18"><abbr title="App\Models\Logic\Data\Push\ToBePaid\BDT_ORG::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BQ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\BQ::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CFHY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\CFHY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CFT.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\CFT::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CIEC.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\CIEC::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\DESP2A6LA9::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DESP2C637M.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\DESP2C637M::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\DESP2H8BBD::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="GBDW.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\GBDW::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HG.php.html#17"><abbr title="App\Models\Logic\Data\Push\ToBePaid\HG::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HR.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\HR::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AJSW.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\AJSW::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HSL.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\HSL::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HZ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\HZ::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JF.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\JF::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JTXY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\JTXY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="KY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\KY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="LHYS.php.html#17"><abbr title="App\Models\Logic\Data\Push\ToBePaid\LHYS::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="LT.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\LT::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MB.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\MB::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MY.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\MY::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MYCF.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\MYCF::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZZ_TJ.php.html#19"><abbr title="App\Models\Logic\Data\Push\ToBePaid\ZZ_TJ::handle">handle</abbr></a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:21:05 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([51,0,0,0,0,0,0,0,0,0,0,1], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([51,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,10,"<a href=\"AD.php.html#16\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\AD<\/a>"],[0,3,"<a href=\"AJSW.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\AJSW<\/a>"],[0,5,"<a href=\"BBSP.php.html#13\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\BBSP<\/a>"],[0,3,"<a href=\"BDT_ORG.php.html#10\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\BDT_ORG<\/a>"],[0,3,"<a href=\"BQ.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\BQ<\/a>"],[0,3,"<a href=\"CFHY.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\CFHY<\/a>"],[0,3,"<a href=\"CFT.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\CFT<\/a>"],[0,3,"<a href=\"CIEC.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\CIEC<\/a>"],[0,3,"<a href=\"DESP2A6LA9.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\DESP2A6LA9<\/a>"],[0,3,"<a href=\"DESP2C637M.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\DESP2C637M<\/a>"],[0,3,"<a href=\"DESP2H8BBD.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\DESP2H8BBD<\/a>"],[0,3,"<a href=\"GBDW.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\GBDW<\/a>"],[0,3,"<a href=\"HG.php.html#9\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\HG<\/a>"],[0,3,"<a href=\"HK.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\HK<\/a>"],[0,3,"<a href=\"HR.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\HR<\/a>"],[0,3,"<a href=\"HSL.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\HSL<\/a>"],[0,3,"<a href=\"HZ.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\HZ<\/a>"],[0,3,"<a href=\"JF.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\JF<\/a>"],[0,3,"<a href=\"JTXY.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\JTXY<\/a>"],[0,3,"<a href=\"KY.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\KY<\/a>"],[0,3,"<a href=\"LHYS.php.html#9\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\LHYS<\/a>"],[0,3,"<a href=\"LT.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\LT<\/a>"],[0,3,"<a href=\"MB.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\MB<\/a>"],[0,3,"<a href=\"MY.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\MY<\/a>"],[0,3,"<a href=\"MYCF.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\MYCF<\/a>"],[0,3,"<a href=\"PCKJ.php.html#10\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\PCKJ<\/a>"],[0,3,"<a href=\"QDMY.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\QDMY<\/a>"],[0,6,"<a href=\"RQ.php.html#10\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\RQ<\/a>"],[0,3,"<a href=\"RRS.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\RRS<\/a>"],[0,3,"<a href=\"RY.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\RY<\/a>"],[0,3,"<a href=\"SFSX.php.html#10\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\SFSX<\/a>"],[0,3,"<a href=\"SHENGMAN.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\SHENGMAN<\/a>"],[0,3,"<a href=\"SM.php.html#9\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\SM<\/a>"],[100,0,"<a href=\"ThirdParty.php.html#9\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ThirdParty<\/a>"],[0,3,"<a href=\"WSY.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\WSY<\/a>"],[0,3,"<a href=\"WZYT.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\WZYT<\/a>"],[0,3,"<a href=\"XM.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\XM<\/a>"],[0,3,"<a href=\"XYDS.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\XYDS<\/a>"],[0,3,"<a href=\"YBT.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\YBT<\/a>"],[0,3,"<a href=\"YGJ.php.html#10\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\YGJ<\/a>"],[0,3,"<a href=\"YLZ.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\YLZ<\/a>"],[0,3,"<a href=\"YXT.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\YXT<\/a>"],[0,3,"<a href=\"ZEY.php.html#12\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZEY<\/a>"],[0,7,"<a href=\"ZJ.php.html#12\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZJ<\/a>"],[0,3,"<a href=\"ZJKA.php.html#9\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZJKA<\/a>"],[0,3,"<a href=\"ZLGX.php.html#10\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZLGX<\/a>"],[0,3,"<a href=\"ZT.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZT<\/a>"],[0,3,"<a href=\"ZTO.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZTO<\/a>"],[0,3,"<a href=\"ZZ.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZZ<\/a>"],[0,3,"<a href=\"ZZ_AH.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZZ_AH<\/a>"],[0,3,"<a href=\"ZZ_BJ.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZZ_BJ<\/a>"],[0,3,"<a href=\"ZZ_TJ.php.html#11\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZZ_TJ<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,10,"<a href=\"AD.php.html#25\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\AD::handle<\/a>"],[0,3,"<a href=\"AJSW.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\AJSW::handle<\/a>"],[0,5,"<a href=\"BBSP.php.html#21\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\BBSP::handle<\/a>"],[0,3,"<a href=\"BDT_ORG.php.html#18\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\BDT_ORG::handle<\/a>"],[0,3,"<a href=\"BQ.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\BQ::handle<\/a>"],[0,3,"<a href=\"CFHY.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\CFHY::handle<\/a>"],[0,3,"<a href=\"CFT.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\CFT::handle<\/a>"],[0,3,"<a href=\"CIEC.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\CIEC::handle<\/a>"],[0,3,"<a href=\"DESP2A6LA9.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\DESP2A6LA9::handle<\/a>"],[0,3,"<a href=\"DESP2C637M.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\DESP2C637M::handle<\/a>"],[0,3,"<a href=\"DESP2H8BBD.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\DESP2H8BBD::handle<\/a>"],[0,3,"<a href=\"GBDW.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\GBDW::handle<\/a>"],[0,3,"<a href=\"HG.php.html#17\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\HG::handle<\/a>"],[0,3,"<a href=\"HK.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\HK::handle<\/a>"],[0,3,"<a href=\"HR.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\HR::handle<\/a>"],[0,3,"<a href=\"HSL.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\HSL::handle<\/a>"],[0,3,"<a href=\"HZ.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\HZ::handle<\/a>"],[0,3,"<a href=\"JF.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\JF::handle<\/a>"],[0,3,"<a href=\"JTXY.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\JTXY::handle<\/a>"],[0,3,"<a href=\"KY.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\KY::handle<\/a>"],[0,3,"<a href=\"LHYS.php.html#17\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\LHYS::handle<\/a>"],[0,3,"<a href=\"LT.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\LT::handle<\/a>"],[0,3,"<a href=\"MB.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\MB::handle<\/a>"],[0,3,"<a href=\"MY.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\MY::handle<\/a>"],[0,3,"<a href=\"MYCF.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\MYCF::handle<\/a>"],[0,3,"<a href=\"PCKJ.php.html#18\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\PCKJ::handle<\/a>"],[0,3,"<a href=\"QDMY.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\QDMY::handle<\/a>"],[0,6,"<a href=\"RQ.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\RQ::handle<\/a>"],[0,3,"<a href=\"RRS.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\RRS::handle<\/a>"],[0,3,"<a href=\"RY.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\RY::handle<\/a>"],[0,3,"<a href=\"SFSX.php.html#18\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\SFSX::handle<\/a>"],[0,3,"<a href=\"SHENGMAN.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\SHENGMAN::handle<\/a>"],[0,3,"<a href=\"SM.php.html#17\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\SM::handle<\/a>"],[0,3,"<a href=\"WSY.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\WSY::handle<\/a>"],[0,3,"<a href=\"WZYT.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\WZYT::handle<\/a>"],[0,3,"<a href=\"XM.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\XM::handle<\/a>"],[0,3,"<a href=\"XYDS.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\XYDS::handle<\/a>"],[0,3,"<a href=\"YBT.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\YBT::handle<\/a>"],[0,3,"<a href=\"YGJ.php.html#18\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\YGJ::handle<\/a>"],[0,3,"<a href=\"YLZ.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\YLZ::handle<\/a>"],[0,3,"<a href=\"YXT.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\YXT::handle<\/a>"],[0,3,"<a href=\"ZEY.php.html#21\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZEY::handle<\/a>"],[0,7,"<a href=\"ZJ.php.html#20\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZJ::handle<\/a>"],[0,3,"<a href=\"ZJKA.php.html#17\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZJKA::handle<\/a>"],[0,3,"<a href=\"ZLGX.php.html#18\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZLGX::handle<\/a>"],[0,3,"<a href=\"ZT.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZT::handle<\/a>"],[0,3,"<a href=\"ZTO.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZTO::handle<\/a>"],[0,3,"<a href=\"ZZ.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZZ::handle<\/a>"],[0,3,"<a href=\"ZZ_AH.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZZ_AH::handle<\/a>"],[0,3,"<a href=\"ZZ_BJ.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZZ_BJ::handle<\/a>"],[0,3,"<a href=\"ZZ_TJ.php.html#19\">App\\Models\\Logic\\Data\\Push\\ToBePaid\\ZZ_TJ::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
