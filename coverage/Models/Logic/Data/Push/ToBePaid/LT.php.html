<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Logic/Data/Push/ToBePaid/LT.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/octicons.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Logic</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Data</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Push</a></li>
         <li class="breadcrumb-item"><a href="index.html">ToBePaid</a></li>
         <li class="breadcrumb-item active">LT.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;28</div></td>
      </tr>

      <tr>
       <td class="danger"><abbr title="App\Models\Logic\Data\Push\ToBePaid\LT">LT</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">12.00</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;28</div></td>
      </tr>

      <tr>
       <td class="danger" colspan="4">&nbsp;<a href="#19"><abbr title="handle()">handle</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">12.00</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;28</div></td>
      </tr>


     </tbody>
    </table>
   </div>
   <table id="code" class="table table-borderless table-condensed">
    <tbody>
     <tr><td><div align="right"><a name="1"></a><a href="#1">1</a></div></td><td class="codeLine"><span class="default">&lt;?php</span></td></tr>
     <tr><td><div align="right"><a name="2"></a><a href="#2">2</a></div></td><td class="codeLine"></td></tr>
     <tr><td><div align="right"><a name="3"></a><a href="#3">3</a></div></td><td class="codeLine"></td></tr>
     <tr><td><div align="right"><a name="4"></a><a href="#4">4</a></div></td><td class="codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App</span><span class="default">\</span><span class="default">Models</span><span class="default">\</span><span class="default">Logic</span><span class="default">\</span><span class="default">Data</span><span class="default">\</span><span class="default">Push</span><span class="default">\</span><span class="default">ToBePaid</span><span class="keyword">;</span></td></tr>
     <tr><td><div align="right"><a name="5"></a><a href="#5">5</a></div></td><td class="codeLine"></td></tr>
     <tr><td><div align="right"><a name="6"></a><a href="#6">6</a></div></td><td class="codeLine"></td></tr>
     <tr><td><div align="right"><a name="7"></a><a href="#7">7</a></div></td><td class="codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Request</span><span class="default">\</span><span class="default">LT</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">LTRequest</span><span class="keyword">;</span></td></tr>
     <tr><td><div align="right"><a name="8"></a><a href="#8">8</a></div></td><td class="codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Throwable</span><span class="keyword">;</span></td></tr>
     <tr><td><div align="right"><a name="9"></a><a href="#9">9</a></div></td><td class="codeLine"></td></tr>
     <tr><td><div align="right"><a name="10"></a><a href="#10">10</a></div></td><td class="codeLine"></td></tr>
     <tr><td><div align="right"><a name="11"></a><a href="#11">11</a></div></td><td class="codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">LT</span><span class="default">&nbsp;</span><span class="keyword">extends</span><span class="default">&nbsp;</span><span class="default">ThirdParty</span></td></tr>
     <tr><td><div align="right"><a name="12"></a><a href="#12">12</a></div></td><td class="codeLine"><span class="keyword">{</span></td></tr>
     <tr><td><div align="right"><a name="13"></a><a href="#13">13</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
     <tr><td><div align="right"><a name="14"></a><a href="#14">14</a></div></td><td class="codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;Throwable</span></td></tr>
     <tr><td><div align="right"><a name="15"></a><a href="#15">15</a></div></td><td class="codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;---------------------------------------------------</span></td></tr>
     <tr><td><div align="right"><a name="16"></a><a href="#16">16</a></div></td><td class="codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@author&nbsp;zhudelei&nbsp;&lt;<EMAIL>&gt;</span></td></tr>
     <tr><td><div align="right"><a name="17"></a><a href="#17">17</a></div></td><td class="codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@since&nbsp;2019-07-12&nbsp;15:31</span></td></tr>
     <tr><td><div align="right"><a name="18"></a><a href="#18">18</a></div></td><td class="codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
     <tr><td><div align="right"><a name="19"></a><a href="#19">19</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">handle</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
     <tr><td><div align="right"><a name="20"></a><a href="#20">20</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="21"></a><a href="#21">21</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$requestData</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="22"></a><a href="#22">22</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$extends</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">json_decode</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">data</span><span class="keyword">[</span><span class="default">&quot;extends&quot;</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="23"></a><a href="#23">23</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$requestData</span><span class="keyword">[</span><span class="default">&quot;extends&quot;</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$extends</span><span class="keyword">[</span><span class="default">'extends'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="24"></a><a href="#24">24</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$requestData</span><span class="keyword">[</span><span class="default">&quot;station_id&quot;</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">data</span><span class="keyword">[</span><span class="default">&quot;gasStationId&quot;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="25"></a><a href="#25">25</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$requestData</span><span class="keyword">[</span><span class="default">&quot;oil_num&quot;</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">(float)</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">data</span><span class="keyword">[</span><span class="default">&quot;realOilNum&quot;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="26"></a><a href="#26">26</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$requestData</span><span class="keyword">[</span><span class="default">&quot;price&quot;</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">(float)</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">data</span><span class="keyword">[</span><span class="default">&quot;realPrice&quot;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="27"></a><a href="#27">27</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$requestData</span><span class="keyword">[</span><span class="default">&quot;pay_price&quot;</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">(float)</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">data</span><span class="keyword">[</span><span class="default">&quot;realPrice&quot;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="28"></a><a href="#28">28</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$requestData</span><span class="keyword">[</span><span class="default">&quot;money&quot;</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">(float)</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">data</span><span class="keyword">[</span><span class="default">&quot;money&quot;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="29"></a><a href="#29">29</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$requestData</span><span class="keyword">[</span><span class="default">&quot;pay_money&quot;</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">data</span><span class="keyword">[</span><span class="default">&quot;money&quot;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="30"></a><a href="#30">30</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$requestData</span><span class="keyword">[</span><span class="default">&quot;trade_id&quot;</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">data</span><span class="keyword">[</span><span class="default">&quot;id&quot;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="31"></a><a href="#31">31</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$requestData</span><span class="keyword">[</span><span class="default">&quot;oil_type&quot;</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">data</span><span class="keyword">[</span><span class="default">&quot;gas_oil_type&quot;</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="32"></a><a href="#32">32</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$requestData</span><span class="keyword">[</span><span class="default">&quot;oil_name&quot;</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">data</span><span class="keyword">[</span><span class="default">&quot;gas_oil_name&quot;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="33"></a><a href="#33">33</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$requestData</span><span class="keyword">[</span><span class="default">&quot;oil_level&quot;</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">data</span><span class="keyword">[</span><span class="default">&quot;gas_oil_level&quot;</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="34"></a><a href="#34">34</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$orderAssocModel</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">createOrder</span><span class="keyword">(</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="35"></a><a href="#35">35</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">2</span><span class="keyword">,</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="36"></a><a href="#36">36</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">2</span><span class="keyword">,</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="37"></a><a href="#37">37</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">data</span><span class="keyword">[</span><span class="default">'id'</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="38"></a><a href="#38">38</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">''</span></td></tr>
     <tr><td><div align="right"><a name="39"></a><a href="#39">39</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
     <tr><td><div align="right"><a name="40"></a><a href="#40">40</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="41"></a><a href="#41">41</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$responseData</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">LTRequest</span><span class="default">::</span><span class="default">handle</span><span class="keyword">(</span><span class="default">&quot;&quot;</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$requestData</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="42"></a><a href="#42">42</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$orderAssocModel</span><span class="default">-&gt;</span><span class="default">platform_reason</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$responseData</span><span class="keyword">[</span><span class="default">'msg'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="43"></a><a href="#43">43</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$orderAssocModel</span><span class="default">-&gt;</span><span class="default">platform_order_id</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$responseData</span><span class="keyword">[</span><span class="default">&quot;data&quot;</span><span class="keyword">]</span><span class="keyword">[</span><span class="default">'order_id'</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="44"></a><a href="#44">44</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$orderAssocModel</span><span class="default">-&gt;</span><span class="default">save</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="45"></a><a href="#45">45</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Throwable</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="46"></a><a href="#46">46</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$exception</span><span class="default">-&gt;</span><span class="default">getCode</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">==</span><span class="default">&nbsp;</span><span class="default">5000999</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="47"></a><a href="#47">47</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$orderAssocModel</span><span class="default">-&gt;</span><span class="default">platform_reason</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="48"></a><a href="#48">48</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$orderAssocModel</span><span class="default">-&gt;</span><span class="default">save</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
     <tr><td><div align="right"><a name="49"></a><a href="#49">49</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
     <tr><td><div align="right"><a name="50"></a><a href="#50">50</a></div></td><td class="codeLine"></td></tr>
     <tr class="danger"><td><div align="right"><a name="51"></a><a href="#51">51</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">;</span></td></tr>
     <tr><td><div align="right"><a name="52"></a><a href="#52">52</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="53"></a><a href="#53">53</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
     <tr><td><div align="right"><a name="54"></a><a href="#54">54</a></div></td><td class="codeLine"><span class="keyword">}</span></td></tr>

    </tbody>
   </table>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="success"><strong>Executed</strong></span>
     <span class="danger"><strong>Not Executed</strong></span>
     <span class="warning"><strong>Dead Code</strong></span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:21:05 CST 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../../../.js/popper.min.js" type="text/javascript"></script>
  <script src="../../../../../.js/bootstrap.min.js" type="text/javascript"></script>
  <script src="../../../../../.js/file.js" type="text/javascript"></script>
 </body>
</html>
