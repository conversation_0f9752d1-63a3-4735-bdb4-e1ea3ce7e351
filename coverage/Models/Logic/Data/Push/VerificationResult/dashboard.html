<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Logic/Data/Push/VerificationResult</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Logic</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Data</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Push</a></li>
         <li class="breadcrumb-item"><a href="index.html">VerificationResult</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#13">App\Models\Logic\Data\Push\VerificationResult\AD</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YBT.php.html#11">App\Models\Logic\Data\Push\VerificationResult\YBT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFSX.php.html#11">App\Models\Logic\Data\Push\VerificationResult\SFSX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHENGMAN.php.html#11">App\Models\Logic\Data\Push\VerificationResult\SHENGMAN</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SP.php.html#12">App\Models\Logic\Data\Push\VerificationResult\SP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#10">App\Models\Logic\Data\Push\VerificationResult\ThirdParty</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WSY.php.html#11">App\Models\Logic\Data\Push\VerificationResult\WSY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WZYT.php.html#11">App\Models\Logic\Data\Push\VerificationResult\WZYT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XM.php.html#11">App\Models\Logic\Data\Push\VerificationResult\XM</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XYDS.php.html#11">App\Models\Logic\Data\Push\VerificationResult\XYDS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YLZ.php.html#11">App\Models\Logic\Data\Push\VerificationResult\YLZ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RRS.php.html#11">App\Models\Logic\Data\Push\VerificationResult\RRS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YXT.php.html#11">App\Models\Logic\Data\Push\VerificationResult\YXT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZJ.php.html#11">App\Models\Logic\Data\Push\VerificationResult\ZJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZJKA.php.html#11">App\Models\Logic\Data\Push\VerificationResult\ZJKA</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZLGX.php.html#11">App\Models\Logic\Data\Push\VerificationResult\ZLGX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZT.php.html#11">App\Models\Logic\Data\Push\VerificationResult\ZT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ.php.html#11">App\Models\Logic\Data\Push\VerificationResult\ZZ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_AH.php.html#11">App\Models\Logic\Data\Push\VerificationResult\ZZ_AH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_BJ.php.html#11">App\Models\Logic\Data\Push\VerificationResult\ZZ_BJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RY.php.html#11">App\Models\Logic\Data\Push\VerificationResult\RY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QDMY.php.html#11">App\Models\Logic\Data\Push\VerificationResult\QDMY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AJSW.php.html#11">App\Models\Logic\Data\Push\VerificationResult\AJSW</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBDW.php.html#11">App\Models\Logic\Data\Push\VerificationResult\GBDW</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BQ.php.html#11">App\Models\Logic\Data\Push\VerificationResult\BQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CFHY.php.html#11">App\Models\Logic\Data\Push\VerificationResult\CFHY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CFT.php.html#11">App\Models\Logic\Data\Push\VerificationResult\CFT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CIEC.php.html#11">App\Models\Logic\Data\Push\VerificationResult\CIEC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#11">App\Models\Logic\Data\Push\VerificationResult\DESP2A6LA9</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2C637M.php.html#11">App\Models\Logic\Data\Push\VerificationResult\DESP2C637M</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#11">App\Models\Logic\Data\Push\VerificationResult\DESP2H8BBD</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FY.php.html#10">App\Models\Logic\Data\Push\VerificationResult\FY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HK.php.html#11">App\Models\Logic\Data\Push\VerificationResult\HK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MYCF.php.html#11">App\Models\Logic\Data\Push\VerificationResult\MYCF</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HLL.php.html#11">App\Models\Logic\Data\Push\VerificationResult\HLL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HR.php.html#11">App\Models\Logic\Data\Push\VerificationResult\HR</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSL.php.html#11">App\Models\Logic\Data\Push\VerificationResult\HSL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HZ.php.html#11">App\Models\Logic\Data\Push\VerificationResult\HZ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JF.php.html#11">App\Models\Logic\Data\Push\VerificationResult\JF</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTXY.php.html#11">App\Models\Logic\Data\Push\VerificationResult\JTXY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#11">App\Models\Logic\Data\Push\VerificationResult\KY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LT.php.html#11">App\Models\Logic\Data\Push\VerificationResult\LT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MY.php.html#11">App\Models\Logic\Data\Push\VerificationResult\MY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_TJ.php.html#11">App\Models\Logic\Data\Push\VerificationResult\ZZ_TJ</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#13">App\Models\Logic\Data\Push\VerificationResult\AD</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#20"><abbr title="App\Models\Logic\Data\Push\VerificationResult\AD::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YBT.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\YBT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SFSX.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\SFSX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHENGMAN.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\SHENGMAN::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SP.php.html#19"><abbr title="App\Models\Logic\Data\Push\VerificationResult\SP::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#12"><abbr title="App\Models\Logic\Data\Push\VerificationResult\ThirdParty::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WSY.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\WSY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WZYT.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\WZYT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XM.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\XM::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XYDS.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\XYDS::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YLZ.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\YLZ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RRS.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\RRS::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YXT.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\YXT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZJ.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\ZJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZJKA.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\ZJKA::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZLGX.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\ZLGX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZT.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\ZT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\ZZ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_AH.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\ZZ_AH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_BJ.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\ZZ_BJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RY.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\RY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QDMY.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\QDMY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AJSW.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\AJSW::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBDW.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\GBDW::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BQ.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\BQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CFHY.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\CFHY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CFT.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\CFT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CIEC.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\CIEC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2A6LA9.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\DESP2A6LA9::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2C637M.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\DESP2C637M::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DESP2H8BBD.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\DESP2H8BBD::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FY.php.html#17"><abbr title="App\Models\Logic\Data\Push\VerificationResult\FY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HK.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\HK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MYCF.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\MYCF::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HLL.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\HLL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HR.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\HR::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSL.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\HSL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HZ.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\HZ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JF.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\JF::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTXY.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\JTXY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="KY.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\KY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LT.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\LT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MY.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\MY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZZ_TJ.php.html#18"><abbr title="App\Models\Logic\Data\Push\VerificationResult\ZZ_TJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AD.php.html#20"><abbr title="App\Models\Logic\Data\Push\VerificationResult\AD::handle">handle</abbr></a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:21:05 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([44,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([44,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,3,"<a href=\"AD.php.html#13\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\AD<\/a>"],[0,1,"<a href=\"AJSW.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\AJSW<\/a>"],[0,1,"<a href=\"BQ.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\BQ<\/a>"],[0,1,"<a href=\"CFHY.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\CFHY<\/a>"],[0,1,"<a href=\"CFT.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\CFT<\/a>"],[0,1,"<a href=\"CIEC.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\CIEC<\/a>"],[0,1,"<a href=\"DESP2A6LA9.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\DESP2A6LA9<\/a>"],[0,1,"<a href=\"DESP2C637M.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\DESP2C637M<\/a>"],[0,1,"<a href=\"DESP2H8BBD.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\DESP2H8BBD<\/a>"],[0,1,"<a href=\"FY.php.html#10\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\FY<\/a>"],[0,1,"<a href=\"GBDW.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\GBDW<\/a>"],[0,1,"<a href=\"HK.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\HK<\/a>"],[0,1,"<a href=\"HLL.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\HLL<\/a>"],[0,1,"<a href=\"HR.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\HR<\/a>"],[0,1,"<a href=\"HSL.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\HSL<\/a>"],[0,1,"<a href=\"HZ.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\HZ<\/a>"],[0,1,"<a href=\"JF.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\JF<\/a>"],[0,1,"<a href=\"JTXY.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\JTXY<\/a>"],[0,1,"<a href=\"KY.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\KY<\/a>"],[0,1,"<a href=\"LT.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\LT<\/a>"],[0,1,"<a href=\"MY.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\MY<\/a>"],[0,1,"<a href=\"MYCF.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\MYCF<\/a>"],[0,1,"<a href=\"QDMY.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\QDMY<\/a>"],[0,1,"<a href=\"RRS.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\RRS<\/a>"],[0,1,"<a href=\"RY.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\RY<\/a>"],[0,1,"<a href=\"SFSX.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\SFSX<\/a>"],[0,1,"<a href=\"SHENGMAN.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\SHENGMAN<\/a>"],[0,1,"<a href=\"SP.php.html#12\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\SP<\/a>"],[0,1,"<a href=\"ThirdParty.php.html#10\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ThirdParty<\/a>"],[0,1,"<a href=\"WSY.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\WSY<\/a>"],[0,1,"<a href=\"WZYT.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\WZYT<\/a>"],[0,1,"<a href=\"XM.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\XM<\/a>"],[0,1,"<a href=\"XYDS.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\XYDS<\/a>"],[0,1,"<a href=\"YBT.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\YBT<\/a>"],[0,1,"<a href=\"YLZ.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\YLZ<\/a>"],[0,1,"<a href=\"YXT.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\YXT<\/a>"],[0,1,"<a href=\"ZJ.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZJ<\/a>"],[0,1,"<a href=\"ZJKA.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZJKA<\/a>"],[0,1,"<a href=\"ZLGX.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZLGX<\/a>"],[0,1,"<a href=\"ZT.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZT<\/a>"],[0,1,"<a href=\"ZZ.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZZ<\/a>"],[0,1,"<a href=\"ZZ_AH.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZZ_AH<\/a>"],[0,1,"<a href=\"ZZ_BJ.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZZ_BJ<\/a>"],[0,1,"<a href=\"ZZ_TJ.php.html#11\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZZ_TJ<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,3,"<a href=\"AD.php.html#20\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\AD::handle<\/a>"],[0,1,"<a href=\"AJSW.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\AJSW::handle<\/a>"],[0,1,"<a href=\"BQ.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\BQ::handle<\/a>"],[0,1,"<a href=\"CFHY.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\CFHY::handle<\/a>"],[0,1,"<a href=\"CFT.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\CFT::handle<\/a>"],[0,1,"<a href=\"CIEC.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\CIEC::handle<\/a>"],[0,1,"<a href=\"DESP2A6LA9.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\DESP2A6LA9::handle<\/a>"],[0,1,"<a href=\"DESP2C637M.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\DESP2C637M::handle<\/a>"],[0,1,"<a href=\"DESP2H8BBD.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\DESP2H8BBD::handle<\/a>"],[0,1,"<a href=\"FY.php.html#17\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\FY::handle<\/a>"],[0,1,"<a href=\"GBDW.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\GBDW::handle<\/a>"],[0,1,"<a href=\"HK.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\HK::handle<\/a>"],[0,1,"<a href=\"HLL.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\HLL::handle<\/a>"],[0,1,"<a href=\"HR.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\HR::handle<\/a>"],[0,1,"<a href=\"HSL.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\HSL::handle<\/a>"],[0,1,"<a href=\"HZ.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\HZ::handle<\/a>"],[0,1,"<a href=\"JF.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\JF::handle<\/a>"],[0,1,"<a href=\"JTXY.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\JTXY::handle<\/a>"],[0,1,"<a href=\"KY.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\KY::handle<\/a>"],[0,1,"<a href=\"LT.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\LT::handle<\/a>"],[0,1,"<a href=\"MY.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\MY::handle<\/a>"],[0,1,"<a href=\"MYCF.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\MYCF::handle<\/a>"],[0,1,"<a href=\"QDMY.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\QDMY::handle<\/a>"],[0,1,"<a href=\"RRS.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\RRS::handle<\/a>"],[0,1,"<a href=\"RY.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\RY::handle<\/a>"],[0,1,"<a href=\"SFSX.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\SFSX::handle<\/a>"],[0,1,"<a href=\"SHENGMAN.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\SHENGMAN::handle<\/a>"],[0,1,"<a href=\"SP.php.html#19\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\SP::handle<\/a>"],[0,1,"<a href=\"ThirdParty.php.html#12\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ThirdParty::__construct<\/a>"],[0,1,"<a href=\"WSY.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\WSY::handle<\/a>"],[0,1,"<a href=\"WZYT.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\WZYT::handle<\/a>"],[0,1,"<a href=\"XM.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\XM::handle<\/a>"],[0,1,"<a href=\"XYDS.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\XYDS::handle<\/a>"],[0,1,"<a href=\"YBT.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\YBT::handle<\/a>"],[0,1,"<a href=\"YLZ.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\YLZ::handle<\/a>"],[0,1,"<a href=\"YXT.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\YXT::handle<\/a>"],[0,1,"<a href=\"ZJ.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZJ::handle<\/a>"],[0,1,"<a href=\"ZJKA.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZJKA::handle<\/a>"],[0,1,"<a href=\"ZLGX.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZLGX::handle<\/a>"],[0,1,"<a href=\"ZT.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZT::handle<\/a>"],[0,1,"<a href=\"ZZ.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZZ::handle<\/a>"],[0,1,"<a href=\"ZZ_AH.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZZ_AH::handle<\/a>"],[0,1,"<a href=\"ZZ_BJ.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZZ_BJ::handle<\/a>"],[0,1,"<a href=\"ZZ_TJ.php.html#18\">App\\Models\\Logic\\Data\\Push\\VerificationResult\\ZZ_TJ::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
