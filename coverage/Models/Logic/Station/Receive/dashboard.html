<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Logic/Station/Receive</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Logic</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Station</a></li>
         <li class="breadcrumb-item"><a href="index.html">Receive</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CY.php.html#12">App\Models\Logic\Station\Receive\CY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHSX.php.html#13">App\Models\Logic\Station\Receive\SHSX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHYK.php.html#13">App\Models\Logic\Station\Receive\ZHYK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#13">App\Models\Logic\Station\Receive\ZHUOYIY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#13">App\Models\Logic\Station\Receive\ZHUOYIQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZDC.php.html#14">App\Models\Logic\Station\Receive\ZDC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YUNDATONG.php.html#11">App\Models\Logic\Station\Receive\YUNDATONG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XYN.php.html#13">App\Models\Logic\Station\Receive\XYN</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XY.php.html#12">App\Models\Logic\Station\Receive\XY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XMSK.php.html#13">App\Models\Logic\Station\Receive\XMSK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WJY.php.html#13">App\Models\Logic\Station\Receive\WJY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#17">App\Models\Logic\Station\Receive\ThirdParty</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZSH.php.html#15">App\Models\Logic\Station\Receive\SQZSH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZL.php.html#13">App\Models\Logic\Station\Receive\SQZL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SP.php.html#11">App\Models\Logic\Station\Receive\SP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SH.php.html#13">App\Models\Logic\Station\Receive\SH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CYLNG.php.html#12">App\Models\Logic\Station\Receive\CYLNG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAIC.php.html#14">App\Models\Logic\Station\Receive\SAIC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QK.php.html#15">App\Models\Logic\Station\Receive\QK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Main.php.html#11">App\Models\Logic\Station\Receive\Main</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MTLSY.php.html#14">App\Models\Logic\Station\Receive\MTLSY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MJ.php.html#13">App\Models\Logic\Station\Receive\MJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTX.php.html#15">App\Models\Logic\Station\Receive\JTX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JT.php.html#15">App\Models\Logic\Station\Receive\JT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JQ.php.html#14">App\Models\Logic\Station\Receive\JQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JH.php.html#15">App\Models\Logic\Station\Receive\JH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSY.php.html#14">App\Models\Logic\Station\Receive\HSY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GB.php.html#14">App\Models\Logic\Station\Receive\GB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GAODENG.php.html#13">App\Models\Logic\Station\Receive\GAODENG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EZT.php.html#12">App\Models\Logic\Station\Receive\EZT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DIANDI.php.html#12">App\Models\Logic\Station\Receive\DIANDI</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZY.php.html#13">App\Models\Logic\Station\Receive\ZY</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ThirdParty.php.html#17">App\Models\Logic\Station\Receive\ThirdParty</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="JH.php.html#15">App\Models\Logic\Station\Receive\JH</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="JT.php.html#15">App\Models\Logic\Station\Receive\JT</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="JTX.php.html#15">App\Models\Logic\Station\Receive\JTX</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="QK.php.html#15">App\Models\Logic\Station\Receive\QK</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="ZY.php.html#13">App\Models\Logic\Station\Receive\ZY</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="XYN.php.html#13">App\Models\Logic\Station\Receive\XYN</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="MJ.php.html#13">App\Models\Logic\Station\Receive\MJ</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="SP.php.html#11">App\Models\Logic\Station\Receive\SP</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="SH.php.html#13">App\Models\Logic\Station\Receive\SH</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="ZHYK.php.html#13">App\Models\Logic\Station\Receive\ZHYK</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="YUNDATONG.php.html#11">App\Models\Logic\Station\Receive\YUNDATONG</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="SHSX.php.html#13">App\Models\Logic\Station\Receive\SHSX</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="SQZSH.php.html#15">App\Models\Logic\Station\Receive\SQZSH</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="XMSK.php.html#13">App\Models\Logic\Station\Receive\XMSK</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="XY.php.html#12">App\Models\Logic\Station\Receive\XY</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="GAODENG.php.html#13">App\Models\Logic\Station\Receive\GAODENG</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="EZT.php.html#12">App\Models\Logic\Station\Receive\EZT</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#13">App\Models\Logic\Station\Receive\ZHUOYIQ</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#13">App\Models\Logic\Station\Receive\ZHUOYIY</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="DIANDI.php.html#12">App\Models\Logic\Station\Receive\DIANDI</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="CY.php.html#12">App\Models\Logic\Station\Receive\CY</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="CYLNG.php.html#12">App\Models\Logic\Station\Receive\CYLNG</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="SQZL.php.html#13">App\Models\Logic\Station\Receive\SQZL</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WJY.php.html#13">App\Models\Logic\Station\Receive\WJY</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Main.php.html#11">App\Models\Logic\Station\Receive\Main</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SAIC.php.html#14">App\Models\Logic\Station\Receive\SAIC</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JQ.php.html#14">App\Models\Logic\Station\Receive\JQ</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ZDC.php.html#14">App\Models\Logic\Station\Receive\ZDC</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CY.php.html#22"><abbr title="App\Models\Logic\Station\Receive\CY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XMSK.php.html#22"><abbr title="App\Models\Logic\Station\Receive\XMSK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZL.php.html#22"><abbr title="App\Models\Logic\Station\Receive\SQZL::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZSH.php.html#24"><abbr title="App\Models\Logic\Station\Receive\SQZSH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#33"><abbr title="App\Models\Logic\Station\Receive\ThirdParty::validateAreaCode">validateAreaCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#56"><abbr title="App\Models\Logic\Station\Receive\ThirdParty::validateRegionCode">validateRegionCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty.php.html#112"><abbr title="App\Models\Logic\Station\Receive\ThirdParty::pushStationToStationCenter">pushStationToStationCenter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WJY.php.html#22"><abbr title="App\Models\Logic\Station\Receive\WJY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XY.php.html#22"><abbr title="App\Models\Logic\Station\Receive\XY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHSX.php.html#22"><abbr title="App\Models\Logic\Station\Receive\SHSX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XYN.php.html#23"><abbr title="App\Models\Logic\Station\Receive\XYN::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YUNDATONG.php.html#21"><abbr title="App\Models\Logic\Station\Receive\YUNDATONG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZDC.php.html#23"><abbr title="App\Models\Logic\Station\Receive\ZDC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#23"><abbr title="App\Models\Logic\Station\Receive\ZHUOYIQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#23"><abbr title="App\Models\Logic\Station\Receive\ZHUOYIY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHYK.php.html#23"><abbr title="App\Models\Logic\Station\Receive\ZHYK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SP.php.html#33"><abbr title="App\Models\Logic\Station\Receive\SP::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SH.php.html#22"><abbr title="App\Models\Logic\Station\Receive\SH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CYLNG.php.html#22"><abbr title="App\Models\Logic\Station\Receive\CYLNG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JQ.php.html#23"><abbr title="App\Models\Logic\Station\Receive\JQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DIANDI.php.html#22"><abbr title="App\Models\Logic\Station\Receive\DIANDI::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EZT.php.html#22"><abbr title="App\Models\Logic\Station\Receive\EZT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GAODENG.php.html#22"><abbr title="App\Models\Logic\Station\Receive\GAODENG::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GB.php.html#23"><abbr title="App\Models\Logic\Station\Receive\GB::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSY.php.html#23"><abbr title="App\Models\Logic\Station\Receive\HSY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JH.php.html#24"><abbr title="App\Models\Logic\Station\Receive\JH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JT.php.html#24"><abbr title="App\Models\Logic\Station\Receive\JT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAIC.php.html#23"><abbr title="App\Models\Logic\Station\Receive\SAIC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTX.php.html#24"><abbr title="App\Models\Logic\Station\Receive\JTX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MJ.php.html#22"><abbr title="App\Models\Logic\Station\Receive\MJ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MTLSY.php.html#23"><abbr title="App\Models\Logic\Station\Receive\MTLSY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Main.php.html#51"><abbr title="App\Models\Logic\Station\Receive\Main::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Main.php.html#64"><abbr title="App\Models\Logic\Station\Receive\Main::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QK.php.html#24"><abbr title="App\Models\Logic\Station\Receive\QK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZY.php.html#37"><abbr title="App\Models\Logic\Station\Receive\ZY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="JT.php.html#24"><abbr title="App\Models\Logic\Station\Receive\JT::handle">handle</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="QK.php.html#24"><abbr title="App\Models\Logic\Station\Receive\QK::handle">handle</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="JH.php.html#24"><abbr title="App\Models\Logic\Station\Receive\JH::handle">handle</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="JTX.php.html#24"><abbr title="App\Models\Logic\Station\Receive\JTX::handle">handle</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="ZY.php.html#37"><abbr title="App\Models\Logic\Station\Receive\ZY::handle">handle</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="XYN.php.html#23"><abbr title="App\Models\Logic\Station\Receive\XYN::handle">handle</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="MJ.php.html#22"><abbr title="App\Models\Logic\Station\Receive\MJ::handle">handle</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="SP.php.html#33"><abbr title="App\Models\Logic\Station\Receive\SP::handle">handle</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="ZHYK.php.html#23"><abbr title="App\Models\Logic\Station\Receive\ZHYK::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="SH.php.html#22"><abbr title="App\Models\Logic\Station\Receive\SH::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="SHSX.php.html#22"><abbr title="App\Models\Logic\Station\Receive\SHSX::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="YUNDATONG.php.html#21"><abbr title="App\Models\Logic\Station\Receive\YUNDATONG::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="SQZSH.php.html#24"><abbr title="App\Models\Logic\Station\Receive\SQZSH::handle">handle</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ThirdParty.php.html#56"><abbr title="App\Models\Logic\Station\Receive\ThirdParty::validateRegionCode">validateRegionCode</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ThirdParty.php.html#112"><abbr title="App\Models\Logic\Station\Receive\ThirdParty::pushStationToStationCenter">pushStationToStationCenter</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="XY.php.html#22"><abbr title="App\Models\Logic\Station\Receive\XY::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="XMSK.php.html#22"><abbr title="App\Models\Logic\Station\Receive\XMSK::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ZHUOYIY.php.html#23"><abbr title="App\Models\Logic\Station\Receive\ZHUOYIY::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ZHUOYIQ.php.html#23"><abbr title="App\Models\Logic\Station\Receive\ZHUOYIQ::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="DIANDI.php.html#22"><abbr title="App\Models\Logic\Station\Receive\DIANDI::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="CY.php.html#22"><abbr title="App\Models\Logic\Station\Receive\CY::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="GAODENG.php.html#22"><abbr title="App\Models\Logic\Station\Receive\GAODENG::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="EZT.php.html#22"><abbr title="App\Models\Logic\Station\Receive\EZT::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="CYLNG.php.html#22"><abbr title="App\Models\Logic\Station\Receive\CYLNG::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="SQZL.php.html#22"><abbr title="App\Models\Logic\Station\Receive\SQZL::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WJY.php.html#22"><abbr title="App\Models\Logic\Station\Receive\WJY::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ThirdParty.php.html#33"><abbr title="App\Models\Logic\Station\Receive\ThirdParty::validateAreaCode">validateAreaCode</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ZDC.php.html#23"><abbr title="App\Models\Logic\Station\Receive\ZDC::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SAIC.php.html#23"><abbr title="App\Models\Logic\Station\Receive\SAIC::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Main.php.html#64"><abbr title="App\Models\Logic\Station\Receive\Main::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JQ.php.html#23"><abbr title="App\Models\Logic\Station\Receive\JQ::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:21:05 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([32,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([35,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,8,"<a href=\"CY.php.html#12\">App\\Models\\Logic\\Station\\Receive\\CY<\/a>"],[0,7,"<a href=\"CYLNG.php.html#12\">App\\Models\\Logic\\Station\\Receive\\CYLNG<\/a>"],[0,8,"<a href=\"DIANDI.php.html#12\">App\\Models\\Logic\\Station\\Receive\\DIANDI<\/a>"],[0,8,"<a href=\"EZT.php.html#12\">App\\Models\\Logic\\Station\\Receive\\EZT<\/a>"],[0,8,"<a href=\"GAODENG.php.html#13\">App\\Models\\Logic\\Station\\Receive\\GAODENG<\/a>"],[0,1,"<a href=\"GB.php.html#14\">App\\Models\\Logic\\Station\\Receive\\GB<\/a>"],[0,1,"<a href=\"HSY.php.html#14\">App\\Models\\Logic\\Station\\Receive\\HSY<\/a>"],[0,20,"<a href=\"JH.php.html#15\">App\\Models\\Logic\\Station\\Receive\\JH<\/a>"],[0,2,"<a href=\"JQ.php.html#14\">App\\Models\\Logic\\Station\\Receive\\JQ<\/a>"],[0,20,"<a href=\"JT.php.html#15\">App\\Models\\Logic\\Station\\Receive\\JT<\/a>"],[0,20,"<a href=\"JTX.php.html#15\">App\\Models\\Logic\\Station\\Receive\\JTX<\/a>"],[0,17,"<a href=\"MJ.php.html#13\">App\\Models\\Logic\\Station\\Receive\\MJ<\/a>"],[0,1,"<a href=\"MTLSY.php.html#14\">App\\Models\\Logic\\Station\\Receive\\MTLSY<\/a>"],[0,3,"<a href=\"Main.php.html#11\">App\\Models\\Logic\\Station\\Receive\\Main<\/a>"],[0,20,"<a href=\"QK.php.html#15\">App\\Models\\Logic\\Station\\Receive\\QK<\/a>"],[0,2,"<a href=\"SAIC.php.html#14\">App\\Models\\Logic\\Station\\Receive\\SAIC<\/a>"],[0,14,"<a href=\"SH.php.html#13\">App\\Models\\Logic\\Station\\Receive\\SH<\/a>"],[0,14,"<a href=\"SHSX.php.html#13\">App\\Models\\Logic\\Station\\Receive\\SHSX<\/a>"],[0,17,"<a href=\"SP.php.html#11\">App\\Models\\Logic\\Station\\Receive\\SP<\/a>"],[0,6,"<a href=\"SQZL.php.html#13\">App\\Models\\Logic\\Station\\Receive\\SQZL<\/a>"],[0,12,"<a href=\"SQZSH.php.html#15\">App\\Models\\Logic\\Station\\Receive\\SQZSH<\/a>"],[0,25,"<a href=\"ThirdParty.php.html#17\">App\\Models\\Logic\\Station\\Receive\\ThirdParty<\/a>"],[0,5,"<a href=\"WJY.php.html#13\">App\\Models\\Logic\\Station\\Receive\\WJY<\/a>"],[0,9,"<a href=\"XMSK.php.html#13\">App\\Models\\Logic\\Station\\Receive\\XMSK<\/a>"],[0,9,"<a href=\"XY.php.html#12\">App\\Models\\Logic\\Station\\Receive\\XY<\/a>"],[0,17,"<a href=\"XYN.php.html#13\">App\\Models\\Logic\\Station\\Receive\\XYN<\/a>"],[0,14,"<a href=\"YUNDATONG.php.html#11\">App\\Models\\Logic\\Station\\Receive\\YUNDATONG<\/a>"],[0,2,"<a href=\"ZDC.php.html#14\">App\\Models\\Logic\\Station\\Receive\\ZDC<\/a>"],[0,8,"<a href=\"ZHUOYIQ.php.html#13\">App\\Models\\Logic\\Station\\Receive\\ZHUOYIQ<\/a>"],[0,8,"<a href=\"ZHUOYIY.php.html#13\">App\\Models\\Logic\\Station\\Receive\\ZHUOYIY<\/a>"],[0,14,"<a href=\"ZHYK.php.html#13\">App\\Models\\Logic\\Station\\Receive\\ZHYK<\/a>"],[0,17,"<a href=\"ZY.php.html#13\">App\\Models\\Logic\\Station\\Receive\\ZY<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,8,"<a href=\"CY.php.html#22\">App\\Models\\Logic\\Station\\Receive\\CY::handle<\/a>"],[0,7,"<a href=\"CYLNG.php.html#22\">App\\Models\\Logic\\Station\\Receive\\CYLNG::handle<\/a>"],[0,8,"<a href=\"DIANDI.php.html#22\">App\\Models\\Logic\\Station\\Receive\\DIANDI::handle<\/a>"],[0,8,"<a href=\"EZT.php.html#22\">App\\Models\\Logic\\Station\\Receive\\EZT::handle<\/a>"],[0,8,"<a href=\"GAODENG.php.html#22\">App\\Models\\Logic\\Station\\Receive\\GAODENG::handle<\/a>"],[0,1,"<a href=\"GB.php.html#23\">App\\Models\\Logic\\Station\\Receive\\GB::handle<\/a>"],[0,1,"<a href=\"HSY.php.html#23\">App\\Models\\Logic\\Station\\Receive\\HSY::handle<\/a>"],[0,20,"<a href=\"JH.php.html#24\">App\\Models\\Logic\\Station\\Receive\\JH::handle<\/a>"],[0,2,"<a href=\"JQ.php.html#23\">App\\Models\\Logic\\Station\\Receive\\JQ::handle<\/a>"],[0,20,"<a href=\"JT.php.html#24\">App\\Models\\Logic\\Station\\Receive\\JT::handle<\/a>"],[0,20,"<a href=\"JTX.php.html#24\">App\\Models\\Logic\\Station\\Receive\\JTX::handle<\/a>"],[0,17,"<a href=\"MJ.php.html#22\">App\\Models\\Logic\\Station\\Receive\\MJ::handle<\/a>"],[0,1,"<a href=\"MTLSY.php.html#23\">App\\Models\\Logic\\Station\\Receive\\MTLSY::handle<\/a>"],[0,1,"<a href=\"Main.php.html#51\">App\\Models\\Logic\\Station\\Receive\\Main::__construct<\/a>"],[0,2,"<a href=\"Main.php.html#64\">App\\Models\\Logic\\Station\\Receive\\Main::handle<\/a>"],[0,20,"<a href=\"QK.php.html#24\">App\\Models\\Logic\\Station\\Receive\\QK::handle<\/a>"],[0,2,"<a href=\"SAIC.php.html#23\">App\\Models\\Logic\\Station\\Receive\\SAIC::handle<\/a>"],[0,14,"<a href=\"SH.php.html#22\">App\\Models\\Logic\\Station\\Receive\\SH::handle<\/a>"],[0,14,"<a href=\"SHSX.php.html#22\">App\\Models\\Logic\\Station\\Receive\\SHSX::handle<\/a>"],[0,17,"<a href=\"SP.php.html#33\">App\\Models\\Logic\\Station\\Receive\\SP::handle<\/a>"],[0,6,"<a href=\"SQZL.php.html#22\">App\\Models\\Logic\\Station\\Receive\\SQZL::handle<\/a>"],[0,12,"<a href=\"SQZSH.php.html#24\">App\\Models\\Logic\\Station\\Receive\\SQZSH::handle<\/a>"],[0,3,"<a href=\"ThirdParty.php.html#33\">App\\Models\\Logic\\Station\\Receive\\ThirdParty::validateAreaCode<\/a>"],[0,11,"<a href=\"ThirdParty.php.html#56\">App\\Models\\Logic\\Station\\Receive\\ThirdParty::validateRegionCode<\/a>"],[0,11,"<a href=\"ThirdParty.php.html#112\">App\\Models\\Logic\\Station\\Receive\\ThirdParty::pushStationToStationCenter<\/a>"],[0,5,"<a href=\"WJY.php.html#22\">App\\Models\\Logic\\Station\\Receive\\WJY::handle<\/a>"],[0,9,"<a href=\"XMSK.php.html#22\">App\\Models\\Logic\\Station\\Receive\\XMSK::handle<\/a>"],[0,9,"<a href=\"XY.php.html#22\">App\\Models\\Logic\\Station\\Receive\\XY::handle<\/a>"],[0,17,"<a href=\"XYN.php.html#23\">App\\Models\\Logic\\Station\\Receive\\XYN::handle<\/a>"],[0,14,"<a href=\"YUNDATONG.php.html#21\">App\\Models\\Logic\\Station\\Receive\\YUNDATONG::handle<\/a>"],[0,2,"<a href=\"ZDC.php.html#23\">App\\Models\\Logic\\Station\\Receive\\ZDC::handle<\/a>"],[0,8,"<a href=\"ZHUOYIQ.php.html#23\">App\\Models\\Logic\\Station\\Receive\\ZHUOYIQ::handle<\/a>"],[0,8,"<a href=\"ZHUOYIY.php.html#23\">App\\Models\\Logic\\Station\\Receive\\ZHUOYIY::handle<\/a>"],[0,14,"<a href=\"ZHYK.php.html#23\">App\\Models\\Logic\\Station\\Receive\\ZHYK::handle<\/a>"],[0,17,"<a href=\"ZY.php.html#37\">App\\Models\\Logic\\Station\\Receive\\ZY::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
