<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Data/Log</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Data</a></li>
         <li class="breadcrumb-item"><a href="index.html">Log</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseLog.php.html#8">App\Models\Data\Log\BaseLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FailedJobs.php.html#9">App\Models\Data\Log\FailedJobs</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueueLog.php.html#11">App\Models\Data\Log\QueueLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReceiveLog.php.html#12">App\Models\Data\Log\ReceiveLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLog.php.html#11">App\Models\Data\Log\RequestLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResponseLog.php.html#11">App\Models\Data\Log\ResponseLog</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ReceiveLog.php.html#12">App\Models\Data\Log\ReceiveLog</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="RequestLog.php.html#11">App\Models\Data\Log\RequestLog</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="QueueLog.php.html#11">App\Models\Data\Log\QueueLog</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="ResponseLog.php.html#11">App\Models\Data\Log\ResponseLog</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="FailedJobs.php.html#9">App\Models\Data\Log\FailedJobs</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BaseLog.php.html#8">App\Models\Data\Log\BaseLog</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseLog.php.html#15"><abbr title="App\Models\Data\Log\BaseLog::initFileLogChannel">initFileLogChannel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReceiveLog.php.html#173"><abbr title="App\Models\Data\Log\ReceiveLog::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResponseLog.php.html#97"><abbr title="App\Models\Data\Log\ResponseLog::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResponseLog.php.html#47"><abbr title="App\Models\Data\Log\ResponseLog::getMessage">getMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResponseLog.php.html#19"><abbr title="App\Models\Data\Log\ResponseLog::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLog.php.html#175"><abbr title="App\Models\Data\Log\RequestLog::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLog.php.html#112"><abbr title="App\Models\Data\Log\RequestLog::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLog.php.html#62"><abbr title="App\Models\Data\Log\RequestLog::getMessage">getMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLog.php.html#24"><abbr title="App\Models\Data\Log\RequestLog::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReceiveLog.php.html#114"><abbr title="App\Models\Data\Log\ReceiveLog::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FailedJobs.php.html#11"><abbr title="App\Models\Data\Log\FailedJobs::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReceiveLog.php.html#60"><abbr title="App\Models\Data\Log\ReceiveLog::getMessage">getMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReceiveLog.php.html#24"><abbr title="App\Models\Data\Log\ReceiveLog::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueueLog.php.html#159"><abbr title="App\Models\Data\Log\QueueLog::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueueLog.php.html#100"><abbr title="App\Models\Data\Log\QueueLog::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueueLog.php.html#50"><abbr title="App\Models\Data\Log\QueueLog::getMessage">getMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueueLog.php.html#13"><abbr title="App\Models\Data\Log\QueueLog::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FailedJobs.php.html#17"><abbr title="App\Models\Data\Log\FailedJobs::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResponseLog.php.html#148"><abbr title="App\Models\Data\Log\ResponseLog::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="RequestLog.php.html#112"><abbr title="App\Models\Data\Log\RequestLog::getData">getData</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="QueueLog.php.html#100"><abbr title="App\Models\Data\Log\QueueLog::getData">getData</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ReceiveLog.php.html#114"><abbr title="App\Models\Data\Log\ReceiveLog::getData">getData</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ResponseLog.php.html#97"><abbr title="App\Models\Data\Log\ResponseLog::getData">getData</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="QueueLog.php.html#50"><abbr title="App\Models\Data\Log\QueueLog::getMessage">getMessage</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ReceiveLog.php.html#60"><abbr title="App\Models\Data\Log\ReceiveLog::getMessage">getMessage</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="RequestLog.php.html#62"><abbr title="App\Models\Data\Log\RequestLog::getMessage">getMessage</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ResponseLog.php.html#47"><abbr title="App\Models\Data\Log\ResponseLog::getMessage">getMessage</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="FailedJobs.php.html#17"><abbr title="App\Models\Data\Log\FailedJobs::getData">getData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ReceiveLog.php.html#24"><abbr title="App\Models\Data\Log\ReceiveLog::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="QueueLog.php.html#13"><abbr title="App\Models\Data\Log\QueueLog::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RequestLog.php.html#24"><abbr title="App\Models\Data\Log\RequestLog::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BaseLog.php.html#15"><abbr title="App\Models\Data\Log\BaseLog::initFileLogChannel">initFileLogChannel</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ResponseLog.php.html#19"><abbr title="App\Models\Data\Log\ResponseLog::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:44:39 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([6,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([19,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"BaseLog.php.html#8\">App\\Models\\Data\\Log\\BaseLog<\/a>"],[0,6,"<a href=\"FailedJobs.php.html#9\">App\\Models\\Data\\Log\\FailedJobs<\/a>"],[0,25,"<a href=\"QueueLog.php.html#11\">App\\Models\\Data\\Log\\QueueLog<\/a>"],[0,26,"<a href=\"ReceiveLog.php.html#12\">App\\Models\\Data\\Log\\ReceiveLog<\/a>"],[0,26,"<a href=\"RequestLog.php.html#11\">App\\Models\\Data\\Log\\RequestLog<\/a>"],[0,22,"<a href=\"ResponseLog.php.html#11\">App\\Models\\Data\\Log\\ResponseLog<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"BaseLog.php.html#15\">App\\Models\\Data\\Log\\BaseLog::initFileLogChannel<\/a>"],[0,1,"<a href=\"FailedJobs.php.html#11\">App\\Models\\Data\\Log\\FailedJobs::handle<\/a>"],[0,5,"<a href=\"FailedJobs.php.html#17\">App\\Models\\Data\\Log\\FailedJobs::getData<\/a>"],[0,3,"<a href=\"QueueLog.php.html#13\">App\\Models\\Data\\Log\\QueueLog::handle<\/a>"],[0,9,"<a href=\"QueueLog.php.html#50\">App\\Models\\Data\\Log\\QueueLog::getMessage<\/a>"],[0,12,"<a href=\"QueueLog.php.html#100\">App\\Models\\Data\\Log\\QueueLog::getData<\/a>"],[0,1,"<a href=\"QueueLog.php.html#159\">App\\Models\\Data\\Log\\QueueLog::getCurDayCount<\/a>"],[0,4,"<a href=\"ReceiveLog.php.html#24\">App\\Models\\Data\\Log\\ReceiveLog::handle<\/a>"],[0,9,"<a href=\"ReceiveLog.php.html#60\">App\\Models\\Data\\Log\\ReceiveLog::getMessage<\/a>"],[0,12,"<a href=\"ReceiveLog.php.html#114\">App\\Models\\Data\\Log\\ReceiveLog::getData<\/a>"],[0,1,"<a href=\"ReceiveLog.php.html#173\">App\\Models\\Data\\Log\\ReceiveLog::getCurDayCount<\/a>"],[0,3,"<a href=\"RequestLog.php.html#24\">App\\Models\\Data\\Log\\RequestLog::handle<\/a>"],[0,9,"<a href=\"RequestLog.php.html#62\">App\\Models\\Data\\Log\\RequestLog::getMessage<\/a>"],[0,13,"<a href=\"RequestLog.php.html#112\">App\\Models\\Data\\Log\\RequestLog::getData<\/a>"],[0,1,"<a href=\"RequestLog.php.html#175\">App\\Models\\Data\\Log\\RequestLog::getCurDayCount<\/a>"],[0,2,"<a href=\"ResponseLog.php.html#19\">App\\Models\\Data\\Log\\ResponseLog::handle<\/a>"],[0,9,"<a href=\"ResponseLog.php.html#47\">App\\Models\\Data\\Log\\ResponseLog::getMessage<\/a>"],[0,10,"<a href=\"ResponseLog.php.html#97\">App\\Models\\Data\\Log\\ResponseLog::getData<\/a>"],[0,1,"<a href=\"ResponseLog.php.html#148\">App\\Models\\Data\\Log\\ResponseLog::getCurDayCount<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
