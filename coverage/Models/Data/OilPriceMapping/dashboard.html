<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Data/OilPriceMapping</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Data</a></li>
         <li class="breadcrumb-item"><a href="index.html">OilPriceMapping</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Basic.php.html#12">App\Models\Data\OilPriceMapping\Basic</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CZB.php.html#10">App\Models\Data\OilPriceMapping\CZB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GS.php.html#10">App\Models\Data\OilPriceMapping\GS</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Basic.php.html#12">App\Models\Data\OilPriceMapping\Basic</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="CZB.php.html#10">App\Models\Data\OilPriceMapping\CZB</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="GS.php.html#10">App\Models\Data\OilPriceMapping\GS</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Basic.php.html#25"><abbr title="App\Models\Data\OilPriceMapping\Basic::replaceKey">replaceKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Basic.php.html#51"><abbr title="App\Models\Data\OilPriceMapping\Basic::filterField">filterField</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Basic.php.html#84"><abbr title="App\Models\Data\OilPriceMapping\Basic::dealOilStationDataNullValue">dealOilStationDataNullValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CZB.php.html#12"><abbr title="App\Models\Data\OilPriceMapping\CZB::getOilPriceData">getOilPriceData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GS.php.html#12"><abbr title="App\Models\Data\OilPriceMapping\GS::getOilPriceData">getOilPriceData</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CZB.php.html#12"><abbr title="App\Models\Data\OilPriceMapping\CZB::getOilPriceData">getOilPriceData</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Basic.php.html#51"><abbr title="App\Models\Data\OilPriceMapping\Basic::filterField">filterField</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Basic.php.html#84"><abbr title="App\Models\Data\OilPriceMapping\Basic::dealOilStationDataNullValue">dealOilStationDataNullValue</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="GS.php.html#12"><abbr title="App\Models\Data\OilPriceMapping\GS::getOilPriceData">getOilPriceData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Basic.php.html#25"><abbr title="App\Models\Data\OilPriceMapping\Basic::replaceKey">replaceKey</abbr></a></td><td class="text-right">30</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:47:38 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([3,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([5,0,0,0,0,0,0,0,0,0,0,1], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,20,"<a href=\"Basic.php.html#12\">App\\Models\\Data\\OilPriceMapping\\Basic<\/a>"],[0,14,"<a href=\"CZB.php.html#10\">App\\Models\\Data\\OilPriceMapping\\CZB<\/a>"],[0,6,"<a href=\"GS.php.html#10\">App\\Models\\Data\\OilPriceMapping\\GS<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[100,1,"<a href=\"Basic.php.html#14\">App\\Models\\Data\\OilPriceMapping\\Basic::getOilPriceData<\/a>"],[0,5,"<a href=\"Basic.php.html#25\">App\\Models\\Data\\OilPriceMapping\\Basic::replaceKey<\/a>"],[0,8,"<a href=\"Basic.php.html#51\">App\\Models\\Data\\OilPriceMapping\\Basic::filterField<\/a>"],[0,6,"<a href=\"Basic.php.html#84\">App\\Models\\Data\\OilPriceMapping\\Basic::dealOilStationDataNullValue<\/a>"],[0,14,"<a href=\"CZB.php.html#12\">App\\Models\\Data\\OilPriceMapping\\CZB::getOilPriceData<\/a>"],[0,6,"<a href=\"GS.php.html#12\">App\\Models\\Data\\OilPriceMapping\\GS::getOilPriceData<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
