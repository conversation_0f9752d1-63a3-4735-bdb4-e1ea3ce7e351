<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Models/Data</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Models</a></li>
         <li class="breadcrumb-item"><a href="index.html">Data</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AuthConfig.php.html#10">App\Models\Data\AuthConfig</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SPCJ.php.html#10">App\Models\Data\OilMapping\SPCJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/YGY.php.html#11">App\Models\Data\OilMapping\YGY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/YBT.php.html#7">App\Models\Data\OilMapping\YBT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/YB.php.html#10">App\Models\Data\OilMapping\YB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/XYDS.php.html#7">App\Models\Data\OilMapping\XYDS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/XM.php.html#10">App\Models\Data\OilMapping\XM</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/XC.php.html#10">App\Models\Data\OilMapping\XC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/WZYT.php.html#10">App\Models\Data\OilMapping\WZYT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/WSY.php.html#10">App\Models\Data\OilMapping\WSY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/TC.php.html#10">App\Models\Data\OilMapping\TC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/STY.php.html#10">App\Models\Data\OilMapping\STY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SQ.php.html#10">App\Models\Data\OilMapping\SQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SP.php.html#10">App\Models\Data\OilMapping\SP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/YXT.php.html#10">App\Models\Data\OilMapping\YXT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SHENGMAN.php.html#10">App\Models\Data\OilMapping\SHENGMAN</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SFSX.php.html#10">App\Models\Data\OilMapping\SFSX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SFFYSIMPLE.php.html#11">App\Models\Data\OilMapping\SFFYSIMPLE</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SFFY.php.html#14">App\Models\Data\OilMapping\SFFY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/RY.php.html#7">App\Models\Data\OilMapping\RY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/RRS.php.html#7">App\Models\Data\OilMapping\RRS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/RQ.php.html#10">App\Models\Data\OilMapping\RQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/QDMY.php.html#10">App\Models\Data\OilMapping\QDMY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/PCKJ.php.html#10">App\Models\Data\OilMapping\PCKJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/MYCF.php.html#10">App\Models\Data\OilMapping\MYCF</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/MYB.php.html#11">App\Models\Data\OilMapping\MYB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/MY.php.html#10">App\Models\Data\OilMapping\MY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/YLZ.php.html#10">App\Models\Data\OilMapping\YLZ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Ygj.php.html#7">App\Models\Data\OilMapping\Ygj</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/MK.php.html#7">App\Models\Data\OilMapping\MK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#14">App\Models\Data\OrderAssoc</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trade/Oil.php.html#14">App\Models\Data\Trade\Oil</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierInfo.php.html#12">App\Models\Data\SupplierInfo</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationUniqueMapping.php.html#15">App\Models\Data\StationUniqueMapping</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitch.php.html#22">App\Models\Data\StationPushSwitch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushRecord.php.html#10">App\Models\Data\StationPushRecord</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushCondition.php.html#12">App\Models\Data\StationPushCondition</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPrice.php.html#14">App\Models\Data\StationPrice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePermission.php.html#9">App\Models\Data\RolePermission</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Role.php.html#9">App\Models\Data\Role</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegionalInfo.php.html#19">App\Models\Data\RegionalInfo</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Permission.php.html#12">App\Models\Data\Permission</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderRefund.php.html#11">App\Models\Data\OrderRefund</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilPriceMapping/GS.php.html#10">App\Models\Data\OilPriceMapping\GS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZEY.php.html#10">App\Models\Data\OilMapping\ZEY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilPriceMapping/CZB.php.html#10">App\Models\Data\OilPriceMapping\CZB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilPriceMapping/Basic.php.html#12">App\Models\Data\OilPriceMapping\Basic</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Zsh.php.html#7">App\Models\Data\OilMapping\Zsh</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZZ_TJ.php.html#10">App\Models\Data\OilMapping\ZZ_TJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZZ_BJ.php.html#10">App\Models\Data\OilMapping\ZZ_BJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZZ_AH.php.html#10">App\Models\Data\OilMapping\ZZ_AH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZZ.php.html#10">App\Models\Data\OilMapping\ZZ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZTO.php.html#10">App\Models\Data\OilMapping\ZTO</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZT.php.html#10">App\Models\Data\OilMapping\ZT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZLGX.php.html#10">App\Models\Data\OilMapping\ZLGX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZJKA.php.html#9">App\Models\Data\OilMapping\ZJKA</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZJ.php.html#12">App\Models\Data\OilMapping\ZJ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/MTL.php.html#10">App\Models\Data\OilMapping\MTL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/MB.php.html#11">App\Models\Data\OilMapping\MB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthInfo.php.html#18">App\Models\Data\AuthInfo</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformInfo.php.html#10">App\Models\Data\DockingPlatformInfo</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Basic.php.html#14">App\Models\Data\OilMapping\Basic</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/BQ.php.html#7">App\Models\Data\OilMapping\BQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/BBSP.php.html#7">App\Models\Data\OilMapping\BBSP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Ad.php.html#16">App\Models\Data\OilMapping\Ad</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/AJSW.php.html#10">App\Models\Data\OilMapping\AJSW</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/ResponseLog.php.html#11">App\Models\Data\Log\ResponseLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/RequestLog.php.html#11">App\Models\Data\Log\RequestLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/ReceiveLog.php.html#12">App\Models\Data\Log\ReceiveLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/QueueLog.php.html#11">App\Models\Data\Log\QueueLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/FailedJobs.php.html#9">App\Models\Data\Log\FailedJobs</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/BaseLog.php.html#8">App\Models\Data\Log\BaseLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DictTag.php.html#8">App\Models\Data\DictTag</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/CFT.php.html#7">App\Models\Data\OilMapping\CFT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingResultEntity.php.html#6">App\Models\Data\DecodingResultEntity</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/ZY.php.html#13">App\Models\Data\DecodingQrCodeAdaptation\ZY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/XY.php.html#9">App\Models\Data\DecodingQrCodeAdaptation\XY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/WJY.php.html#10">App\Models\Data\DecodingQrCodeAdaptation\WJY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/SP.php.html#9">App\Models\Data\DecodingQrCodeAdaptation\SP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/DIANDI.php.html#9">App\Models\Data\DecodingQrCodeAdaptation\DIANDI</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/BASIC.php.html#10">App\Models\Data\DecodingQrCodeAdaptation\BASIC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/BASE.php.html#9">App\Models\Data\DecodingQrCodeAdaptation\BASE</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CompanyMapping.php.html#14">App\Models\Data\CompanyMapping</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Common.php.html#7">App\Models\Data\Common</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Bill.php.html#7">App\Models\Data\Bill</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Base.php.html#15">App\Models\Data\Base</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/BdtOrg.php.html#11">App\Models\Data\OilMapping\BdtOrg</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/CHTX.php.html#11">App\Models\Data\OilMapping\CHTX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Lhys.php.html#7">App\Models\Data\OilMapping\Lhys</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HLL.php.html#7">App\Models\Data\OilMapping\HLL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/LT.php.html#7">App\Models\Data\OilMapping\LT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/LF.php.html#7">App\Models\Data\OilMapping\LF</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/KY.php.html#10">App\Models\Data\OilMapping\KY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Jd.php.html#7">App\Models\Data\OilMapping\Jd</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/JTXY.php.html#7">App\Models\Data\OilMapping\JTXY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/JF.php.html#10">App\Models\Data\OilMapping\JF</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/JDWC.php.html#11">App\Models\Data\OilMapping\JDWC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HytOrg.php.html#10">App\Models\Data\OilMapping\HytOrg</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HZ.php.html#7">App\Models\Data\OilMapping\HZ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HYJY.php.html#7">App\Models\Data\OilMapping\HYJY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HSL.php.html#10">App\Models\Data\OilMapping\HSL</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HR.php.html#10">App\Models\Data\OilMapping\HR</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HLJH.php.html#7">App\Models\Data\OilMapping\HLJH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/CIEC.php.html#11">App\Models\Data\OilMapping\CIEC</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HK.php.html#7">App\Models\Data\OilMapping\HK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HG.php.html#9">App\Models\Data\OilMapping\HG</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Ghc.php.html#14">App\Models\Data\OilMapping\Ghc</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/GBDW.php.html#12">App\Models\Data\OilMapping\GBDW</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Fy.php.html#14">App\Models\Data\OilMapping\Fy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/DESP2H8BBD.php.html#13">App\Models\Data\OilMapping\DESP2H8BBD</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/DESP2C637M.php.html#13">App\Models\Data\OilMapping\DESP2C637M</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/DESP2A6LA9.php.html#13">App\Models\Data\OilMapping\DESP2A6LA9</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/DDE.php.html#11">App\Models\Data\OilMapping\DDE</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Common.php.html#13">App\Models\Data\OilMapping\Common</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Cfhy.php.html#10">App\Models\Data\OilMapping\Cfhy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/CN.php.html#11">App\Models\Data\OilMapping\CN</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trade/Order.php.html#14">App\Models\Data\Trade\Order</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="OrderAssoc.php.html#14">App\Models\Data\OrderAssoc</a></td><td class="text-right">14520</td></tr>
       <tr><td><a href="StationUniqueMapping.php.html#15">App\Models\Data\StationUniqueMapping</a></td><td class="text-right">3422</td></tr>
       <tr><td><a href="Base.php.html#15">App\Models\Data\Base</a></td><td class="text-right">2450</td></tr>
       <tr><td><a href="StationPrice.php.html#14">App\Models\Data\StationPrice</a></td><td class="text-right">2162</td></tr>
       <tr><td><a href="OilMapping/Common.php.html#13">App\Models\Data\OilMapping\Common</a></td><td class="text-right">1980</td></tr>
       <tr><td><a href="AuthInfo.php.html#18">App\Models\Data\AuthInfo</a></td><td class="text-right">1560</td></tr>
       <tr><td><a href="Log/ReceiveLog.php.html#12">App\Models\Data\Log\ReceiveLog</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="Log/RequestLog.php.html#11">App\Models\Data\Log\RequestLog</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="StationPushSwitch.php.html#22">App\Models\Data\StationPushSwitch</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Log/QueueLog.php.html#11">App\Models\Data\Log\QueueLog</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="StationPushCondition.php.html#12">App\Models\Data\StationPushCondition</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="Log/ResponseLog.php.html#11">App\Models\Data\Log\ResponseLog</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="OilPriceMapping/Basic.php.html#12">App\Models\Data\OilPriceMapping\Basic</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="OilMapping/Ad.php.html#16">App\Models\Data\OilMapping\Ad</a></td><td class="text-right">380</td></tr>
       <tr><td><a href="DockingPlatformInfo.php.html#10">App\Models\Data\DockingPlatformInfo</a></td><td class="text-right">380</td></tr>
       <tr><td><a href="RegionalInfo.php.html#19">App\Models\Data\RegionalInfo</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="CompanyMapping.php.html#14">App\Models\Data\CompanyMapping</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="OilMapping/YGY.php.html#11">App\Models\Data\OilMapping\YGY</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="OrderRefund.php.html#11">App\Models\Data\OrderRefund</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="OilMapping/Basic.php.html#14">App\Models\Data\OilMapping\Basic</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="AuthConfig.php.html#10">App\Models\Data\AuthConfig</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="OilPriceMapping/CZB.php.html#10">App\Models\Data\OilPriceMapping\CZB</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="OilMapping/Fy.php.html#14">App\Models\Data\OilMapping\Fy</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Permission.php.html#12">App\Models\Data\Permission</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="StationPushRecord.php.html#10">App\Models\Data\StationPushRecord</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="SupplierInfo.php.html#12">App\Models\Data\SupplierInfo</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Role.php.html#9">App\Models\Data\Role</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="RolePermission.php.html#9">App\Models\Data\RolePermission</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="OilMapping/SFFY.php.html#14">App\Models\Data\OilMapping\SFFY</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="OilMapping/ZEY.php.html#10">App\Models\Data\OilMapping\ZEY</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="OilMapping/CHTX.php.html#11">App\Models\Data\OilMapping\CHTX</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="OilMapping/SHENGMAN.php.html#10">App\Models\Data\OilMapping\SHENGMAN</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="OilMapping/ZJ.php.html#12">App\Models\Data\OilMapping\ZJ</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="OilMapping/YLZ.php.html#10">App\Models\Data\OilMapping\YLZ</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="OilMapping/DESP2H8BBD.php.html#13">App\Models\Data\OilMapping\DESP2H8BBD</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="OilMapping/DESP2C637M.php.html#13">App\Models\Data\OilMapping\DESP2C637M</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="OilMapping/DESP2A6LA9.php.html#13">App\Models\Data\OilMapping\DESP2A6LA9</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="OilMapping/HG.php.html#9">App\Models\Data\OilMapping\HG</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/KY.php.html#10">App\Models\Data\OilMapping\KY</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/GBDW.php.html#12">App\Models\Data\OilMapping\GBDW</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/Ygj.php.html#7">App\Models\Data\OilMapping\Ygj</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/MYCF.php.html#10">App\Models\Data\OilMapping\MYCF</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/RQ.php.html#10">App\Models\Data\OilMapping\RQ</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/XM.php.html#10">App\Models\Data\OilMapping\XM</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/QDMY.php.html#10">App\Models\Data\OilMapping\QDMY</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/ZZ.php.html#10">App\Models\Data\OilMapping\ZZ</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/SPCJ.php.html#10">App\Models\Data\OilMapping\SPCJ</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/ZZ_AH.php.html#10">App\Models\Data\OilMapping\ZZ_AH</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/WZYT.php.html#10">App\Models\Data\OilMapping\WZYT</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/ZZ_BJ.php.html#10">App\Models\Data\OilMapping\ZZ_BJ</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/ZZ_TJ.php.html#10">App\Models\Data\OilMapping\ZZ_TJ</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/HSL.php.html#10">App\Models\Data\OilMapping\HSL</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Log/FailedJobs.php.html#9">App\Models\Data\Log\FailedJobs</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilPriceMapping/GS.php.html#10">App\Models\Data\OilPriceMapping\GS</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/SFFYSIMPLE.php.html#11">App\Models\Data\OilMapping\SFFYSIMPLE</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/SFSX.php.html#10">App\Models\Data\OilMapping\SFSX</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/STY.php.html#10">App\Models\Data\OilMapping\STY</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/YXT.php.html#10">App\Models\Data\OilMapping\YXT</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/ZT.php.html#10">App\Models\Data\OilMapping\ZT</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/ZTO.php.html#10">App\Models\Data\OilMapping\ZTO</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/JF.php.html#10">App\Models\Data\OilMapping\JF</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/HR.php.html#10">App\Models\Data\OilMapping\HR</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/ZLGX.php.html#10">App\Models\Data\OilMapping\ZLGX</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/AJSW.php.html#10">App\Models\Data\OilMapping\AJSW</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/ZY.php.html#13">App\Models\Data\DecodingQrCodeAdaptation\ZY</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/WJY.php.html#10">App\Models\Data\DecodingQrCodeAdaptation\WJY</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/Cfhy.php.html#10">App\Models\Data\OilMapping\Cfhy</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/MTL.php.html#10">App\Models\Data\OilMapping\MTL</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/LF.php.html#7">App\Models\Data\OilMapping\LF</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/CIEC.php.html#11">App\Models\Data\OilMapping\CIEC</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/CN.php.html#11">App\Models\Data\OilMapping\CN</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/JDWC.php.html#11">App\Models\Data\OilMapping\JDWC</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/MB.php.html#11">App\Models\Data\OilMapping\MB</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/Ghc.php.html#14">App\Models\Data\OilMapping\Ghc</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DecodingResultEntity.php.html#6">App\Models\Data\DecodingResultEntity</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DictTag.php.html#8">App\Models\Data\DictTag</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/HK.php.html#7">App\Models\Data\OilMapping\HK</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/BdtOrg.php.html#11">App\Models\Data\OilMapping\BdtOrg</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/HytOrg.php.html#10">App\Models\Data\OilMapping\HytOrg</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/DDE.php.html#11">App\Models\Data\OilMapping\DDE</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/ZJKA.php.html#9">App\Models\Data\OilMapping\ZJKA</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/SQ.php.html#10">App\Models\Data\OilMapping\SQ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/SP.php.html#10">App\Models\Data\OilMapping\SP</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/MYB.php.html#11">App\Models\Data\OilMapping\MYB</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/TC.php.html#10">App\Models\Data\OilMapping\TC</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/WSY.php.html#10">App\Models\Data\OilMapping\WSY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/PCKJ.php.html#10">App\Models\Data\OilMapping\PCKJ</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/XC.php.html#10">App\Models\Data\OilMapping\XC</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/YB.php.html#10">App\Models\Data\OilMapping\YB</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/MY.php.html#10">App\Models\Data\OilMapping\MY</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/HYJY.php.html#7">App\Models\Data\OilMapping\HYJY</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/RY.php.html#7">App\Models\Data\OilMapping\RY</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Log/BaseLog.php.html#8">App\Models\Data\Log\BaseLog</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/RRS.php.html#7">App\Models\Data\OilMapping\RRS</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/HLJH.php.html#7">App\Models\Data\OilMapping\HLJH</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/HLL.php.html#7">App\Models\Data\OilMapping\HLL</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/MK.php.html#7">App\Models\Data\OilMapping\MK</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/BBSP.php.html#7">App\Models\Data\OilMapping\BBSP</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/BQ.php.html#7">App\Models\Data\OilMapping\BQ</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/XYDS.php.html#7">App\Models\Data\OilMapping\XYDS</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/CFT.php.html#7">App\Models\Data\OilMapping\CFT</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/YBT.php.html#7">App\Models\Data\OilMapping\YBT</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/JTXY.php.html#7">App\Models\Data\OilMapping\JTXY</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/Jd.php.html#7">App\Models\Data\OilMapping\Jd</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/LT.php.html#7">App\Models\Data\OilMapping\LT</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/Lhys.php.html#7">App\Models\Data\OilMapping\Lhys</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/HZ.php.html#7">App\Models\Data\OilMapping\HZ</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AuthConfig.php.html#14"><abbr title="App\Models\Data\AuthConfig::getAuthConfigValByName">getAuthConfigValByName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilPriceMapping/GS.php.html#12"><abbr title="App\Models\Data\OilPriceMapping\GS::getOilPriceData">getOilPriceData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZZ.php.html#19"><abbr title="App\Models\Data\OilMapping\ZZ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZZ_AH.php.html#19"><abbr title="App\Models\Data\OilMapping\ZZ_AH::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZZ_BJ.php.html#19"><abbr title="App\Models\Data\OilMapping\ZZ_BJ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZZ_TJ.php.html#19"><abbr title="App\Models\Data\OilMapping\ZZ_TJ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Zsh.php.html#9"><abbr title="App\Models\Data\OilMapping\Zsh::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Zsh.php.html#14"><abbr title="App\Models\Data\OilMapping\Zsh::getPayedCouponList">getPayedCouponList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Zsh.php.html#28"><abbr title="App\Models\Data\OilMapping\Zsh::getCouponList">getCouponList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilPriceMapping/Basic.php.html#25"><abbr title="App\Models\Data\OilPriceMapping\Basic::replaceKey">replaceKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilPriceMapping/Basic.php.html#51"><abbr title="App\Models\Data\OilPriceMapping\Basic::filterField">filterField</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilPriceMapping/Basic.php.html#84"><abbr title="App\Models\Data\OilPriceMapping\Basic::dealOilStationDataNullValue">dealOilStationDataNullValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilPriceMapping/CZB.php.html#12"><abbr title="App\Models\Data\OilPriceMapping\CZB::getOilPriceData">getOilPriceData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#25"><abbr title="App\Models\Data\OrderAssoc::setId">setId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZT.php.html#19"><abbr title="App\Models\Data\OilMapping\ZT::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#47"><abbr title="App\Models\Data\OrderAssoc::insert">insert</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#84"><abbr title="App\Models\Data\OrderAssoc::getMessage">getMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#92"><abbr title="App\Models\Data\OrderAssoc::updateSelfOrderStatus">updateSelfOrderStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#113"><abbr title="App\Models\Data\OrderAssoc::updateOrderInfoByOrderId">updateOrderInfoByOrderId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#165"><abbr title="App\Models\Data\OrderAssoc::updateOrderInfoById">updateOrderInfoById</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#201"><abbr title="App\Models\Data\OrderAssoc::getOrderInfoByPlatformOrderId">getOrderInfoByPlatformOrderId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#223"><abbr title="App\Models\Data\OrderAssoc::getOrderInfoBySelfOrderId">getOrderInfoBySelfOrderId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#269"><abbr title="App\Models\Data\OrderAssoc::getOrderInfoByWhere">getOrderInfoByWhere</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#335"><abbr title="App\Models\Data\OrderAssoc::getOneOrderByWhereAndLock">getOneOrderByWhereAndLock</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#394"><abbr title="App\Models\Data\OrderAssoc::insertBySearchForPlatform">insertBySearchForPlatform</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#456"><abbr title="App\Models\Data\OrderAssoc::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#528"><abbr title="App\Models\Data\OrderAssoc::getChart">getChart</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZTO.php.html#19"><abbr title="App\Models\Data\OilMapping\ZTO::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZLGX.php.html#19"><abbr title="App\Models\Data\OilMapping\ZLGX::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderRefund.php.html#23"><abbr title="App\Models\Data\OrderRefund::insert">insert</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/WSY.php.html#15"><abbr title="App\Models\Data\OilMapping\WSY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/RRS.php.html#9"><abbr title="App\Models\Data\OilMapping\RRS::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/RY.php.html#9"><abbr title="App\Models\Data\OilMapping\RY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SFFY.php.html#28"><abbr title="App\Models\Data\OilMapping\SFFY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SFFYSIMPLE.php.html#20"><abbr title="App\Models\Data\OilMapping\SFFYSIMPLE::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SFSX.php.html#19"><abbr title="App\Models\Data\OilMapping\SFSX::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SHENGMAN.php.html#19"><abbr title="App\Models\Data\OilMapping\SHENGMAN::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SP.php.html#35"><abbr title="App\Models\Data\OilMapping\SP::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SPCJ.php.html#35"><abbr title="App\Models\Data\OilMapping\SPCJ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/SQ.php.html#19"><abbr title="App\Models\Data\OilMapping\SQ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/STY.php.html#19"><abbr title="App\Models\Data\OilMapping\STY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/TC.php.html#19"><abbr title="App\Models\Data\OilMapping\TC::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/WZYT.php.html#19"><abbr title="App\Models\Data\OilMapping\WZYT::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZJKA.php.html#11"><abbr title="App\Models\Data\OilMapping\ZJKA::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/XC.php.html#19"><abbr title="App\Models\Data\OilMapping\XC::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/XM.php.html#19"><abbr title="App\Models\Data\OilMapping\XM::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/XYDS.php.html#9"><abbr title="App\Models\Data\OilMapping\XYDS::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/YB.php.html#19"><abbr title="App\Models\Data\OilMapping\YB::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/YBT.php.html#9"><abbr title="App\Models\Data\OilMapping\YBT::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/YGY.php.html#31"><abbr title="App\Models\Data\OilMapping\YGY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/YGY.php.html#126"><abbr title="App\Models\Data\OilMapping\YGY::getAvailableOil">getAvailableOil</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/YLZ.php.html#19"><abbr title="App\Models\Data\OilMapping\YLZ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/YXT.php.html#19"><abbr title="App\Models\Data\OilMapping\YXT::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Ygj.php.html#9"><abbr title="App\Models\Data\OilMapping\Ygj::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZEY.php.html#24"><abbr title="App\Models\Data\OilMapping\ZEY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/ZJ.php.html#21"><abbr title="App\Models\Data\OilMapping\ZJ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssoc.php.html#597"><abbr title="App\Models\Data\OrderAssoc::getRetryOrderById">getRetryOrderById</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderRefund.php.html#49"><abbr title="App\Models\Data\OrderRefund::getMessage">getMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/QDMY.php.html#19"><abbr title="App\Models\Data\OilMapping\QDMY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationUniqueMapping.php.html#76"><abbr title="App\Models\Data\StationUniqueMapping::getStationPriceByStationId">getStationPriceByStationId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushCondition.php.html#130"><abbr title="App\Models\Data\StationPushCondition::receiveStationPushRule">receiveStationPushRule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushRecord.php.html#12"><abbr title="App\Models\Data\StationPushRecord::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushRecord.php.html#39"><abbr title="App\Models\Data\StationPushRecord::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitch.php.html#34"><abbr title="App\Models\Data\StationPushSwitch::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitch.php.html#70"><abbr title="App\Models\Data\StationPushSwitch::getOpenAll">getOpenAll</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitch.php.html#95"><abbr title="App\Models\Data\StationPushSwitch::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitch.php.html#112"><abbr title="App\Models\Data\StationPushSwitch::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitch.php.html#130"><abbr title="App\Models\Data\StationPushSwitch::getPushedStationList">getPushedStationList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitch.php.html#200"><abbr title="App\Models\Data\StationPushSwitch::getAll">getAll</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationUniqueMapping.php.html#24"><abbr title="App\Models\Data\StationUniqueMapping::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationUniqueMapping.php.html#55"><abbr title="App\Models\Data\StationUniqueMapping::getDataByStationId">getDataByStationId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationUniqueMapping.php.html#126"><abbr title="App\Models\Data\StationUniqueMapping::getStationDataByStationIdAndCondition">getStationDataByStationIdAndCondition</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushCondition.php.html#117"><abbr title="App\Models\Data\StationPushCondition::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationUniqueMapping.php.html#181"><abbr title="App\Models\Data\StationUniqueMapping::getStationPriceByExternalStationId">getStationPriceByExternalStationId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationUniqueMapping.php.html#248"><abbr title="App\Models\Data\StationUniqueMapping::getStationUniqueMappingByWhere">getStationUniqueMappingByWhere</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierInfo.php.html#24"><abbr title="App\Models\Data\SupplierInfo::getSupplierNameBySupplierCode">getSupplierNameBySupplierCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierInfo.php.html#45"><abbr title="App\Models\Data\SupplierInfo::getAllData">getAllData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierInfo.php.html#50"><abbr title="App\Models\Data\SupplierInfo::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierInfo.php.html#78"><abbr title="App\Models\Data\SupplierInfo::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierInfo.php.html#95"><abbr title="App\Models\Data\SupplierInfo::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierInfo.php.html#101"><abbr title="App\Models\Data\SupplierInfo::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trade/Oil.php.html#24"><abbr title="App\Models\Data\Trade\Oil::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trade/Oil.php.html#31"><abbr title="App\Models\Data\Trade\Oil::dmd">dmd</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trade/Order.php.html#86"><abbr title="App\Models\Data\Trade\Order::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trade/Order.php.html#93"><abbr title="App\Models\Data\Trade\Order::sg">sg</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushCondition.php.html#122"><abbr title="App\Models\Data\StationPushCondition::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushCondition.php.html#101"><abbr title="App\Models\Data\StationPushCondition::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderRefund.php.html#58"><abbr title="App\Models\Data\OrderRefund::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Role.php.html#34"><abbr title="App\Models\Data\Role::getSelect">getSelect</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Permission.php.html#16"><abbr title="App\Models\Data\Permission::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Permission.php.html#36"><abbr title="App\Models\Data\Permission::getSelect">getSelect</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Permission.php.html#50"><abbr title="App\Models\Data\Permission::getTree">getTree</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Permission.php.html#89"><abbr title="App\Models\Data\Permission::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Permission.php.html#105"><abbr title="App\Models\Data\Permission::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Permission.php.html#111"><abbr title="App\Models\Data\Permission::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegionalInfo.php.html#24"><abbr title="App\Models\Data\RegionalInfo::getCityCodeByName">getCityCodeByName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegionalInfo.php.html#46"><abbr title="App\Models\Data\RegionalInfo::getCityCodeByNameFilterUnit">getCityCodeByNameFilterUnit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegionalInfo.php.html#77"><abbr title="App\Models\Data\RegionalInfo::filterCode">filterCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegionalInfo.php.html#94"><abbr title="App\Models\Data\RegionalInfo::getNameByCode">getNameByCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegionalInfo.php.html#133"><abbr title="App\Models\Data\RegionalInfo::getParentCodeByChildren">getParentCodeByChildren</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Role.php.html#13"><abbr title="App\Models\Data\Role::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Role.php.html#48"><abbr title="App\Models\Data\Role::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushCondition.php.html#91"><abbr title="App\Models\Data\StationPushCondition::getPushRuleByNameAbbreviation">getPushRuleByNameAbbreviation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Role.php.html#64"><abbr title="App\Models\Data\Role::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Role.php.html#70"><abbr title="App\Models\Data\Role::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Role.php.html#78"><abbr title="App\Models\Data\Role::getValidateData">getValidateData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePermission.php.html#11"><abbr title="App\Models\Data\RolePermission::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePermission.php.html#48"><abbr title="App\Models\Data\RolePermission::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePermission.php.html#100"><abbr title="App\Models\Data\RolePermission::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePermission.php.html#112"><abbr title="App\Models\Data\RolePermission::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPrice.php.html#23"><abbr title="App\Models\Data\StationPrice::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPrice.php.html#148"><abbr title="App\Models\Data\StationPrice::getDataByWhere">getDataByWhere</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPrice.php.html#181"><abbr title="App\Models\Data\StationPrice::updateByWhere">updateByWhere</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPrice.php.html#213"><abbr title="App\Models\Data\StationPrice::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushCondition.php.html#22"><abbr title="App\Models\Data\StationPushCondition::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/RQ.php.html#30"><abbr title="App\Models\Data\OilMapping\RQ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/PCKJ.php.html#19"><abbr title="App\Models\Data\OilMapping\PCKJ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthConfig.php.html#31"><abbr title="App\Models\Data\AuthConfig::getAuthConfigNameByVal">getAuthConfigNameByVal</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformInfo.php.html#29"><abbr title="App\Models\Data\DockingPlatformInfo::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CompanyMapping.php.html#98"><abbr title="App\Models\Data\CompanyMapping::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/BASE.php.html#12"><abbr title="App\Models\Data\DecodingQrCodeAdaptation\BASE::getAdaptationResult">getAdaptationResult</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/BASIC.php.html#12"><abbr title="App\Models\Data\DecodingQrCodeAdaptation\BASIC::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/DIANDI.php.html#12"><abbr title="App\Models\Data\DecodingQrCodeAdaptation\DIANDI::getAdaptationResult">getAdaptationResult</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/SP.php.html#12"><abbr title="App\Models\Data\DecodingQrCodeAdaptation\SP::getAdaptationResult">getAdaptationResult</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/WJY.php.html#20"><abbr title="App\Models\Data\DecodingQrCodeAdaptation\WJY::getAdaptationResult">getAdaptationResult</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/XY.php.html#11"><abbr title="App\Models\Data\DecodingQrCodeAdaptation\XY::getAdaptationResult">getAdaptationResult</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/ZY.php.html#23"><abbr title="App\Models\Data\DecodingQrCodeAdaptation\ZY::getAdaptationResult">getAdaptationResult</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DecodingResultEntity.php.html#26"><abbr title="App\Models\Data\DecodingResultEntity::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DictTag.php.html#12"><abbr title="App\Models\Data\DictTag::getRebateLabel">getRebateLabel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformInfo.php.html#14"><abbr title="App\Models\Data\DockingPlatformInfo::getPlatformNameByNameAbbreviation">getPlatformNameByNameAbbreviation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformInfo.php.html#50"><abbr title="App\Models\Data\DockingPlatformInfo::getSelect">getSelect</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CompanyMapping.php.html#28"><abbr title="App\Models\Data\CompanyMapping::getCompanyByWhere">getCompanyByWhere</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformInfo.php.html#65"><abbr title="App\Models\Data\DockingPlatformInfo::getFieldsByNameAbbreviation">getFieldsByNameAbbreviation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformInfo.php.html#95"><abbr title="App\Models\Data\DockingPlatformInfo::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformInfo.php.html#112"><abbr title="App\Models\Data\DockingPlatformInfo::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformInfo.php.html#118"><abbr title="App\Models\Data\DockingPlatformInfo::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/BaseLog.php.html#15"><abbr title="App\Models\Data\Log\BaseLog::initFileLogChannel">initFileLogChannel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/FailedJobs.php.html#11"><abbr title="App\Models\Data\Log\FailedJobs::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/FailedJobs.php.html#17"><abbr title="App\Models\Data\Log\FailedJobs::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/QueueLog.php.html#13"><abbr title="App\Models\Data\Log\QueueLog::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/QueueLog.php.html#50"><abbr title="App\Models\Data\Log\QueueLog::getMessage">getMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/QueueLog.php.html#100"><abbr title="App\Models\Data\Log\QueueLog::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/QueueLog.php.html#159"><abbr title="App\Models\Data\Log\QueueLog::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/ReceiveLog.php.html#24"><abbr title="App\Models\Data\Log\ReceiveLog::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CompanyMapping.php.html#81"><abbr title="App\Models\Data\CompanyMapping::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Common.php.html#43"><abbr title="App\Models\Data\Common::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/ReceiveLog.php.html#114"><abbr title="App\Models\Data\Log\ReceiveLog::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthInfo.php.html#192"><abbr title="App\Models\Data\AuthInfo::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthConfig.php.html#48"><abbr title="App\Models\Data\AuthConfig::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthConfig.php.html#68"><abbr title="App\Models\Data\AuthConfig::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthConfig.php.html#77"><abbr title="App\Models\Data\AuthConfig::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthConfig.php.html#93"><abbr title="App\Models\Data\AuthConfig::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthConfig.php.html#104"><abbr title="App\Models\Data\AuthConfig::getFieldsByNames">getFieldsByNames</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthConfig.php.html#109"><abbr title="App\Models\Data\AuthConfig::updateByName">updateByName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthInfo.php.html#23"><abbr title="App\Models\Data\AuthInfo::getAuthInfoByAccessKey">getAuthInfoByAccessKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthInfo.php.html#40"><abbr title="App\Models\Data\AuthInfo::getAuthInfoById">getAuthInfoById</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthInfo.php.html#51"><abbr title="App\Models\Data\AuthInfo::getAuthInfoFieldByNameAbbreviation">getAuthInfoFieldByNameAbbreviation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthInfo.php.html#81"><abbr title="App\Models\Data\AuthInfo::getAuthInfoByWhere">getAuthInfoByWhere</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthInfo.php.html#129"><abbr title="App\Models\Data\AuthInfo::getAuthInfoByRoleCode">getAuthInfoByRoleCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthInfo.php.html#163"><abbr title="App\Models\Data\AuthInfo::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthInfo.php.html#209"><abbr title="App\Models\Data\AuthInfo::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Bill.php.html#107"><abbr title="App\Models\Data\Bill::formatBillDataNew">formatBillDataNew</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AuthInfo.php.html#224"><abbr title="App\Models\Data\AuthInfo::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Base.php.html#34"><abbr title="App\Models\Data\Base::getLogTableSqlByTime">getLogTableSqlByTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Base.php.html#85"><abbr title="App\Models\Data\Base::getCurrentDayCountSqlByTable">getCurrentDayCountSqlByTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Base.php.html#106"><abbr title="App\Models\Data\Base::getCurWeekNo">getCurWeekNo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Base.php.html#117"><abbr title="App\Models\Data\Base::getCurYear">getCurYear</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Base.php.html#134"><abbr title="App\Models\Data\Base::replaceKey">replaceKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Base.php.html#159"><abbr title="App\Models\Data\Base::filterField">filterField</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Base.php.html#200"><abbr title="App\Models\Data\Base::popNotInFillAbleElement">popNotInFillAbleElement</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Base.php.html#211"><abbr title="App\Models\Data\Base::dealOilStationDataNullValue">dealOilStationDataNullValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Base.php.html#239"><abbr title="App\Models\Data\Base::getStationPushRecordTableSqlByTime">getStationPushRecordTableSqlByTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Bill.php.html#10"><abbr title="App\Models\Data\Bill::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Bill.php.html#15"><abbr title="App\Models\Data\Bill::formatBillData">formatBillData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/ReceiveLog.php.html#60"><abbr title="App\Models\Data\Log\ReceiveLog::getMessage">getMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/ReceiveLog.php.html#173"><abbr title="App\Models\Data\Log\ReceiveLog::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/MYCF.php.html#19"><abbr title="App\Models\Data\OilMapping\MYCF::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HytOrg.php.html#19"><abbr title="App\Models\Data\OilMapping\HytOrg::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Fy.php.html#95"><abbr title="App\Models\Data\OilMapping\Fy::getAvailableOil">getAvailableOil</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/GBDW.php.html#21"><abbr title="App\Models\Data\OilMapping\GBDW::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Ghc.php.html#16"><abbr title="App\Models\Data\OilMapping\Ghc::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HG.php.html#11"><abbr title="App\Models\Data\OilMapping\HG::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HK.php.html#9"><abbr title="App\Models\Data\OilMapping\HK::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HLJH.php.html#9"><abbr title="App\Models\Data\OilMapping\HLJH::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HLL.php.html#9"><abbr title="App\Models\Data\OilMapping\HLL::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HR.php.html#19"><abbr title="App\Models\Data\OilMapping\HR::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HSL.php.html#19"><abbr title="App\Models\Data\OilMapping\HSL::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HYJY.php.html#9"><abbr title="App\Models\Data\OilMapping\HYJY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/HZ.php.html#9"><abbr title="App\Models\Data\OilMapping\HZ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/JDWC.php.html#20"><abbr title="App\Models\Data\OilMapping\JDWC::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/DESP2H8BBD.php.html#40"><abbr title="App\Models\Data\OilMapping\DESP2H8BBD::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/JF.php.html#19"><abbr title="App\Models\Data\OilMapping\JF::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/JTXY.php.html#9"><abbr title="App\Models\Data\OilMapping\JTXY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Jd.php.html#9"><abbr title="App\Models\Data\OilMapping\Jd::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/KY.php.html#19"><abbr title="App\Models\Data\OilMapping\KY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/LF.php.html#9"><abbr title="App\Models\Data\OilMapping\LF::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/LT.php.html#9"><abbr title="App\Models\Data\OilMapping\LT::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Lhys.php.html#9"><abbr title="App\Models\Data\OilMapping\Lhys::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/MB.php.html#31"><abbr title="App\Models\Data\OilMapping\MB::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/MK.php.html#9"><abbr title="App\Models\Data\OilMapping\MK::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/MTL.php.html#19"><abbr title="App\Models\Data\OilMapping\MTL::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/MY.php.html#19"><abbr title="App\Models\Data\OilMapping\MY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/MYB.php.html#20"><abbr title="App\Models\Data\OilMapping\MYB::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Fy.php.html#23"><abbr title="App\Models\Data\OilMapping\Fy::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/DESP2C637M.php.html#40"><abbr title="App\Models\Data\OilMapping\DESP2C637M::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/RequestLog.php.html#24"><abbr title="App\Models\Data\Log\RequestLog::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Basic.php.html#18"><abbr title="App\Models\Data\OilMapping\Basic::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/RequestLog.php.html#62"><abbr title="App\Models\Data\Log\RequestLog::getMessage">getMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/RequestLog.php.html#112"><abbr title="App\Models\Data\Log\RequestLog::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/RequestLog.php.html#175"><abbr title="App\Models\Data\Log\RequestLog::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/ResponseLog.php.html#19"><abbr title="App\Models\Data\Log\ResponseLog::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/ResponseLog.php.html#47"><abbr title="App\Models\Data\Log\ResponseLog::getMessage">getMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/ResponseLog.php.html#97"><abbr title="App\Models\Data\Log\ResponseLog::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Log/ResponseLog.php.html#148"><abbr title="App\Models\Data\Log\ResponseLog::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/AJSW.php.html#19"><abbr title="App\Models\Data\OilMapping\AJSW::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Ad.php.html#30"><abbr title="App\Models\Data\OilMapping\Ad::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Ad.php.html#172"><abbr title="App\Models\Data\OilMapping\Ad::gcJ02CoordinateConvertToBd">gcJ02CoordinateConvertToBd</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/BBSP.php.html#9"><abbr title="App\Models\Data\OilMapping\BBSP::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/BQ.php.html#9"><abbr title="App\Models\Data\OilMapping\BQ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Basic.php.html#24"><abbr title="App\Models\Data\OilMapping\Basic::getSpecialPrice">getSpecialPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/DESP2A6LA9.php.html#40"><abbr title="App\Models\Data\OilMapping\DESP2A6LA9::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/BdtOrg.php.html#20"><abbr title="App\Models\Data\OilMapping\BdtOrg::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/CFT.php.html#9"><abbr title="App\Models\Data\OilMapping\CFT::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/CHTX.php.html#20"><abbr title="App\Models\Data\OilMapping\CHTX::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/CIEC.php.html#20"><abbr title="App\Models\Data\OilMapping\CIEC::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/CN.php.html#20"><abbr title="App\Models\Data\OilMapping\CN::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Cfhy.php.html#19"><abbr title="App\Models\Data\OilMapping\Cfhy::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Common.php.html#24"><abbr title="App\Models\Data\OilMapping\Common::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Common.php.html#95"><abbr title="App\Models\Data\OilMapping\Common::filterCommonField">filterCommonField</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Common.php.html#120"><abbr title="App\Models\Data\OilMapping\Common::removeRepeatOilAll">removeRepeatOilAll</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Common.php.html#164"><abbr title="App\Models\Data\OilMapping\Common::getOrderNeedGunInfoForSupplierCode">getOrderNeedGunInfoForSupplierCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/Common.php.html#187"><abbr title="App\Models\Data\OilMapping\Common::comparePriceAndDealRepeatOil">comparePriceAndDealRepeatOil</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilMapping/DDE.php.html#20"><abbr title="App\Models\Data\OilMapping\DDE::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trade/Order.php.html#112"><abbr title="App\Models\Data\Trade\Order::default">default</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="OrderAssoc.php.html#456"><abbr title="App\Models\Data\OrderAssoc::getData">getData</abbr></a></td><td class="text-right">506</td></tr>
       <tr><td><a href="OilMapping/Ad.php.html#30"><abbr title="App\Models\Data\OilMapping\Ad::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="OrderAssoc.php.html#269"><abbr title="App\Models\Data\OrderAssoc::getOrderInfoByWhere">getOrderInfoByWhere</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="StationPushCondition.php.html#22"><abbr title="App\Models\Data\StationPushCondition::getData">getData</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="OrderAssoc.php.html#335"><abbr title="App\Models\Data\OrderAssoc::getOneOrderByWhereAndLock">getOneOrderByWhereAndLock</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="StationUniqueMapping.php.html#248"><abbr title="App\Models\Data\StationUniqueMapping::getStationUniqueMappingByWhere">getStationUniqueMappingByWhere</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="AuthInfo.php.html#81"><abbr title="App\Models\Data\AuthInfo::getAuthInfoByWhere">getAuthInfoByWhere</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="CompanyMapping.php.html#28"><abbr title="App\Models\Data\CompanyMapping::getCompanyByWhere">getCompanyByWhere</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="OilMapping/Common.php.html#24"><abbr title="App\Models\Data\OilMapping\Common::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="OilMapping/Common.php.html#187"><abbr title="App\Models\Data\OilMapping\Common::comparePriceAndDealRepeatOil">comparePriceAndDealRepeatOil</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="StationPrice.php.html#181"><abbr title="App\Models\Data\StationPrice::updateByWhere">updateByWhere</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="StationUniqueMapping.php.html#181"><abbr title="App\Models\Data\StationUniqueMapping::getStationPriceByExternalStationId">getStationPriceByExternalStationId</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="OilPriceMapping/CZB.php.html#12"><abbr title="App\Models\Data\OilPriceMapping\CZB::getOilPriceData">getOilPriceData</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="StationUniqueMapping.php.html#76"><abbr title="App\Models\Data\StationUniqueMapping::getStationPriceByStationId">getStationPriceByStationId</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="StationPushSwitch.php.html#130"><abbr title="App\Models\Data\StationPushSwitch::getPushedStationList">getPushedStationList</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="OrderAssoc.php.html#113"><abbr title="App\Models\Data\OrderAssoc::updateOrderInfoByOrderId">updateOrderInfoByOrderId</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Log/RequestLog.php.html#112"><abbr title="App\Models\Data\Log\RequestLog::getData">getData</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="OilMapping/Basic.php.html#24"><abbr title="App\Models\Data\OilMapping\Basic::getSpecialPrice">getSpecialPrice</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Log/QueueLog.php.html#100"><abbr title="App\Models\Data\Log\QueueLog::getData">getData</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="StationPrice.php.html#148"><abbr title="App\Models\Data\StationPrice::getDataByWhere">getDataByWhere</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Log/ReceiveLog.php.html#114"><abbr title="App\Models\Data\Log\ReceiveLog::getData">getData</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="OilMapping/Common.php.html#120"><abbr title="App\Models\Data\OilMapping\Common::removeRepeatOilAll">removeRepeatOilAll</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="StationUniqueMapping.php.html#126"><abbr title="App\Models\Data\StationUniqueMapping::getStationDataByStationIdAndCondition">getStationDataByStationIdAndCondition</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="StationPushRecord.php.html#39"><abbr title="App\Models\Data\StationPushRecord::getData">getData</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="OilMapping/Fy.php.html#23"><abbr title="App\Models\Data\OilMapping\Fy::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="OilMapping/SFFY.php.html#28"><abbr title="App\Models\Data\OilMapping\SFFY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="StationPrice.php.html#23"><abbr title="App\Models\Data\StationPrice::create">create</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Log/ResponseLog.php.html#97"><abbr title="App\Models\Data\Log\ResponseLog::getData">getData</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Base.php.html#239"><abbr title="App\Models\Data\Base::getStationPushRecordTableSqlByTime">getStationPushRecordTableSqlByTime</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Base.php.html#34"><abbr title="App\Models\Data\Base::getLogTableSqlByTime">getLogTableSqlByTime</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="OilMapping/ZEY.php.html#24"><abbr title="App\Models\Data\OilMapping\ZEY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="StationPrice.php.html#213"><abbr title="App\Models\Data\StationPrice::getData">getData</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="OrderAssoc.php.html#528"><abbr title="App\Models\Data\OrderAssoc::getChart">getChart</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="OilMapping/SHENGMAN.php.html#19"><abbr title="App\Models\Data\OilMapping\SHENGMAN::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="OilMapping/CHTX.php.html#20"><abbr title="App\Models\Data\OilMapping\CHTX::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="OrderAssoc.php.html#165"><abbr title="App\Models\Data\OrderAssoc::updateOrderInfoById">updateOrderInfoById</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="OrderAssoc.php.html#394"><abbr title="App\Models\Data\OrderAssoc::insertBySearchForPlatform">insertBySearchForPlatform</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Log/RequestLog.php.html#62"><abbr title="App\Models\Data\Log\RequestLog::getMessage">getMessage</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Log/ResponseLog.php.html#47"><abbr title="App\Models\Data\Log\ResponseLog::getMessage">getMessage</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Log/ReceiveLog.php.html#60"><abbr title="App\Models\Data\Log\ReceiveLog::getMessage">getMessage</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="OrderRefund.php.html#58"><abbr title="App\Models\Data\OrderRefund::getData">getData</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Log/QueueLog.php.html#50"><abbr title="App\Models\Data\Log\QueueLog::getMessage">getMessage</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="OilMapping/YGY.php.html#126"><abbr title="App\Models\Data\OilMapping\YGY::getAvailableOil">getAvailableOil</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="OilMapping/ZJ.php.html#21"><abbr title="App\Models\Data\OilMapping\ZJ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="AuthInfo.php.html#129"><abbr title="App\Models\Data\AuthInfo::getAuthInfoByRoleCode">getAuthInfoByRoleCode</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="OilMapping/DESP2H8BBD.php.html#40"><abbr title="App\Models\Data\OilMapping\DESP2H8BBD::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="OilMapping/DESP2C637M.php.html#40"><abbr title="App\Models\Data\OilMapping\DESP2C637M::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Base.php.html#159"><abbr title="App\Models\Data\Base::filterField">filterField</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="OilMapping/DESP2A6LA9.php.html#40"><abbr title="App\Models\Data\OilMapping\DESP2A6LA9::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="OilMapping/YLZ.php.html#19"><abbr title="App\Models\Data\OilMapping\YLZ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="DockingPlatformInfo.php.html#65"><abbr title="App\Models\Data\DockingPlatformInfo::getFieldsByNameAbbreviation">getFieldsByNameAbbreviation</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="OilPriceMapping/Basic.php.html#51"><abbr title="App\Models\Data\OilPriceMapping\Basic::filterField">filterField</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="OilMapping/GBDW.php.html#21"><abbr title="App\Models\Data\OilMapping\GBDW::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/KY.php.html#19"><abbr title="App\Models\Data\OilMapping\KY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/Ygj.php.html#9"><abbr title="App\Models\Data\OilMapping\Ygj::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/HG.php.html#11"><abbr title="App\Models\Data\OilMapping\HG::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OrderAssoc.php.html#223"><abbr title="App\Models\Data\OrderAssoc::getOrderInfoBySelfOrderId">getOrderInfoBySelfOrderId</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/RQ.php.html#30"><abbr title="App\Models\Data\OilMapping\RQ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/MYCF.php.html#19"><abbr title="App\Models\Data\OilMapping\MYCF::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/XM.php.html#19"><abbr title="App\Models\Data\OilMapping\XM::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/YGY.php.html#31"><abbr title="App\Models\Data\OilMapping\YGY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="RegionalInfo.php.html#94"><abbr title="App\Models\Data\RegionalInfo::getNameByCode">getNameByCode</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OilMapping/ZZ_BJ.php.html#19"><abbr title="App\Models\Data\OilMapping\ZZ_BJ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/SPCJ.php.html#35"><abbr title="App\Models\Data\OilMapping\SPCJ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/WZYT.php.html#19"><abbr title="App\Models\Data\OilMapping\WZYT::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilPriceMapping/Basic.php.html#84"><abbr title="App\Models\Data\OilPriceMapping\Basic::dealOilStationDataNullValue">dealOilStationDataNullValue</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilPriceMapping/GS.php.html#12"><abbr title="App\Models\Data\OilPriceMapping\GS::getOilPriceData">getOilPriceData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/ZZ_TJ.php.html#19"><abbr title="App\Models\Data\OilMapping\ZZ_TJ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/ZZ_AH.php.html#19"><abbr title="App\Models\Data\OilMapping\ZZ_AH::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/QDMY.php.html#19"><abbr title="App\Models\Data\OilMapping\QDMY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/HSL.php.html#19"><abbr title="App\Models\Data\OilMapping\HSL::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="StationPushSwitch.php.html#34"><abbr title="App\Models\Data\StationPushSwitch::getData">getData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SupplierInfo.php.html#24"><abbr title="App\Models\Data\SupplierInfo::getSupplierNameBySupplierCode">getSupplierNameBySupplierCode</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Base.php.html#211"><abbr title="App\Models\Data\Base::dealOilStationDataNullValue">dealOilStationDataNullValue</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/ZZ.php.html#19"><abbr title="App\Models\Data\OilMapping\ZZ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="OilMapping/ZTO.php.html#19"><abbr title="App\Models\Data\OilMapping\ZTO::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/STY.php.html#19"><abbr title="App\Models\Data\OilMapping\STY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="RolePermission.php.html#48"><abbr title="App\Models\Data\RolePermission::update">update</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Log/FailedJobs.php.html#17"><abbr title="App\Models\Data\Log\FailedJobs::getData">getData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/SFSX.php.html#19"><abbr title="App\Models\Data\OilMapping\SFSX::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/SFFYSIMPLE.php.html#20"><abbr title="App\Models\Data\OilMapping\SFFYSIMPLE::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilPriceMapping/Basic.php.html#25"><abbr title="App\Models\Data\OilPriceMapping\Basic::replaceKey">replaceKey</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/YXT.php.html#19"><abbr title="App\Models\Data\OilMapping\YXT::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="RegionalInfo.php.html#46"><abbr title="App\Models\Data\RegionalInfo::getCityCodeByNameFilterUnit">getCityCodeByNameFilterUnit</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Base.php.html#134"><abbr title="App\Models\Data\Base::replaceKey">replaceKey</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Permission.php.html#50"><abbr title="App\Models\Data\Permission::getTree">getTree</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/HR.php.html#19"><abbr title="App\Models\Data\OilMapping\HR::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/ZT.php.html#19"><abbr title="App\Models\Data\OilMapping\ZT::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OilMapping/JF.php.html#19"><abbr title="App\Models\Data\OilMapping\JF::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Base.php.html#200"><abbr title="App\Models\Data\Base::popNotInFillAbleElement">popNotInFillAbleElement</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AuthInfo.php.html#163"><abbr title="App\Models\Data\AuthInfo::getData">getData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/WJY.php.html#20"><abbr title="App\Models\Data\DecodingQrCodeAdaptation\WJY::getAdaptationResult">getAdaptationResult</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DecodingQrCodeAdaptation/ZY.php.html#23"><abbr title="App\Models\Data\DecodingQrCodeAdaptation\ZY::getAdaptationResult">getAdaptationResult</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AuthInfo.php.html#51"><abbr title="App\Models\Data\AuthInfo::getAuthInfoFieldByNameAbbreviation">getAuthInfoFieldByNameAbbreviation</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DockingPlatformInfo.php.html#14"><abbr title="App\Models\Data\DockingPlatformInfo::getPlatformNameByNameAbbreviation">getPlatformNameByNameAbbreviation</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/ZLGX.php.html#19"><abbr title="App\Models\Data\OilMapping\ZLGX::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Log/ReceiveLog.php.html#24"><abbr title="App\Models\Data\Log\ReceiveLog::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/CIEC.php.html#20"><abbr title="App\Models\Data\OilMapping\CIEC::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/AJSW.php.html#19"><abbr title="App\Models\Data\OilMapping\AJSW::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/MTL.php.html#19"><abbr title="App\Models\Data\OilMapping\MTL::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OrderAssoc.php.html#47"><abbr title="App\Models\Data\OrderAssoc::insert">insert</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/JDWC.php.html#20"><abbr title="App\Models\Data\OilMapping\JDWC::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/CN.php.html#20"><abbr title="App\Models\Data\OilMapping\CN::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/LF.php.html#9"><abbr title="App\Models\Data\OilMapping\LF::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/Cfhy.php.html#19"><abbr title="App\Models\Data\OilMapping\Cfhy::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OilMapping/MB.php.html#31"><abbr title="App\Models\Data\OilMapping\MB::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OrderAssoc.php.html#201"><abbr title="App\Models\Data\OrderAssoc::getOrderInfoByPlatformOrderId">getOrderInfoByPlatformOrderId</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OrderRefund.php.html#49"><abbr title="App\Models\Data\OrderRefund::getMessage">getMessage</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Permission.php.html#36"><abbr title="App\Models\Data\Permission::getSelect">getSelect</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RegionalInfo.php.html#24"><abbr title="App\Models\Data\RegionalInfo::getCityCodeByName">getCityCodeByName</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Role.php.html#34"><abbr title="App\Models\Data\Role::getSelect">getSelect</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Role.php.html#13"><abbr title="App\Models\Data\Role::getData">getData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RolePermission.php.html#112"><abbr title="App\Models\Data\RolePermission::create">create</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OrderAssoc.php.html#92"><abbr title="App\Models\Data\OrderAssoc::updateSelfOrderStatus">updateSelfOrderStatus</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="StationUniqueMapping.php.html#55"><abbr title="App\Models\Data\StationUniqueMapping::getDataByStationId">getDataByStationId</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OrderAssoc.php.html#84"><abbr title="App\Models\Data\OrderAssoc::getMessage">getMessage</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OrderRefund.php.html#23"><abbr title="App\Models\Data\OrderRefund::insert">insert</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AuthConfig.php.html#14"><abbr title="App\Models\Data\AuthConfig::getAuthConfigValByName">getAuthConfigValByName</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/ZJKA.php.html#11"><abbr title="App\Models\Data\OilMapping\ZJKA::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/HytOrg.php.html#19"><abbr title="App\Models\Data\OilMapping\HytOrg::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AuthConfig.php.html#48"><abbr title="App\Models\Data\AuthConfig::getData">getData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AuthInfo.php.html#23"><abbr title="App\Models\Data\AuthInfo::getAuthInfoByAccessKey">getAuthInfoByAccessKey</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DecodingResultEntity.php.html#26"><abbr title="App\Models\Data\DecodingResultEntity::__construct">__construct</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DictTag.php.html#12"><abbr title="App\Models\Data\DictTag::getRebateLabel">getRebateLabel</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DockingPlatformInfo.php.html#29"><abbr title="App\Models\Data\DockingPlatformInfo::getData">getData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Log/QueueLog.php.html#13"><abbr title="App\Models\Data\Log\QueueLog::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Log/RequestLog.php.html#24"><abbr title="App\Models\Data\Log\RequestLog::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/BdtOrg.php.html#20"><abbr title="App\Models\Data\OilMapping\BdtOrg::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/Common.php.html#164"><abbr title="App\Models\Data\OilMapping\Common::getOrderNeedGunInfoForSupplierCode">getOrderNeedGunInfoForSupplierCode</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/DDE.php.html#20"><abbr title="App\Models\Data\OilMapping\DDE::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/Fy.php.html#95"><abbr title="App\Models\Data\OilMapping\Fy::getAvailableOil">getAvailableOil</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/Ghc.php.html#16"><abbr title="App\Models\Data\OilMapping\Ghc::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/HK.php.html#9"><abbr title="App\Models\Data\OilMapping\HK::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SupplierInfo.php.html#50"><abbr title="App\Models\Data\SupplierInfo::getData">getData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/TC.php.html#19"><abbr title="App\Models\Data\OilMapping\TC::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/SP.php.html#35"><abbr title="App\Models\Data\OilMapping\SP::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/SQ.php.html#19"><abbr title="App\Models\Data\OilMapping\SQ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AuthConfig.php.html#31"><abbr title="App\Models\Data\AuthConfig::getAuthConfigNameByVal">getAuthConfigNameByVal</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/PCKJ.php.html#19"><abbr title="App\Models\Data\OilMapping\PCKJ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/MYB.php.html#20"><abbr title="App\Models\Data\OilMapping\MYB::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/WSY.php.html#15"><abbr title="App\Models\Data\OilMapping\WSY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/XC.php.html#19"><abbr title="App\Models\Data\OilMapping\XC::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/MY.php.html#19"><abbr title="App\Models\Data\OilMapping\MY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OilMapping/YB.php.html#19"><abbr title="App\Models\Data\OilMapping\YB::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="StationPushSwitch.php.html#200"><abbr title="App\Models\Data\StationPushSwitch::getAll">getAll</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/BBSP.php.html#9"><abbr title="App\Models\Data\OilMapping\BBSP::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="StationPushSwitch.php.html#70"><abbr title="App\Models\Data\StationPushSwitch::getOpenAll">getOpenAll</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Base.php.html#117"><abbr title="App\Models\Data\Base::getCurYear">getCurYear</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Base.php.html#85"><abbr title="App\Models\Data\Base::getCurrentDayCountSqlByTable">getCurrentDayCountSqlByTable</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Log/BaseLog.php.html#15"><abbr title="App\Models\Data\Log\BaseLog::initFileLogChannel">initFileLogChannel</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AuthInfo.php.html#40"><abbr title="App\Models\Data\AuthInfo::getAuthInfoById">getAuthInfoById</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="StationPushRecord.php.html#12"><abbr title="App\Models\Data\StationPushRecord::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/XYDS.php.html#9"><abbr title="App\Models\Data\OilMapping\XYDS::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Log/ResponseLog.php.html#19"><abbr title="App\Models\Data\Log\ResponseLog::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/MK.php.html#9"><abbr title="App\Models\Data\OilMapping\MK::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/BQ.php.html#9"><abbr title="App\Models\Data\OilMapping\BQ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Permission.php.html#16"><abbr title="App\Models\Data\Permission::getData">getData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/LT.php.html#9"><abbr title="App\Models\Data\OilMapping\LT::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/Jd.php.html#9"><abbr title="App\Models\Data\OilMapping\Jd::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/JTXY.php.html#9"><abbr title="App\Models\Data\OilMapping\JTXY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OrderAssoc.php.html#597"><abbr title="App\Models\Data\OrderAssoc::getRetryOrderById">getRetryOrderById</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/HZ.php.html#9"><abbr title="App\Models\Data\OilMapping\HZ::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/HYJY.php.html#9"><abbr title="App\Models\Data\OilMapping\HYJY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/HLL.php.html#9"><abbr title="App\Models\Data\OilMapping\HLL::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/RY.php.html#9"><abbr title="App\Models\Data\OilMapping\RY::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/HLJH.php.html#9"><abbr title="App\Models\Data\OilMapping\HLJH::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/YBT.php.html#9"><abbr title="App\Models\Data\OilMapping\YBT::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RegionalInfo.php.html#133"><abbr title="App\Models\Data\RegionalInfo::getParentCodeByChildren">getParentCodeByChildren</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/RRS.php.html#9"><abbr title="App\Models\Data\OilMapping\RRS::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Role.php.html#78"><abbr title="App\Models\Data\Role::getValidateData">getValidateData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RolePermission.php.html#11"><abbr title="App\Models\Data\RolePermission::getData">getData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/Lhys.php.html#9"><abbr title="App\Models\Data\OilMapping\Lhys::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OilMapping/CFT.php.html#9"><abbr title="App\Models\Data\OilMapping\CFT::getOilStationData">getOilStationData</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:44:39 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([117,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([234,0,0,0,0,0,0,0,0,0,0,4], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,14,"<a href=\"AuthConfig.php.html#10\">App\\Models\\Data\\AuthConfig<\/a>"],[0,39,"<a href=\"AuthInfo.php.html#18\">App\\Models\\Data\\AuthInfo<\/a>"],[0,49,"<a href=\"Base.php.html#15\">App\\Models\\Data\\Base<\/a>"],[0,3,"<a href=\"Bill.php.html#7\">App\\Models\\Data\\Bill<\/a>"],[0,1,"<a href=\"Common.php.html#7\">App\\Models\\Data\\Common<\/a>"],[0,17,"<a href=\"CompanyMapping.php.html#14\">App\\Models\\Data\\CompanyMapping<\/a>"],[0,1,"<a href=\"DecodingQrCodeAdaptation\/BASE.php.html#9\">App\\Models\\Data\\DecodingQrCodeAdaptation\\BASE<\/a>"],[0,2,"<a href=\"DecodingQrCodeAdaptation\/BASIC.php.html#10\">App\\Models\\Data\\DecodingQrCodeAdaptation\\BASIC<\/a>"],[0,1,"<a href=\"DecodingQrCodeAdaptation\/DIANDI.php.html#9\">App\\Models\\Data\\DecodingQrCodeAdaptation\\DIANDI<\/a>"],[0,1,"<a href=\"DecodingQrCodeAdaptation\/SP.php.html#9\">App\\Models\\Data\\DecodingQrCodeAdaptation\\SP<\/a>"],[0,4,"<a href=\"DecodingQrCodeAdaptation\/WJY.php.html#10\">App\\Models\\Data\\DecodingQrCodeAdaptation\\WJY<\/a>"],[0,1,"<a href=\"DecodingQrCodeAdaptation\/XY.php.html#9\">App\\Models\\Data\\DecodingQrCodeAdaptation\\XY<\/a>"],[0,4,"<a href=\"DecodingQrCodeAdaptation\/ZY.php.html#13\">App\\Models\\Data\\DecodingQrCodeAdaptation\\ZY<\/a>"],[0,3,"<a href=\"DecodingResultEntity.php.html#6\">App\\Models\\Data\\DecodingResultEntity<\/a>"],[0,3,"<a href=\"DictTag.php.html#8\">App\\Models\\Data\\DictTag<\/a>"],[0,19,"<a href=\"DockingPlatformInfo.php.html#10\">App\\Models\\Data\\DockingPlatformInfo<\/a>"],[0,2,"<a href=\"Log\/BaseLog.php.html#8\">App\\Models\\Data\\Log\\BaseLog<\/a>"],[0,6,"<a href=\"Log\/FailedJobs.php.html#9\">App\\Models\\Data\\Log\\FailedJobs<\/a>"],[0,25,"<a href=\"Log\/QueueLog.php.html#11\">App\\Models\\Data\\Log\\QueueLog<\/a>"],[0,26,"<a href=\"Log\/ReceiveLog.php.html#12\">App\\Models\\Data\\Log\\ReceiveLog<\/a>"],[0,26,"<a href=\"Log\/RequestLog.php.html#11\">App\\Models\\Data\\Log\\RequestLog<\/a>"],[0,22,"<a href=\"Log\/ResponseLog.php.html#11\">App\\Models\\Data\\Log\\ResponseLog<\/a>"],[0,4,"<a href=\"OilMapping\/AJSW.php.html#10\">App\\Models\\Data\\OilMapping\\AJSW<\/a>"],[0,19,"<a href=\"OilMapping\/Ad.php.html#16\">App\\Models\\Data\\OilMapping\\Ad<\/a>"],[0,2,"<a href=\"OilMapping\/BBSP.php.html#7\">App\\Models\\Data\\OilMapping\\BBSP<\/a>"],[0,2,"<a href=\"OilMapping\/BQ.php.html#7\">App\\Models\\Data\\OilMapping\\BQ<\/a>"],[0,15,"<a href=\"OilMapping\/Basic.php.html#14\">App\\Models\\Data\\OilMapping\\Basic<\/a>"],[0,3,"<a href=\"OilMapping\/BdtOrg.php.html#11\">App\\Models\\Data\\OilMapping\\BdtOrg<\/a>"],[0,2,"<a href=\"OilMapping\/CFT.php.html#7\">App\\Models\\Data\\OilMapping\\CFT<\/a>"],[0,9,"<a href=\"OilMapping\/CHTX.php.html#11\">App\\Models\\Data\\OilMapping\\CHTX<\/a>"],[0,4,"<a href=\"OilMapping\/CIEC.php.html#11\">App\\Models\\Data\\OilMapping\\CIEC<\/a>"],[0,4,"<a href=\"OilMapping\/CN.php.html#11\">App\\Models\\Data\\OilMapping\\CN<\/a>"],[0,4,"<a href=\"OilMapping\/Cfhy.php.html#10\">App\\Models\\Data\\OilMapping\\Cfhy<\/a>"],[0,44,"<a href=\"OilMapping\/Common.php.html#13\">App\\Models\\Data\\OilMapping\\Common<\/a>"],[0,3,"<a href=\"OilMapping\/DDE.php.html#11\">App\\Models\\Data\\OilMapping\\DDE<\/a>"],[0,8,"<a href=\"OilMapping\/DESP2A6LA9.php.html#13\">App\\Models\\Data\\OilMapping\\DESP2A6LA9<\/a>"],[0,8,"<a href=\"OilMapping\/DESP2C637M.php.html#13\">App\\Models\\Data\\OilMapping\\DESP2C637M<\/a>"],[0,8,"<a href=\"OilMapping\/DESP2H8BBD.php.html#13\">App\\Models\\Data\\OilMapping\\DESP2H8BBD<\/a>"],[0,14,"<a href=\"OilMapping\/Fy.php.html#14\">App\\Models\\Data\\OilMapping\\Fy<\/a>"],[0,7,"<a href=\"OilMapping\/GBDW.php.html#12\">App\\Models\\Data\\OilMapping\\GBDW<\/a>"],[0,3,"<a href=\"OilMapping\/Ghc.php.html#14\">App\\Models\\Data\\OilMapping\\Ghc<\/a>"],[0,7,"<a href=\"OilMapping\/HG.php.html#9\">App\\Models\\Data\\OilMapping\\HG<\/a>"],[0,3,"<a href=\"OilMapping\/HK.php.html#7\">App\\Models\\Data\\OilMapping\\HK<\/a>"],[0,2,"<a href=\"OilMapping\/HLJH.php.html#7\">App\\Models\\Data\\OilMapping\\HLJH<\/a>"],[0,2,"<a href=\"OilMapping\/HLL.php.html#7\">App\\Models\\Data\\OilMapping\\HLL<\/a>"],[0,5,"<a href=\"OilMapping\/HR.php.html#10\">App\\Models\\Data\\OilMapping\\HR<\/a>"],[0,6,"<a href=\"OilMapping\/HSL.php.html#10\">App\\Models\\Data\\OilMapping\\HSL<\/a>"],[0,2,"<a href=\"OilMapping\/HYJY.php.html#7\">App\\Models\\Data\\OilMapping\\HYJY<\/a>"],[0,2,"<a href=\"OilMapping\/HZ.php.html#7\">App\\Models\\Data\\OilMapping\\HZ<\/a>"],[0,3,"<a href=\"OilMapping\/HytOrg.php.html#10\">App\\Models\\Data\\OilMapping\\HytOrg<\/a>"],[0,4,"<a href=\"OilMapping\/JDWC.php.html#11\">App\\Models\\Data\\OilMapping\\JDWC<\/a>"],[0,5,"<a href=\"OilMapping\/JF.php.html#10\">App\\Models\\Data\\OilMapping\\JF<\/a>"],[0,2,"<a href=\"OilMapping\/JTXY.php.html#7\">App\\Models\\Data\\OilMapping\\JTXY<\/a>"],[0,2,"<a href=\"OilMapping\/Jd.php.html#7\">App\\Models\\Data\\OilMapping\\Jd<\/a>"],[0,7,"<a href=\"OilMapping\/KY.php.html#10\">App\\Models\\Data\\OilMapping\\KY<\/a>"],[0,4,"<a href=\"OilMapping\/LF.php.html#7\">App\\Models\\Data\\OilMapping\\LF<\/a>"],[0,2,"<a href=\"OilMapping\/LT.php.html#7\">App\\Models\\Data\\OilMapping\\LT<\/a>"],[0,2,"<a href=\"OilMapping\/Lhys.php.html#7\">App\\Models\\Data\\OilMapping\\Lhys<\/a>"],[0,4,"<a href=\"OilMapping\/MB.php.html#11\">App\\Models\\Data\\OilMapping\\MB<\/a>"],[0,2,"<a href=\"OilMapping\/MK.php.html#7\">App\\Models\\Data\\OilMapping\\MK<\/a>"],[0,4,"<a href=\"OilMapping\/MTL.php.html#10\">App\\Models\\Data\\OilMapping\\MTL<\/a>"],[0,3,"<a href=\"OilMapping\/MY.php.html#10\">App\\Models\\Data\\OilMapping\\MY<\/a>"],[0,3,"<a href=\"OilMapping\/MYB.php.html#11\">App\\Models\\Data\\OilMapping\\MYB<\/a>"],[0,7,"<a href=\"OilMapping\/MYCF.php.html#10\">App\\Models\\Data\\OilMapping\\MYCF<\/a>"],[0,3,"<a href=\"OilMapping\/PCKJ.php.html#10\">App\\Models\\Data\\OilMapping\\PCKJ<\/a>"],[0,6,"<a href=\"OilMapping\/QDMY.php.html#10\">App\\Models\\Data\\OilMapping\\QDMY<\/a>"],[0,7,"<a href=\"OilMapping\/RQ.php.html#10\">App\\Models\\Data\\OilMapping\\RQ<\/a>"],[0,2,"<a href=\"OilMapping\/RRS.php.html#7\">App\\Models\\Data\\OilMapping\\RRS<\/a>"],[0,2,"<a href=\"OilMapping\/RY.php.html#7\">App\\Models\\Data\\OilMapping\\RY<\/a>"],[0,10,"<a href=\"OilMapping\/SFFY.php.html#14\">App\\Models\\Data\\OilMapping\\SFFY<\/a>"],[0,5,"<a href=\"OilMapping\/SFFYSIMPLE.php.html#11\">App\\Models\\Data\\OilMapping\\SFFYSIMPLE<\/a>"],[0,5,"<a href=\"OilMapping\/SFSX.php.html#10\">App\\Models\\Data\\OilMapping\\SFSX<\/a>"],[0,9,"<a href=\"OilMapping\/SHENGMAN.php.html#10\">App\\Models\\Data\\OilMapping\\SHENGMAN<\/a>"],[0,3,"<a href=\"OilMapping\/SP.php.html#10\">App\\Models\\Data\\OilMapping\\SP<\/a>"],[0,6,"<a href=\"OilMapping\/SPCJ.php.html#10\">App\\Models\\Data\\OilMapping\\SPCJ<\/a>"],[0,3,"<a href=\"OilMapping\/SQ.php.html#10\">App\\Models\\Data\\OilMapping\\SQ<\/a>"],[0,5,"<a href=\"OilMapping\/STY.php.html#10\">App\\Models\\Data\\OilMapping\\STY<\/a>"],[0,3,"<a href=\"OilMapping\/TC.php.html#10\">App\\Models\\Data\\OilMapping\\TC<\/a>"],[0,3,"<a href=\"OilMapping\/WSY.php.html#10\">App\\Models\\Data\\OilMapping\\WSY<\/a>"],[0,6,"<a href=\"OilMapping\/WZYT.php.html#10\">App\\Models\\Data\\OilMapping\\WZYT<\/a>"],[0,3,"<a href=\"OilMapping\/XC.php.html#10\">App\\Models\\Data\\OilMapping\\XC<\/a>"],[0,7,"<a href=\"OilMapping\/XM.php.html#10\">App\\Models\\Data\\OilMapping\\XM<\/a>"],[0,2,"<a href=\"OilMapping\/XYDS.php.html#7\">App\\Models\\Data\\OilMapping\\XYDS<\/a>"],[0,3,"<a href=\"OilMapping\/YB.php.html#10\">App\\Models\\Data\\OilMapping\\YB<\/a>"],[0,2,"<a href=\"OilMapping\/YBT.php.html#7\">App\\Models\\Data\\OilMapping\\YBT<\/a>"],[0,15,"<a href=\"OilMapping\/YGY.php.html#11\">App\\Models\\Data\\OilMapping\\YGY<\/a>"],[0,8,"<a href=\"OilMapping\/YLZ.php.html#10\">App\\Models\\Data\\OilMapping\\YLZ<\/a>"],[0,5,"<a href=\"OilMapping\/YXT.php.html#10\">App\\Models\\Data\\OilMapping\\YXT<\/a>"],[0,7,"<a href=\"OilMapping\/Ygj.php.html#7\">App\\Models\\Data\\OilMapping\\Ygj<\/a>"],[0,10,"<a href=\"OilMapping\/ZEY.php.html#10\">App\\Models\\Data\\OilMapping\\ZEY<\/a>"],[0,8,"<a href=\"OilMapping\/ZJ.php.html#12\">App\\Models\\Data\\OilMapping\\ZJ<\/a>"],[0,3,"<a href=\"OilMapping\/ZJKA.php.html#9\">App\\Models\\Data\\OilMapping\\ZJKA<\/a>"],[0,4,"<a href=\"OilMapping\/ZLGX.php.html#10\">App\\Models\\Data\\OilMapping\\ZLGX<\/a>"],[0,5,"<a href=\"OilMapping\/ZT.php.html#10\">App\\Models\\Data\\OilMapping\\ZT<\/a>"],[0,5,"<a href=\"OilMapping\/ZTO.php.html#10\">App\\Models\\Data\\OilMapping\\ZTO<\/a>"],[0,6,"<a href=\"OilMapping\/ZZ.php.html#10\">App\\Models\\Data\\OilMapping\\ZZ<\/a>"],[0,6,"<a href=\"OilMapping\/ZZ_AH.php.html#10\">App\\Models\\Data\\OilMapping\\ZZ_AH<\/a>"],[0,6,"<a href=\"OilMapping\/ZZ_BJ.php.html#10\">App\\Models\\Data\\OilMapping\\ZZ_BJ<\/a>"],[0,6,"<a href=\"OilMapping\/ZZ_TJ.php.html#10\">App\\Models\\Data\\OilMapping\\ZZ_TJ<\/a>"],[0,3,"<a href=\"OilMapping\/Zsh.php.html#7\">App\\Models\\Data\\OilMapping\\Zsh<\/a>"],[0,20,"<a href=\"OilPriceMapping\/Basic.php.html#12\">App\\Models\\Data\\OilPriceMapping\\Basic<\/a>"],[0,14,"<a href=\"OilPriceMapping\/CZB.php.html#10\">App\\Models\\Data\\OilPriceMapping\\CZB<\/a>"],[0,6,"<a href=\"OilPriceMapping\/GS.php.html#10\">App\\Models\\Data\\OilPriceMapping\\GS<\/a>"],[0,120,"<a href=\"OrderAssoc.php.html#14\">App\\Models\\Data\\OrderAssoc<\/a>"],[0,15,"<a href=\"OrderRefund.php.html#11\">App\\Models\\Data\\OrderRefund<\/a>"],[0,13,"<a href=\"Permission.php.html#12\">App\\Models\\Data\\Permission<\/a>"],[0,18,"<a href=\"RegionalInfo.php.html#19\">App\\Models\\Data\\RegionalInfo<\/a>"],[0,11,"<a href=\"Role.php.html#9\">App\\Models\\Data\\Role<\/a>"],[0,11,"<a href=\"RolePermission.php.html#9\">App\\Models\\Data\\RolePermission<\/a>"],[0,46,"<a href=\"StationPrice.php.html#14\">App\\Models\\Data\\StationPrice<\/a>"],[0,22,"<a href=\"StationPushCondition.php.html#12\">App\\Models\\Data\\StationPushCondition<\/a>"],[0,13,"<a href=\"StationPushRecord.php.html#10\">App\\Models\\Data\\StationPushRecord<\/a>"],[0,25,"<a href=\"StationPushSwitch.php.html#22\">App\\Models\\Data\\StationPushSwitch<\/a>"],[0,58,"<a href=\"StationUniqueMapping.php.html#15\">App\\Models\\Data\\StationUniqueMapping<\/a>"],[0,13,"<a href=\"SupplierInfo.php.html#12\">App\\Models\\Data\\SupplierInfo<\/a>"],[0,2,"<a href=\"Trade\/Oil.php.html#14\">App\\Models\\Data\\Trade\\Oil<\/a>"],[0,3,"<a href=\"Trade\/Order.php.html#14\">App\\Models\\Data\\Trade\\Order<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,3,"<a href=\"AuthConfig.php.html#14\">App\\Models\\Data\\AuthConfig::getAuthConfigValByName<\/a>"],[0,3,"<a href=\"AuthConfig.php.html#31\">App\\Models\\Data\\AuthConfig::getAuthConfigNameByVal<\/a>"],[0,3,"<a href=\"AuthConfig.php.html#48\">App\\Models\\Data\\AuthConfig::getData<\/a>"],[0,1,"<a href=\"AuthConfig.php.html#68\">App\\Models\\Data\\AuthConfig::update<\/a>"],[0,1,"<a href=\"AuthConfig.php.html#77\">App\\Models\\Data\\AuthConfig::create<\/a>"],[0,1,"<a href=\"AuthConfig.php.html#93\">App\\Models\\Data\\AuthConfig::delete<\/a>"],[0,1,"<a href=\"AuthConfig.php.html#104\">App\\Models\\Data\\AuthConfig::getFieldsByNames<\/a>"],[0,1,"<a href=\"AuthConfig.php.html#109\">App\\Models\\Data\\AuthConfig::updateByName<\/a>"],[0,3,"<a href=\"AuthInfo.php.html#23\">App\\Models\\Data\\AuthInfo::getAuthInfoByAccessKey<\/a>"],[0,2,"<a href=\"AuthInfo.php.html#40\">App\\Models\\Data\\AuthInfo::getAuthInfoById<\/a>"],[0,4,"<a href=\"AuthInfo.php.html#51\">App\\Models\\Data\\AuthInfo::getAuthInfoFieldByNameAbbreviation<\/a>"],[0,15,"<a href=\"AuthInfo.php.html#81\">App\\Models\\Data\\AuthInfo::getAuthInfoByWhere<\/a>"],[0,8,"<a href=\"AuthInfo.php.html#129\">App\\Models\\Data\\AuthInfo::getAuthInfoByRoleCode<\/a>"],[0,4,"<a href=\"AuthInfo.php.html#163\">App\\Models\\Data\\AuthInfo::getData<\/a>"],[0,1,"<a href=\"AuthInfo.php.html#192\">App\\Models\\Data\\AuthInfo::update<\/a>"],[0,1,"<a href=\"AuthInfo.php.html#209\">App\\Models\\Data\\AuthInfo::delete<\/a>"],[0,1,"<a href=\"AuthInfo.php.html#224\">App\\Models\\Data\\AuthInfo::create<\/a>"],[100,1,"<a href=\"Base.php.html#19\">App\\Models\\Data\\Base::handle<\/a>"],[0,10,"<a href=\"Base.php.html#34\">App\\Models\\Data\\Base::getLogTableSqlByTime<\/a>"],[0,2,"<a href=\"Base.php.html#85\">App\\Models\\Data\\Base::getCurrentDayCountSqlByTable<\/a>"],[0,1,"<a href=\"Base.php.html#106\">App\\Models\\Data\\Base::getCurWeekNo<\/a>"],[0,2,"<a href=\"Base.php.html#117\">App\\Models\\Data\\Base::getCurYear<\/a>"],[0,5,"<a href=\"Base.php.html#134\">App\\Models\\Data\\Base::replaceKey<\/a>"],[0,8,"<a href=\"Base.php.html#159\">App\\Models\\Data\\Base::filterField<\/a>"],[0,4,"<a href=\"Base.php.html#200\">App\\Models\\Data\\Base::popNotInFillAbleElement<\/a>"],[0,6,"<a href=\"Base.php.html#211\">App\\Models\\Data\\Base::dealOilStationDataNullValue<\/a>"],[0,10,"<a href=\"Base.php.html#239\">App\\Models\\Data\\Base::getStationPushRecordTableSqlByTime<\/a>"],[0,1,"<a href=\"Bill.php.html#10\">App\\Models\\Data\\Bill::handle<\/a>"],[0,1,"<a href=\"Bill.php.html#15\">App\\Models\\Data\\Bill::formatBillData<\/a>"],[0,1,"<a href=\"Bill.php.html#107\">App\\Models\\Data\\Bill::formatBillDataNew<\/a>"],[0,1,"<a href=\"Common.php.html#43\">App\\Models\\Data\\Common::handle<\/a>"],[0,15,"<a href=\"CompanyMapping.php.html#28\">App\\Models\\Data\\CompanyMapping::getCompanyByWhere<\/a>"],[0,1,"<a href=\"CompanyMapping.php.html#81\">App\\Models\\Data\\CompanyMapping::update<\/a>"],[0,1,"<a href=\"CompanyMapping.php.html#98\">App\\Models\\Data\\CompanyMapping::create<\/a>"],[0,1,"<a href=\"DecodingQrCodeAdaptation\/BASE.php.html#12\">App\\Models\\Data\\DecodingQrCodeAdaptation\\BASE::getAdaptationResult<\/a>"],[0,1,"<a href=\"DecodingQrCodeAdaptation\/BASIC.php.html#12\">App\\Models\\Data\\DecodingQrCodeAdaptation\\BASIC::handle<\/a>"],[100,1,"<a href=\"DecodingQrCodeAdaptation\/BASIC.php.html#16\">App\\Models\\Data\\DecodingQrCodeAdaptation\\BASIC::getAdaptationResult<\/a>"],[0,1,"<a href=\"DecodingQrCodeAdaptation\/DIANDI.php.html#12\">App\\Models\\Data\\DecodingQrCodeAdaptation\\DIANDI::getAdaptationResult<\/a>"],[0,1,"<a href=\"DecodingQrCodeAdaptation\/SP.php.html#12\">App\\Models\\Data\\DecodingQrCodeAdaptation\\SP::getAdaptationResult<\/a>"],[0,4,"<a href=\"DecodingQrCodeAdaptation\/WJY.php.html#20\">App\\Models\\Data\\DecodingQrCodeAdaptation\\WJY::getAdaptationResult<\/a>"],[0,1,"<a href=\"DecodingQrCodeAdaptation\/XY.php.html#11\">App\\Models\\Data\\DecodingQrCodeAdaptation\\XY::getAdaptationResult<\/a>"],[0,4,"<a href=\"DecodingQrCodeAdaptation\/ZY.php.html#23\">App\\Models\\Data\\DecodingQrCodeAdaptation\\ZY::getAdaptationResult<\/a>"],[0,3,"<a href=\"DecodingResultEntity.php.html#26\">App\\Models\\Data\\DecodingResultEntity::__construct<\/a>"],[0,3,"<a href=\"DictTag.php.html#12\">App\\Models\\Data\\DictTag::getRebateLabel<\/a>"],[0,4,"<a href=\"DockingPlatformInfo.php.html#14\">App\\Models\\Data\\DockingPlatformInfo::getPlatformNameByNameAbbreviation<\/a>"],[0,3,"<a href=\"DockingPlatformInfo.php.html#29\">App\\Models\\Data\\DockingPlatformInfo::getData<\/a>"],[0,1,"<a href=\"DockingPlatformInfo.php.html#50\">App\\Models\\Data\\DockingPlatformInfo::getSelect<\/a>"],[0,8,"<a href=\"DockingPlatformInfo.php.html#65\">App\\Models\\Data\\DockingPlatformInfo::getFieldsByNameAbbreviation<\/a>"],[0,1,"<a href=\"DockingPlatformInfo.php.html#95\">App\\Models\\Data\\DockingPlatformInfo::update<\/a>"],[0,1,"<a href=\"DockingPlatformInfo.php.html#112\">App\\Models\\Data\\DockingPlatformInfo::delete<\/a>"],[0,1,"<a href=\"DockingPlatformInfo.php.html#118\">App\\Models\\Data\\DockingPlatformInfo::create<\/a>"],[0,2,"<a href=\"Log\/BaseLog.php.html#15\">App\\Models\\Data\\Log\\BaseLog::initFileLogChannel<\/a>"],[0,1,"<a href=\"Log\/FailedJobs.php.html#11\">App\\Models\\Data\\Log\\FailedJobs::handle<\/a>"],[0,5,"<a href=\"Log\/FailedJobs.php.html#17\">App\\Models\\Data\\Log\\FailedJobs::getData<\/a>"],[0,3,"<a href=\"Log\/QueueLog.php.html#13\">App\\Models\\Data\\Log\\QueueLog::handle<\/a>"],[0,9,"<a href=\"Log\/QueueLog.php.html#50\">App\\Models\\Data\\Log\\QueueLog::getMessage<\/a>"],[0,12,"<a href=\"Log\/QueueLog.php.html#100\">App\\Models\\Data\\Log\\QueueLog::getData<\/a>"],[0,1,"<a href=\"Log\/QueueLog.php.html#159\">App\\Models\\Data\\Log\\QueueLog::getCurDayCount<\/a>"],[0,4,"<a href=\"Log\/ReceiveLog.php.html#24\">App\\Models\\Data\\Log\\ReceiveLog::handle<\/a>"],[0,9,"<a href=\"Log\/ReceiveLog.php.html#60\">App\\Models\\Data\\Log\\ReceiveLog::getMessage<\/a>"],[0,12,"<a href=\"Log\/ReceiveLog.php.html#114\">App\\Models\\Data\\Log\\ReceiveLog::getData<\/a>"],[0,1,"<a href=\"Log\/ReceiveLog.php.html#173\">App\\Models\\Data\\Log\\ReceiveLog::getCurDayCount<\/a>"],[0,3,"<a href=\"Log\/RequestLog.php.html#24\">App\\Models\\Data\\Log\\RequestLog::handle<\/a>"],[0,9,"<a href=\"Log\/RequestLog.php.html#62\">App\\Models\\Data\\Log\\RequestLog::getMessage<\/a>"],[0,13,"<a href=\"Log\/RequestLog.php.html#112\">App\\Models\\Data\\Log\\RequestLog::getData<\/a>"],[0,1,"<a href=\"Log\/RequestLog.php.html#175\">App\\Models\\Data\\Log\\RequestLog::getCurDayCount<\/a>"],[0,2,"<a href=\"Log\/ResponseLog.php.html#19\">App\\Models\\Data\\Log\\ResponseLog::handle<\/a>"],[0,9,"<a href=\"Log\/ResponseLog.php.html#47\">App\\Models\\Data\\Log\\ResponseLog::getMessage<\/a>"],[0,10,"<a href=\"Log\/ResponseLog.php.html#97\">App\\Models\\Data\\Log\\ResponseLog::getData<\/a>"],[0,1,"<a href=\"Log\/ResponseLog.php.html#148\">App\\Models\\Data\\Log\\ResponseLog::getCurDayCount<\/a>"],[0,4,"<a href=\"OilMapping\/AJSW.php.html#19\">App\\Models\\Data\\OilMapping\\AJSW::getOilStationData<\/a>"],[0,18,"<a href=\"OilMapping\/Ad.php.html#30\">App\\Models\\Data\\OilMapping\\Ad::getOilStationData<\/a>"],[0,1,"<a href=\"OilMapping\/Ad.php.html#172\">App\\Models\\Data\\OilMapping\\Ad::gcJ02CoordinateConvertToBd<\/a>"],[0,2,"<a href=\"OilMapping\/BBSP.php.html#9\">App\\Models\\Data\\OilMapping\\BBSP::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/BQ.php.html#9\">App\\Models\\Data\\OilMapping\\BQ::getOilStationData<\/a>"],[0,1,"<a href=\"OilMapping\/Basic.php.html#18\">App\\Models\\Data\\OilMapping\\Basic::handle<\/a>"],[100,1,"<a href=\"OilMapping\/Basic.php.html#22\">App\\Models\\Data\\OilMapping\\Basic::getOilStationData<\/a>"],[0,13,"<a href=\"OilMapping\/Basic.php.html#24\">App\\Models\\Data\\OilMapping\\Basic::getSpecialPrice<\/a>"],[0,3,"<a href=\"OilMapping\/BdtOrg.php.html#20\">App\\Models\\Data\\OilMapping\\BdtOrg::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/CFT.php.html#9\">App\\Models\\Data\\OilMapping\\CFT::getOilStationData<\/a>"],[0,9,"<a href=\"OilMapping\/CHTX.php.html#20\">App\\Models\\Data\\OilMapping\\CHTX::getOilStationData<\/a>"],[0,4,"<a href=\"OilMapping\/CIEC.php.html#20\">App\\Models\\Data\\OilMapping\\CIEC::getOilStationData<\/a>"],[0,4,"<a href=\"OilMapping\/CN.php.html#20\">App\\Models\\Data\\OilMapping\\CN::getOilStationData<\/a>"],[0,4,"<a href=\"OilMapping\/Cfhy.php.html#19\">App\\Models\\Data\\OilMapping\\Cfhy::getOilStationData<\/a>"],[0,14,"<a href=\"OilMapping\/Common.php.html#24\">App\\Models\\Data\\OilMapping\\Common::getOilStationData<\/a>"],[0,1,"<a href=\"OilMapping\/Common.php.html#95\">App\\Models\\Data\\OilMapping\\Common::filterCommonField<\/a>"],[0,12,"<a href=\"OilMapping\/Common.php.html#120\">App\\Models\\Data\\OilMapping\\Common::removeRepeatOilAll<\/a>"],[0,3,"<a href=\"OilMapping\/Common.php.html#164\">App\\Models\\Data\\OilMapping\\Common::getOrderNeedGunInfoForSupplierCode<\/a>"],[0,14,"<a href=\"OilMapping\/Common.php.html#187\">App\\Models\\Data\\OilMapping\\Common::comparePriceAndDealRepeatOil<\/a>"],[0,3,"<a href=\"OilMapping\/DDE.php.html#20\">App\\Models\\Data\\OilMapping\\DDE::getOilStationData<\/a>"],[0,8,"<a href=\"OilMapping\/DESP2A6LA9.php.html#40\">App\\Models\\Data\\OilMapping\\DESP2A6LA9::getOilStationData<\/a>"],[0,8,"<a href=\"OilMapping\/DESP2C637M.php.html#40\">App\\Models\\Data\\OilMapping\\DESP2C637M::getOilStationData<\/a>"],[0,8,"<a href=\"OilMapping\/DESP2H8BBD.php.html#40\">App\\Models\\Data\\OilMapping\\DESP2H8BBD::getOilStationData<\/a>"],[0,11,"<a href=\"OilMapping\/Fy.php.html#23\">App\\Models\\Data\\OilMapping\\Fy::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/Fy.php.html#95\">App\\Models\\Data\\OilMapping\\Fy::getAvailableOil<\/a>"],[0,7,"<a href=\"OilMapping\/GBDW.php.html#21\">App\\Models\\Data\\OilMapping\\GBDW::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/Ghc.php.html#16\">App\\Models\\Data\\OilMapping\\Ghc::getOilStationData<\/a>"],[0,7,"<a href=\"OilMapping\/HG.php.html#11\">App\\Models\\Data\\OilMapping\\HG::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/HK.php.html#9\">App\\Models\\Data\\OilMapping\\HK::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/HLJH.php.html#9\">App\\Models\\Data\\OilMapping\\HLJH::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/HLL.php.html#9\">App\\Models\\Data\\OilMapping\\HLL::getOilStationData<\/a>"],[0,5,"<a href=\"OilMapping\/HR.php.html#19\">App\\Models\\Data\\OilMapping\\HR::getOilStationData<\/a>"],[0,6,"<a href=\"OilMapping\/HSL.php.html#19\">App\\Models\\Data\\OilMapping\\HSL::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/HYJY.php.html#9\">App\\Models\\Data\\OilMapping\\HYJY::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/HZ.php.html#9\">App\\Models\\Data\\OilMapping\\HZ::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/HytOrg.php.html#19\">App\\Models\\Data\\OilMapping\\HytOrg::getOilStationData<\/a>"],[0,4,"<a href=\"OilMapping\/JDWC.php.html#20\">App\\Models\\Data\\OilMapping\\JDWC::getOilStationData<\/a>"],[0,5,"<a href=\"OilMapping\/JF.php.html#19\">App\\Models\\Data\\OilMapping\\JF::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/JTXY.php.html#9\">App\\Models\\Data\\OilMapping\\JTXY::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/Jd.php.html#9\">App\\Models\\Data\\OilMapping\\Jd::getOilStationData<\/a>"],[0,7,"<a href=\"OilMapping\/KY.php.html#19\">App\\Models\\Data\\OilMapping\\KY::getOilStationData<\/a>"],[0,4,"<a href=\"OilMapping\/LF.php.html#9\">App\\Models\\Data\\OilMapping\\LF::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/LT.php.html#9\">App\\Models\\Data\\OilMapping\\LT::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/Lhys.php.html#9\">App\\Models\\Data\\OilMapping\\Lhys::getOilStationData<\/a>"],[0,4,"<a href=\"OilMapping\/MB.php.html#31\">App\\Models\\Data\\OilMapping\\MB::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/MK.php.html#9\">App\\Models\\Data\\OilMapping\\MK::getOilStationData<\/a>"],[0,4,"<a href=\"OilMapping\/MTL.php.html#19\">App\\Models\\Data\\OilMapping\\MTL::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/MY.php.html#19\">App\\Models\\Data\\OilMapping\\MY::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/MYB.php.html#20\">App\\Models\\Data\\OilMapping\\MYB::getOilStationData<\/a>"],[0,7,"<a href=\"OilMapping\/MYCF.php.html#19\">App\\Models\\Data\\OilMapping\\MYCF::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/PCKJ.php.html#19\">App\\Models\\Data\\OilMapping\\PCKJ::getOilStationData<\/a>"],[0,6,"<a href=\"OilMapping\/QDMY.php.html#19\">App\\Models\\Data\\OilMapping\\QDMY::getOilStationData<\/a>"],[0,7,"<a href=\"OilMapping\/RQ.php.html#30\">App\\Models\\Data\\OilMapping\\RQ::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/RRS.php.html#9\">App\\Models\\Data\\OilMapping\\RRS::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/RY.php.html#9\">App\\Models\\Data\\OilMapping\\RY::getOilStationData<\/a>"],[0,10,"<a href=\"OilMapping\/SFFY.php.html#28\">App\\Models\\Data\\OilMapping\\SFFY::getOilStationData<\/a>"],[0,5,"<a href=\"OilMapping\/SFFYSIMPLE.php.html#20\">App\\Models\\Data\\OilMapping\\SFFYSIMPLE::getOilStationData<\/a>"],[0,5,"<a href=\"OilMapping\/SFSX.php.html#19\">App\\Models\\Data\\OilMapping\\SFSX::getOilStationData<\/a>"],[0,9,"<a href=\"OilMapping\/SHENGMAN.php.html#19\">App\\Models\\Data\\OilMapping\\SHENGMAN::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/SP.php.html#35\">App\\Models\\Data\\OilMapping\\SP::getOilStationData<\/a>"],[0,6,"<a href=\"OilMapping\/SPCJ.php.html#35\">App\\Models\\Data\\OilMapping\\SPCJ::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/SQ.php.html#19\">App\\Models\\Data\\OilMapping\\SQ::getOilStationData<\/a>"],[0,5,"<a href=\"OilMapping\/STY.php.html#19\">App\\Models\\Data\\OilMapping\\STY::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/TC.php.html#19\">App\\Models\\Data\\OilMapping\\TC::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/WSY.php.html#15\">App\\Models\\Data\\OilMapping\\WSY::getOilStationData<\/a>"],[0,6,"<a href=\"OilMapping\/WZYT.php.html#19\">App\\Models\\Data\\OilMapping\\WZYT::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/XC.php.html#19\">App\\Models\\Data\\OilMapping\\XC::getOilStationData<\/a>"],[0,7,"<a href=\"OilMapping\/XM.php.html#19\">App\\Models\\Data\\OilMapping\\XM::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/XYDS.php.html#9\">App\\Models\\Data\\OilMapping\\XYDS::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/YB.php.html#19\">App\\Models\\Data\\OilMapping\\YB::getOilStationData<\/a>"],[0,2,"<a href=\"OilMapping\/YBT.php.html#9\">App\\Models\\Data\\OilMapping\\YBT::getOilStationData<\/a>"],[0,7,"<a href=\"OilMapping\/YGY.php.html#31\">App\\Models\\Data\\OilMapping\\YGY::getOilStationData<\/a>"],[0,8,"<a href=\"OilMapping\/YGY.php.html#126\">App\\Models\\Data\\OilMapping\\YGY::getAvailableOil<\/a>"],[0,8,"<a href=\"OilMapping\/YLZ.php.html#19\">App\\Models\\Data\\OilMapping\\YLZ::getOilStationData<\/a>"],[0,5,"<a href=\"OilMapping\/YXT.php.html#19\">App\\Models\\Data\\OilMapping\\YXT::getOilStationData<\/a>"],[0,7,"<a href=\"OilMapping\/Ygj.php.html#9\">App\\Models\\Data\\OilMapping\\Ygj::getOilStationData<\/a>"],[0,10,"<a href=\"OilMapping\/ZEY.php.html#24\">App\\Models\\Data\\OilMapping\\ZEY::getOilStationData<\/a>"],[0,8,"<a href=\"OilMapping\/ZJ.php.html#21\">App\\Models\\Data\\OilMapping\\ZJ::getOilStationData<\/a>"],[0,3,"<a href=\"OilMapping\/ZJKA.php.html#11\">App\\Models\\Data\\OilMapping\\ZJKA::getOilStationData<\/a>"],[0,4,"<a href=\"OilMapping\/ZLGX.php.html#19\">App\\Models\\Data\\OilMapping\\ZLGX::getOilStationData<\/a>"],[0,5,"<a href=\"OilMapping\/ZT.php.html#19\">App\\Models\\Data\\OilMapping\\ZT::getOilStationData<\/a>"],[0,5,"<a href=\"OilMapping\/ZTO.php.html#19\">App\\Models\\Data\\OilMapping\\ZTO::getOilStationData<\/a>"],[0,6,"<a href=\"OilMapping\/ZZ.php.html#19\">App\\Models\\Data\\OilMapping\\ZZ::getOilStationData<\/a>"],[0,6,"<a href=\"OilMapping\/ZZ_AH.php.html#19\">App\\Models\\Data\\OilMapping\\ZZ_AH::getOilStationData<\/a>"],[0,6,"<a href=\"OilMapping\/ZZ_BJ.php.html#19\">App\\Models\\Data\\OilMapping\\ZZ_BJ::getOilStationData<\/a>"],[0,6,"<a href=\"OilMapping\/ZZ_TJ.php.html#19\">App\\Models\\Data\\OilMapping\\ZZ_TJ::getOilStationData<\/a>"],[0,1,"<a href=\"OilMapping\/Zsh.php.html#9\">App\\Models\\Data\\OilMapping\\Zsh::getOilStationData<\/a>"],[0,1,"<a href=\"OilMapping\/Zsh.php.html#14\">App\\Models\\Data\\OilMapping\\Zsh::getPayedCouponList<\/a>"],[0,1,"<a href=\"OilMapping\/Zsh.php.html#28\">App\\Models\\Data\\OilMapping\\Zsh::getCouponList<\/a>"],[100,1,"<a href=\"OilPriceMapping\/Basic.php.html#14\">App\\Models\\Data\\OilPriceMapping\\Basic::getOilPriceData<\/a>"],[0,5,"<a href=\"OilPriceMapping\/Basic.php.html#25\">App\\Models\\Data\\OilPriceMapping\\Basic::replaceKey<\/a>"],[0,8,"<a href=\"OilPriceMapping\/Basic.php.html#51\">App\\Models\\Data\\OilPriceMapping\\Basic::filterField<\/a>"],[0,6,"<a href=\"OilPriceMapping\/Basic.php.html#84\">App\\Models\\Data\\OilPriceMapping\\Basic::dealOilStationDataNullValue<\/a>"],[0,14,"<a href=\"OilPriceMapping\/CZB.php.html#12\">App\\Models\\Data\\OilPriceMapping\\CZB::getOilPriceData<\/a>"],[0,6,"<a href=\"OilPriceMapping\/GS.php.html#12\">App\\Models\\Data\\OilPriceMapping\\GS::getOilPriceData<\/a>"],[0,1,"<a href=\"OrderAssoc.php.html#25\">App\\Models\\Data\\OrderAssoc::setId<\/a>"],[0,4,"<a href=\"OrderAssoc.php.html#47\">App\\Models\\Data\\OrderAssoc::insert<\/a>"],[0,3,"<a href=\"OrderAssoc.php.html#84\">App\\Models\\Data\\OrderAssoc::getMessage<\/a>"],[0,3,"<a href=\"OrderAssoc.php.html#92\">App\\Models\\Data\\OrderAssoc::updateSelfOrderStatus<\/a>"],[0,13,"<a href=\"OrderAssoc.php.html#113\">App\\Models\\Data\\OrderAssoc::updateOrderInfoByOrderId<\/a>"],[0,9,"<a href=\"OrderAssoc.php.html#165\">App\\Models\\Data\\OrderAssoc::updateOrderInfoById<\/a>"],[0,4,"<a href=\"OrderAssoc.php.html#201\">App\\Models\\Data\\OrderAssoc::getOrderInfoByPlatformOrderId<\/a>"],[0,7,"<a href=\"OrderAssoc.php.html#223\">App\\Models\\Data\\OrderAssoc::getOrderInfoBySelfOrderId<\/a>"],[0,17,"<a href=\"OrderAssoc.php.html#269\">App\\Models\\Data\\OrderAssoc::getOrderInfoByWhere<\/a>"],[0,16,"<a href=\"OrderAssoc.php.html#335\">App\\Models\\Data\\OrderAssoc::getOneOrderByWhereAndLock<\/a>"],[0,9,"<a href=\"OrderAssoc.php.html#394\">App\\Models\\Data\\OrderAssoc::insertBySearchForPlatform<\/a>"],[0,22,"<a href=\"OrderAssoc.php.html#456\">App\\Models\\Data\\OrderAssoc::getData<\/a>"],[0,10,"<a href=\"OrderAssoc.php.html#528\">App\\Models\\Data\\OrderAssoc::getChart<\/a>"],[0,2,"<a href=\"OrderAssoc.php.html#597\">App\\Models\\Data\\OrderAssoc::getRetryOrderById<\/a>"],[0,3,"<a href=\"OrderRefund.php.html#23\">App\\Models\\Data\\OrderRefund::insert<\/a>"],[0,3,"<a href=\"OrderRefund.php.html#49\">App\\Models\\Data\\OrderRefund::getMessage<\/a>"],[0,9,"<a href=\"OrderRefund.php.html#58\">App\\Models\\Data\\OrderRefund::getData<\/a>"],[0,2,"<a href=\"Permission.php.html#16\">App\\Models\\Data\\Permission::getData<\/a>"],[0,3,"<a href=\"Permission.php.html#36\">App\\Models\\Data\\Permission::getSelect<\/a>"],[0,5,"<a href=\"Permission.php.html#50\">App\\Models\\Data\\Permission::getTree<\/a>"],[0,1,"<a href=\"Permission.php.html#89\">App\\Models\\Data\\Permission::update<\/a>"],[0,1,"<a href=\"Permission.php.html#105\">App\\Models\\Data\\Permission::delete<\/a>"],[0,1,"<a href=\"Permission.php.html#111\">App\\Models\\Data\\Permission::create<\/a>"],[0,3,"<a href=\"RegionalInfo.php.html#24\">App\\Models\\Data\\RegionalInfo::getCityCodeByName<\/a>"],[0,5,"<a href=\"RegionalInfo.php.html#46\">App\\Models\\Data\\RegionalInfo::getCityCodeByNameFilterUnit<\/a>"],[0,1,"<a href=\"RegionalInfo.php.html#77\">App\\Models\\Data\\RegionalInfo::filterCode<\/a>"],[0,7,"<a href=\"RegionalInfo.php.html#94\">App\\Models\\Data\\RegionalInfo::getNameByCode<\/a>"],[0,2,"<a href=\"RegionalInfo.php.html#133\">App\\Models\\Data\\RegionalInfo::getParentCodeByChildren<\/a>"],[0,3,"<a href=\"Role.php.html#13\">App\\Models\\Data\\Role::getData<\/a>"],[0,3,"<a href=\"Role.php.html#34\">App\\Models\\Data\\Role::getSelect<\/a>"],[0,1,"<a href=\"Role.php.html#48\">App\\Models\\Data\\Role::update<\/a>"],[0,1,"<a href=\"Role.php.html#64\">App\\Models\\Data\\Role::delete<\/a>"],[0,1,"<a href=\"Role.php.html#70\">App\\Models\\Data\\Role::create<\/a>"],[0,2,"<a href=\"Role.php.html#78\">App\\Models\\Data\\Role::getValidateData<\/a>"],[0,2,"<a href=\"RolePermission.php.html#11\">App\\Models\\Data\\RolePermission::getData<\/a>"],[0,5,"<a href=\"RolePermission.php.html#48\">App\\Models\\Data\\RolePermission::update<\/a>"],[0,1,"<a href=\"RolePermission.php.html#100\">App\\Models\\Data\\RolePermission::delete<\/a>"],[0,3,"<a href=\"RolePermission.php.html#112\">App\\Models\\Data\\RolePermission::create<\/a>"],[0,10,"<a href=\"StationPrice.php.html#23\">App\\Models\\Data\\StationPrice::create<\/a>"],[0,12,"<a href=\"StationPrice.php.html#148\">App\\Models\\Data\\StationPrice::getDataByWhere<\/a>"],[0,14,"<a href=\"StationPrice.php.html#181\">App\\Models\\Data\\StationPrice::updateByWhere<\/a>"],[0,10,"<a href=\"StationPrice.php.html#213\">App\\Models\\Data\\StationPrice::getData<\/a>"],[0,17,"<a href=\"StationPushCondition.php.html#22\">App\\Models\\Data\\StationPushCondition::getData<\/a>"],[0,1,"<a href=\"StationPushCondition.php.html#91\">App\\Models\\Data\\StationPushCondition::getPushRuleByNameAbbreviation<\/a>"],[0,1,"<a href=\"StationPushCondition.php.html#101\">App\\Models\\Data\\StationPushCondition::update<\/a>"],[0,1,"<a href=\"StationPushCondition.php.html#117\">App\\Models\\Data\\StationPushCondition::delete<\/a>"],[0,1,"<a href=\"StationPushCondition.php.html#122\">App\\Models\\Data\\StationPushCondition::create<\/a>"],[0,1,"<a href=\"StationPushCondition.php.html#130\">App\\Models\\Data\\StationPushCondition::receiveStationPushRule<\/a>"],[0,2,"<a href=\"StationPushRecord.php.html#12\">App\\Models\\Data\\StationPushRecord::handle<\/a>"],[0,11,"<a href=\"StationPushRecord.php.html#39\">App\\Models\\Data\\StationPushRecord::getData<\/a>"],[0,6,"<a href=\"StationPushSwitch.php.html#34\">App\\Models\\Data\\StationPushSwitch::getData<\/a>"],[0,2,"<a href=\"StationPushSwitch.php.html#70\">App\\Models\\Data\\StationPushSwitch::getOpenAll<\/a>"],[0,1,"<a href=\"StationPushSwitch.php.html#95\">App\\Models\\Data\\StationPushSwitch::update<\/a>"],[0,1,"<a href=\"StationPushSwitch.php.html#112\">App\\Models\\Data\\StationPushSwitch::create<\/a>"],[0,13,"<a href=\"StationPushSwitch.php.html#130\">App\\Models\\Data\\StationPushSwitch::getPushedStationList<\/a>"],[0,2,"<a href=\"StationPushSwitch.php.html#200\">App\\Models\\Data\\StationPushSwitch::getAll<\/a>"],[0,1,"<a href=\"StationUniqueMapping.php.html#24\">App\\Models\\Data\\StationUniqueMapping::create<\/a>"],[0,3,"<a href=\"StationUniqueMapping.php.html#55\">App\\Models\\Data\\StationUniqueMapping::getDataByStationId<\/a>"],[0,13,"<a href=\"StationUniqueMapping.php.html#76\">App\\Models\\Data\\StationUniqueMapping::getStationPriceByStationId<\/a>"],[0,12,"<a href=\"StationUniqueMapping.php.html#126\">App\\Models\\Data\\StationUniqueMapping::getStationDataByStationIdAndCondition<\/a>"],[0,14,"<a href=\"StationUniqueMapping.php.html#181\">App\\Models\\Data\\StationUniqueMapping::getStationPriceByExternalStationId<\/a>"],[0,15,"<a href=\"StationUniqueMapping.php.html#248\">App\\Models\\Data\\StationUniqueMapping::getStationUniqueMappingByWhere<\/a>"],[0,6,"<a href=\"SupplierInfo.php.html#24\">App\\Models\\Data\\SupplierInfo::getSupplierNameBySupplierCode<\/a>"],[0,1,"<a href=\"SupplierInfo.php.html#45\">App\\Models\\Data\\SupplierInfo::getAllData<\/a>"],[0,3,"<a href=\"SupplierInfo.php.html#50\">App\\Models\\Data\\SupplierInfo::getData<\/a>"],[0,1,"<a href=\"SupplierInfo.php.html#78\">App\\Models\\Data\\SupplierInfo::update<\/a>"],[0,1,"<a href=\"SupplierInfo.php.html#95\">App\\Models\\Data\\SupplierInfo::delete<\/a>"],[0,1,"<a href=\"SupplierInfo.php.html#101\">App\\Models\\Data\\SupplierInfo::create<\/a>"],[0,1,"<a href=\"Trade\/Oil.php.html#24\">App\\Models\\Data\\Trade\\Oil::handle<\/a>"],[0,1,"<a href=\"Trade\/Oil.php.html#31\">App\\Models\\Data\\Trade\\Oil::dmd<\/a>"],[0,1,"<a href=\"Trade\/Order.php.html#86\">App\\Models\\Data\\Trade\\Order::handle<\/a>"],[0,1,"<a href=\"Trade\/Order.php.html#93\">App\\Models\\Data\\Trade\\Order::sg<\/a>"],[0,1,"<a href=\"Trade\/Order.php.html#112\">App\\Models\\Data\\Trade\\Order::default<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
