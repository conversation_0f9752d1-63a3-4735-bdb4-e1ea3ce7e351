<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Console</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Console</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Commands/CheckFyOrderUseTime.php.html#17">App\Console\Commands\CheckFyOrderUseTime</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZhyk.php.html#15">App\Console\Commands\PullOilForZhyk</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSh.php.html#14">App\Console\Commands\PullOilForSh</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForShSx.php.html#14">App\Console\Commands\PullOilForShSx</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSqZsh.php.html#15">App\Console\Commands\PullOilForSqZsh</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSqzl.php.html#15">App\Console\Commands\PullOilForSqzl</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForTBJX.php.html#14">App\Console\Commands\PullOilForTBJX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForWjy.php.html#16">App\Console\Commands\PullOilForWjy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForXYN.php.html#16">App\Console\Commands\PullOilForXYN</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForYunDaTong.php.html#15">App\Console\Commands\PullOilForYunDaTong</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZHUOYIQ.php.html#15">App\Console\Commands\PullOilForZHUOYIQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZHUOYIY.php.html#15">App\Console\Commands\PullOilForZHUOYIY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZdc.php.html#15">App\Console\Commands\PullOilForZdc</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZwl.php.html#14">App\Console\Commands\PullOilForZwl</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSaic.php.html#18">App\Console\Commands\PullOilForSaic</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZy.php.html#16">App\Console\Commands\PullOilForZy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullPrice.php.html#14">App\Console\Commands\PullPrice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullPriceForCzb.php.html#12">App\Console\Commands\PullPriceForCzb</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullPriceForGS.php.html#13">App\Console\Commands\PullPriceForGS</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForCnpc.php.html#23">App\Console\Commands\PullVerificationResultForCnpc</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForHbkj.php.html#24">App\Console\Commands\PullVerificationResultForHbkj</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForHsy.php.html#24">App\Console\Commands\PullVerificationResultForHsy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PushAccountBalanceToAD.php.html#19">App\Console\Commands\PushAccountBalanceToAD</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PushBillToSFFY.php.html#24">App\Console\Commands\PushBillToSFFY</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/QueueMonitoringAlarm.php.html#17">App\Console\Commands\QueueMonitoringAlarm</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ReleaseFrozenQuotaForDt.php.html#17">App\Console\Commands\ReleaseFrozenQuotaForDt</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/RunSpecialCode.php.html#10">App\Console\Commands\RunSpecialCode</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSg.php.html#15">App\Console\Commands\PullOilForSg</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForQK.php.html#16">App\Console\Commands\PullOilForQK</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CheckPushOrderStatus.php.html#20">App\Console\Commands\CheckPushOrderStatus</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForCy.php.html#14">App\Console\Commands\PullOilForCy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CheckReservationOrderStatus.php.html#22">App\Console\Commands\CheckReservationOrderStatus</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CreateLogTable.php.html#20">App\Console\Commands\CreateLogTable</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CreateStationPushRecordTable.php.html#19">App\Console\Commands\CreateStationPushRecordTable</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/DeleteLogTable.php.html#21">App\Console\Commands\DeleteLogTable</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBill.php.html#14">App\Console\Commands\PullBill</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForGdqp.php.html#23">App\Console\Commands\PullBillForGdqp</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForGs.php.html#24">App\Console\Commands\PullBillForGs</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForKl.php.html#24">App\Console\Commands\PullBillForKl</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForSc.php.html#24">App\Console\Commands\PullBillForSc</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForYc.php.html#23">App\Console\Commands\PullBillForYc</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOil.php.html#20">App\Console\Commands\PullOil</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForBdt.php.html#14">App\Console\Commands\PullOilForBdt</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForCyLng.php.html#14">App\Console\Commands\PullOilForCyLng</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForMtlSy.php.html#15">App\Console\Commands\PullOilForMtlSy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForCzb.php.html#13">App\Console\Commands\PullOilForCzb</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForDt.php.html#16">App\Console\Commands\PullOilForDt</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForEzt.php.html#20">App\Console\Commands\PullOilForEzt</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForGB.php.html#15">App\Console\Commands\PullOilForGB</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForGaoDeng.php.html#15">App\Console\Commands\PullOilForGaoDeng</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForHsy.php.html#15">App\Console\Commands\PullOilForHsy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForHyt.php.html#14">App\Console\Commands\PullOilForHyt</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForJH.php.html#16">App\Console\Commands\PullOilForJH</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForJQ.php.html#16">App\Console\Commands\PullOilForJQ</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForJT.php.html#16">App\Console\Commands\PullOilForJT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForJTX.php.html#16">App\Console\Commands\PullOilForJTX</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForMj.php.html#16">App\Console\Commands\PullOilForMj</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Kernel.php.html#60">App\Console\Kernel</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Commands/PullOilForSaic.php.html#18">App\Console\Commands\PullOilForSaic</a></td><td class="text-right">1406</td></tr>
       <tr><td><a href="Commands/PullOilForZy.php.html#16">App\Console\Commands\PullOilForZy</a></td><td class="text-right">1406</td></tr>
       <tr><td><a href="Commands/PullOilForSg.php.html#15">App\Console\Commands\PullOilForSg</a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="Commands/PullOilForJT.php.html#16">App\Console\Commands\PullOilForJT</a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="Commands/PullOilForJTX.php.html#16">App\Console\Commands\PullOilForJTX</a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="Commands/PullOilForQK.php.html#16">App\Console\Commands\PullOilForQK</a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="Commands/PullOilForJH.php.html#16">App\Console\Commands\PullOilForJH</a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="Commands/PullOilForYunDaTong.php.html#15">App\Console\Commands\PullOilForYunDaTong</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="Commands/PullOilForZhyk.php.html#15">App\Console\Commands\PullOilForZhyk</a></td><td class="text-right">930</td></tr>
       <tr><td><a href="Commands/PullOilForJQ.php.html#16">App\Console\Commands\PullOilForJQ</a></td><td class="text-right">870</td></tr>
       <tr><td><a href="Commands/PullOilForGB.php.html#15">App\Console\Commands\PullOilForGB</a></td><td class="text-right">870</td></tr>
       <tr><td><a href="Commands/PullOilForXYN.php.html#16">App\Console\Commands\PullOilForXYN</a></td><td class="text-right">812</td></tr>
       <tr><td><a href="Commands/PullOilForMj.php.html#16">App\Console\Commands\PullOilForMj</a></td><td class="text-right">812</td></tr>
       <tr><td><a href="Commands/PullOilForSqZsh.php.html#15">App\Console\Commands\PullOilForSqZsh</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="Commands/PullOilForZdc.php.html#15">App\Console\Commands\PullOilForZdc</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="Commands/PullOilForZHUOYIQ.php.html#15">App\Console\Commands\PullOilForZHUOYIQ</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Commands/PullOilForZHUOYIY.php.html#15">App\Console\Commands\PullOilForZHUOYIY</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Commands/PullOilForGaoDeng.php.html#15">App\Console\Commands\PullOilForGaoDeng</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Commands/PullOilForBdt.php.html#14">App\Console\Commands\PullOilForBdt</a></td><td class="text-right">552</td></tr>
       <tr><td><a href="Commands/PullOil.php.html#20">App\Console\Commands\PullOil</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="Commands/PullOilForMtlSy.php.html#15">App\Console\Commands\PullOilForMtlSy</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="Commands/PullOilForHyt.php.html#14">App\Console\Commands\PullOilForHyt</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="Commands/PullOilForSh.php.html#14">App\Console\Commands\PullOilForSh</a></td><td class="text-right">380</td></tr>
       <tr><td><a href="Commands/PullOilForWjy.php.html#16">App\Console\Commands\PullOilForWjy</a></td><td class="text-right">380</td></tr>
       <tr><td><a href="Commands/PullOilForHsy.php.html#15">App\Console\Commands\PullOilForHsy</a></td><td class="text-right">380</td></tr>
       <tr><td><a href="Commands/PullOilForShSx.php.html#14">App\Console\Commands\PullOilForShSx</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Commands/PullOilForCzb.php.html#13">App\Console\Commands\PullOilForCzb</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Commands/PullBillForSc.php.html#24">App\Console\Commands\PullBillForSc</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="Commands/PullOilForDt.php.html#16">App\Console\Commands\PullOilForDt</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="Commands/PullOilForCyLng.php.html#14">App\Console\Commands\PullOilForCyLng</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="Commands/PullOilForCy.php.html#14">App\Console\Commands\PullOilForCy</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="Commands/PullOilForEzt.php.html#20">App\Console\Commands\PullOilForEzt</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Commands/PullBillForGdqp.php.html#23">App\Console\Commands\PullBillForGdqp</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Commands/PullBillForGs.php.html#24">App\Console\Commands\PullBillForGs</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Commands/PullOilForTBJX.php.html#14">App\Console\Commands\PullOilForTBJX</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Commands/PullOilForSqzl.php.html#15">App\Console\Commands\PullOilForSqzl</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Commands/PullBillForKl.php.html#24">App\Console\Commands\PullBillForKl</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Commands/PullBillForYc.php.html#23">App\Console\Commands\PullBillForYc</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Commands/PullOilForZwl.php.html#14">App\Console\Commands\PullOilForZwl</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Commands/PushBillToSFFY.php.html#24">App\Console\Commands\PushBillToSFFY</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForHbkj.php.html#24">App\Console\Commands\PullVerificationResultForHbkj</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Commands/CheckReservationOrderStatus.php.html#22">App\Console\Commands\CheckReservationOrderStatus</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForCnpc.php.html#23">App\Console\Commands\PullVerificationResultForCnpc</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForHsy.php.html#24">App\Console\Commands\PullVerificationResultForHsy</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Commands/RunSpecialCode.php.html#10">App\Console\Commands\RunSpecialCode</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Commands/CheckPushOrderStatus.php.html#20">App\Console\Commands\CheckPushOrderStatus</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Commands/DeleteLogTable.php.html#21">App\Console\Commands\DeleteLogTable</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Commands/CreateLogTable.php.html#20">App\Console\Commands\CreateLogTable</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Commands/PullPriceForGS.php.html#13">App\Console\Commands\PullPriceForGS</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Commands/CreateStationPushRecordTable.php.html#19">App\Console\Commands\CreateStationPushRecordTable</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Commands/ReleaseFrozenQuotaForDt.php.html#17">App\Console\Commands\ReleaseFrozenQuotaForDt</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Commands/CheckFyOrderUseTime.php.html#17">App\Console\Commands\CheckFyOrderUseTime</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Commands/PullPriceForCzb.php.html#12">App\Console\Commands\PullPriceForCzb</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Commands/QueueMonitoringAlarm.php.html#17">App\Console\Commands\QueueMonitoringAlarm</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Commands/CheckFyOrderUseTime.php.html#37"><abbr title="App\Console\Commands\CheckFyOrderUseTime::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForTBJX.php.html#37"><abbr title="App\Console\Commands\PullOilForTBJX::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZHUOYIY.php.html#48"><abbr title="App\Console\Commands\PullOilForZHUOYIY::getStatusMapping">getStatusMapping</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZHUOYIY.php.html#40"><abbr title="App\Console\Commands\PullOilForZHUOYIY::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZHUOYIQ.php.html#59"><abbr title="App\Console\Commands\PullOilForZHUOYIQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZHUOYIQ.php.html#48"><abbr title="App\Console\Commands\PullOilForZHUOYIQ::getStatusMapping">getStatusMapping</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZHUOYIQ.php.html#40"><abbr title="App\Console\Commands\PullOilForZHUOYIQ::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForYunDaTong.php.html#52"><abbr title="App\Console\Commands\PullOilForYunDaTong::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForYunDaTong.php.html#38"><abbr title="App\Console\Commands\PullOilForYunDaTong::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForXYN.php.html#53"><abbr title="App\Console\Commands\PullOilForXYN::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForXYN.php.html#39"><abbr title="App\Console\Commands\PullOilForXYN::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForWjy.php.html#54"><abbr title="App\Console\Commands\PullOilForWjy::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForWjy.php.html#39"><abbr title="App\Console\Commands\PullOilForWjy::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForTBJX.php.html#51"><abbr title="App\Console\Commands\PullOilForTBJX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSqzl.php.html#52"><abbr title="App\Console\Commands\PullOilForSqzl::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZdc.php.html#38"><abbr title="App\Console\Commands\PullOilForZdc::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSqzl.php.html#38"><abbr title="App\Console\Commands\PullOilForSqzl::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSqZsh.php.html#55"><abbr title="App\Console\Commands\PullOilForSqZsh::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSqZsh.php.html#40"><abbr title="App\Console\Commands\PullOilForSqZsh::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForShSx.php.html#51"><abbr title="App\Console\Commands\PullOilForShSx::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForShSx.php.html#37"><abbr title="App\Console\Commands\PullOilForShSx::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSh.php.html#51"><abbr title="App\Console\Commands\PullOilForSh::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSh.php.html#37"><abbr title="App\Console\Commands\PullOilForSh::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSg.php.html#54"><abbr title="App\Console\Commands\PullOilForSg::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSg.php.html#42"><abbr title="App\Console\Commands\PullOilForSg::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSaic.php.html#53"><abbr title="App\Console\Commands\PullOilForSaic::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForSaic.php.html#42"><abbr title="App\Console\Commands\PullOilForSaic::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForQK.php.html#55"><abbr title="App\Console\Commands\PullOilForQK::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForQK.php.html#41"><abbr title="App\Console\Commands\PullOilForQK::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZHUOYIY.php.html#59"><abbr title="App\Console\Commands\PullOilForZHUOYIY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZdc.php.html#49"><abbr title="App\Console\Commands\PullOilForZdc::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForMtlSy.php.html#40"><abbr title="App\Console\Commands\PullOilForMtlSy::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForHbkj.php.html#56"><abbr title="App\Console\Commands\PullVerificationResultForHbkj::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/RunSpecialCode.php.html#41"><abbr title="App\Console\Commands\RunSpecialCode::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/RunSpecialCode.php.html#30"><abbr title="App\Console\Commands\RunSpecialCode::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ReleaseFrozenQuotaForDt.php.html#49"><abbr title="App\Console\Commands\ReleaseFrozenQuotaForDt::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/ReleaseFrozenQuotaForDt.php.html#38"><abbr title="App\Console\Commands\ReleaseFrozenQuotaForDt::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/QueueMonitoringAlarm.php.html#49"><abbr title="App\Console\Commands\QueueMonitoringAlarm::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/QueueMonitoringAlarm.php.html#38"><abbr title="App\Console\Commands\QueueMonitoringAlarm::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PushBillToSFFY.php.html#61"><abbr title="App\Console\Commands\PushBillToSFFY::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PushBillToSFFY.php.html#50"><abbr title="App\Console\Commands\PushBillToSFFY::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PushAccountBalanceToAD.php.html#50"><abbr title="App\Console\Commands\PushAccountBalanceToAD::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PushAccountBalanceToAD.php.html#39"><abbr title="App\Console\Commands\PushAccountBalanceToAD::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForHsy.php.html#56"><abbr title="App\Console\Commands\PullVerificationResultForHsy::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForHsy.php.html#44"><abbr title="App\Console\Commands\PullVerificationResultForHsy::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForHbkj.php.html#44"><abbr title="App\Console\Commands\PullVerificationResultForHbkj::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZhyk.php.html#38"><abbr title="App\Console\Commands\PullOilForZhyk::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForCnpc.php.html#55"><abbr title="App\Console\Commands\PullVerificationResultForCnpc::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForCnpc.php.html#43"><abbr title="App\Console\Commands\PullVerificationResultForCnpc::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullPriceForGS.php.html#48"><abbr title="App\Console\Commands\PullPriceForGS::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullPriceForGS.php.html#36"><abbr title="App\Console\Commands\PullPriceForGS::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullPriceForCzb.php.html#47"><abbr title="App\Console\Commands\PullPriceForCzb::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullPriceForCzb.php.html#35"><abbr title="App\Console\Commands\PullPriceForCzb::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullPrice.php.html#50"><abbr title="App\Console\Commands\PullPrice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullPrice.php.html#40"><abbr title="App\Console\Commands\PullPrice::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZy.php.html#59"><abbr title="App\Console\Commands\PullOilForZy::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZy.php.html#42"><abbr title="App\Console\Commands\PullOilForZy::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZwl.php.html#51"><abbr title="App\Console\Commands\PullOilForZwl::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZwl.php.html#37"><abbr title="App\Console\Commands\PullOilForZwl::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForZhyk.php.html#52"><abbr title="App\Console\Commands\PullOilForZhyk::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForMtlSy.php.html#56"><abbr title="App\Console\Commands\PullOilForMtlSy::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForMj.php.html#63"><abbr title="App\Console\Commands\PullOilForMj::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CheckFyOrderUseTime.php.html#48"><abbr title="App\Console\Commands\CheckFyOrderUseTime::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBill.php.html#47"><abbr title="App\Console\Commands\PullBill::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOil.php.html#58"><abbr title="App\Console\Commands\PullOil::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOil.php.html#48"><abbr title="App\Console\Commands\PullOil::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForYc.php.html#58"><abbr title="App\Console\Commands\PullBillForYc::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForYc.php.html#46"><abbr title="App\Console\Commands\PullBillForYc::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForSc.php.html#59"><abbr title="App\Console\Commands\PullBillForSc::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForSc.php.html#47"><abbr title="App\Console\Commands\PullBillForSc::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForKl.php.html#59"><abbr title="App\Console\Commands\PullBillForKl::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForKl.php.html#47"><abbr title="App\Console\Commands\PullBillForKl::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForGs.php.html#63"><abbr title="App\Console\Commands\PullBillForGs::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForGs.php.html#51"><abbr title="App\Console\Commands\PullBillForGs::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForGdqp.php.html#58"><abbr title="App\Console\Commands\PullBillForGdqp::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBillForGdqp.php.html#46"><abbr title="App\Console\Commands\PullBillForGdqp::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullBill.php.html#37"><abbr title="App\Console\Commands\PullBill::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOil.php.html#79"><abbr title="App\Console\Commands\PullOil::dealNotExistsStation">dealNotExistsStation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/DeleteLogTable.php.html#90"><abbr title="App\Console\Commands\DeleteLogTable::deleteTable">deleteTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/DeleteLogTable.php.html#61"><abbr title="App\Console\Commands\DeleteLogTable::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/DeleteLogTable.php.html#50"><abbr title="App\Console\Commands\DeleteLogTable::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CreateStationPushRecordTable.php.html#71"><abbr title="App\Console\Commands\CreateStationPushRecordTable::createTable">createTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CreateStationPushRecordTable.php.html#50"><abbr title="App\Console\Commands\CreateStationPushRecordTable::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CreateStationPushRecordTable.php.html#39"><abbr title="App\Console\Commands\CreateStationPushRecordTable::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CreateLogTable.php.html#87"><abbr title="App\Console\Commands\CreateLogTable::createTable">createTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CreateLogTable.php.html#59"><abbr title="App\Console\Commands\CreateLogTable::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CreateLogTable.php.html#48"><abbr title="App\Console\Commands\CreateLogTable::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CheckReservationOrderStatus.php.html#54"><abbr title="App\Console\Commands\CheckReservationOrderStatus::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CheckReservationOrderStatus.php.html#43"><abbr title="App\Console\Commands\CheckReservationOrderStatus::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CheckPushOrderStatus.php.html#51"><abbr title="App\Console\Commands\CheckPushOrderStatus::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/CheckPushOrderStatus.php.html#40"><abbr title="App\Console\Commands\CheckPushOrderStatus::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOil.php.html#63"><abbr title="App\Console\Commands\PullOil::getExistsStation">getExistsStation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOil.php.html#153"><abbr title="App\Console\Commands\PullOil::stopStation">stopStation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForMj.php.html#42"><abbr title="App\Console\Commands\PullOilForMj::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForGaoDeng.php.html#38"><abbr title="App\Console\Commands\PullOilForGaoDeng::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForJTX.php.html#55"><abbr title="App\Console\Commands\PullOilForJTX::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForJTX.php.html#41"><abbr title="App\Console\Commands\PullOilForJTX::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForJT.php.html#58"><abbr title="App\Console\Commands\PullOilForJT::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForJT.php.html#44"><abbr title="App\Console\Commands\PullOilForJT::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForJQ.php.html#50"><abbr title="App\Console\Commands\PullOilForJQ::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForJQ.php.html#39"><abbr title="App\Console\Commands\PullOilForJQ::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForJH.php.html#55"><abbr title="App\Console\Commands\PullOilForJH::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForJH.php.html#41"><abbr title="App\Console\Commands\PullOilForJH::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForHyt.php.html#57"><abbr title="App\Console\Commands\PullOilForHyt::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForHyt.php.html#42"><abbr title="App\Console\Commands\PullOilForHyt::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForHsy.php.html#52"><abbr title="App\Console\Commands\PullOilForHsy::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForHsy.php.html#38"><abbr title="App\Console\Commands\PullOilForHsy::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForGaoDeng.php.html#52"><abbr title="App\Console\Commands\PullOilForGaoDeng::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForGB.php.html#50"><abbr title="App\Console\Commands\PullOilForGB::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForBdt.php.html#37"><abbr title="App\Console\Commands\PullOilForBdt::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForGB.php.html#39"><abbr title="App\Console\Commands\PullOilForGB::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForEzt.php.html#55"><abbr title="App\Console\Commands\PullOilForEzt::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForEzt.php.html#43"><abbr title="App\Console\Commands\PullOilForEzt::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForDt.php.html#55"><abbr title="App\Console\Commands\PullOilForDt::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForDt.php.html#43"><abbr title="App\Console\Commands\PullOilForDt::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForCzb.php.html#55"><abbr title="App\Console\Commands\PullOilForCzb::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForCzb.php.html#44"><abbr title="App\Console\Commands\PullOilForCzb::getNameAbbreviations">getNameAbbreviations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForCzb.php.html#38"><abbr title="App\Console\Commands\PullOilForCzb::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForCyLng.php.html#49"><abbr title="App\Console\Commands\PullOilForCyLng::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForCyLng.php.html#37"><abbr title="App\Console\Commands\PullOilForCyLng::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForCy.php.html#49"><abbr title="App\Console\Commands\PullOilForCy::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForCy.php.html#37"><abbr title="App\Console\Commands\PullOilForCy::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Commands/PullOilForBdt.php.html#49"><abbr title="App\Console\Commands\PullOilForBdt::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Kernel.php.html#127"><abbr title="App\Console\Kernel::schedule">schedule</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Commands/PullOilForSaic.php.html#53"><abbr title="App\Console\Commands\PullOilForSaic::handle">handle</abbr></a></td><td class="text-right">1332</td></tr>
       <tr><td><a href="Commands/PullOilForZy.php.html#59"><abbr title="App\Console\Commands\PullOilForZy::handle">handle</abbr></a></td><td class="text-right">1332</td></tr>
       <tr><td><a href="Commands/PullOilForJTX.php.html#55"><abbr title="App\Console\Commands\PullOilForJTX::handle">handle</abbr></a></td><td class="text-right">1056</td></tr>
       <tr><td><a href="Commands/PullOilForSg.php.html#54"><abbr title="App\Console\Commands\PullOilForSg::handle">handle</abbr></a></td><td class="text-right">1056</td></tr>
       <tr><td><a href="Commands/PullOilForQK.php.html#55"><abbr title="App\Console\Commands\PullOilForQK::handle">handle</abbr></a></td><td class="text-right">1056</td></tr>
       <tr><td><a href="Commands/PullOilForJT.php.html#58"><abbr title="App\Console\Commands\PullOilForJT::handle">handle</abbr></a></td><td class="text-right">1056</td></tr>
       <tr><td><a href="Commands/PullOilForJH.php.html#55"><abbr title="App\Console\Commands\PullOilForJH::handle">handle</abbr></a></td><td class="text-right">1056</td></tr>
       <tr><td><a href="Commands/PullOilForYunDaTong.php.html#52"><abbr title="App\Console\Commands\PullOilForYunDaTong::handle">handle</abbr></a></td><td class="text-right">930</td></tr>
       <tr><td><a href="Commands/PullOilForZhyk.php.html#52"><abbr title="App\Console\Commands\PullOilForZhyk::handle">handle</abbr></a></td><td class="text-right">870</td></tr>
       <tr><td><a href="Commands/PullOilForGB.php.html#50"><abbr title="App\Console\Commands\PullOilForGB::handle">handle</abbr></a></td><td class="text-right">812</td></tr>
       <tr><td><a href="Commands/PullOilForJQ.php.html#50"><abbr title="App\Console\Commands\PullOilForJQ::handle">handle</abbr></a></td><td class="text-right">812</td></tr>
       <tr><td><a href="Commands/PullOilForXYN.php.html#53"><abbr title="App\Console\Commands\PullOilForXYN::handle">handle</abbr></a></td><td class="text-right">756</td></tr>
       <tr><td><a href="Commands/PullOilForMj.php.html#63"><abbr title="App\Console\Commands\PullOilForMj::handle">handle</abbr></a></td><td class="text-right">756</td></tr>
       <tr><td><a href="Commands/PullOilForSqZsh.php.html#55"><abbr title="App\Console\Commands\PullOilForSqZsh::handle">handle</abbr></a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Commands/PullOilForZdc.php.html#49"><abbr title="App\Console\Commands\PullOilForZdc::handle">handle</abbr></a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Commands/PullOilForGaoDeng.php.html#52"><abbr title="App\Console\Commands\PullOilForGaoDeng::handle">handle</abbr></a></td><td class="text-right">600</td></tr>
       <tr><td><a href="Commands/PullOilForZHUOYIY.php.html#59"><abbr title="App\Console\Commands\PullOilForZHUOYIY::handle">handle</abbr></a></td><td class="text-right">552</td></tr>
       <tr><td><a href="Commands/PullOilForZHUOYIQ.php.html#59"><abbr title="App\Console\Commands\PullOilForZHUOYIQ::handle">handle</abbr></a></td><td class="text-right">552</td></tr>
       <tr><td><a href="Commands/PullOilForBdt.php.html#49"><abbr title="App\Console\Commands\PullOilForBdt::handle">handle</abbr></a></td><td class="text-right">506</td></tr>
       <tr><td><a href="Commands/PullOilForHyt.php.html#57"><abbr title="App\Console\Commands\PullOilForHyt::handle">handle</abbr></a></td><td class="text-right">380</td></tr>
       <tr><td><a href="Commands/PullOilForMtlSy.php.html#56"><abbr title="App\Console\Commands\PullOilForMtlSy::handle">handle</abbr></a></td><td class="text-right">380</td></tr>
       <tr><td><a href="Commands/PullOilForSh.php.html#51"><abbr title="App\Console\Commands\PullOilForSh::handle">handle</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Commands/PullOilForHsy.php.html#52"><abbr title="App\Console\Commands\PullOilForHsy::handle">handle</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Commands/PullOilForWjy.php.html#54"><abbr title="App\Console\Commands\PullOilForWjy::handle">handle</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Commands/PullOilForShSx.php.html#51"><abbr title="App\Console\Commands\PullOilForShSx::handle">handle</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="Commands/PullOilForDt.php.html#55"><abbr title="App\Console\Commands\PullOilForDt::handle">handle</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="Commands/PullOilForCzb.php.html#55"><abbr title="App\Console\Commands\PullOilForCzb::handle">handle</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="Commands/PullBillForSc.php.html#59"><abbr title="App\Console\Commands\PullBillForSc::handle">handle</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="Commands/PullOilForCy.php.html#49"><abbr title="App\Console\Commands\PullOilForCy::handle">handle</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Commands/PullOilForCyLng.php.html#49"><abbr title="App\Console\Commands\PullOilForCyLng::handle">handle</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Commands/PullOilForTBJX.php.html#51"><abbr title="App\Console\Commands\PullOilForTBJX::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Commands/PullOilForSqzl.php.html#52"><abbr title="App\Console\Commands\PullOilForSqzl::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Commands/PullOilForEzt.php.html#55"><abbr title="App\Console\Commands\PullOilForEzt::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Commands/PullBillForGdqp.php.html#58"><abbr title="App\Console\Commands\PullBillForGdqp::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Commands/PullBillForGs.php.html#63"><abbr title="App\Console\Commands\PullBillForGs::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Commands/PullBillForKl.php.html#59"><abbr title="App\Console\Commands\PullBillForKl::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Commands/PullOil.php.html#153"><abbr title="App\Console\Commands\PullOil::stopStation">stopStation</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Commands/PullBillForYc.php.html#58"><abbr title="App\Console\Commands\PullBillForYc::handle">handle</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Commands/PullOilForZwl.php.html#51"><abbr title="App\Console\Commands\PullOilForZwl::handle">handle</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Commands/PushBillToSFFY.php.html#61"><abbr title="App\Console\Commands\PushBillToSFFY::handle">handle</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForHbkj.php.html#56"><abbr title="App\Console\Commands\PullVerificationResultForHbkj::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Commands/CheckReservationOrderStatus.php.html#54"><abbr title="App\Console\Commands\CheckReservationOrderStatus::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForHsy.php.html#56"><abbr title="App\Console\Commands\PullVerificationResultForHsy::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Commands/PullVerificationResultForCnpc.php.html#55"><abbr title="App\Console\Commands\PullVerificationResultForCnpc::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Commands/RunSpecialCode.php.html#41"><abbr title="App\Console\Commands\RunSpecialCode::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Commands/CheckPushOrderStatus.php.html#51"><abbr title="App\Console\Commands\CheckPushOrderStatus::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Commands/PullOil.php.html#79"><abbr title="App\Console\Commands\PullOil::dealNotExistsStation">dealNotExistsStation</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Commands/PullPriceForGS.php.html#48"><abbr title="App\Console\Commands\PullPriceForGS::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Commands/DeleteLogTable.php.html#61"><abbr title="App\Console\Commands\DeleteLogTable::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Commands/ReleaseFrozenQuotaForDt.php.html#49"><abbr title="App\Console\Commands\ReleaseFrozenQuotaForDt::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Commands/CheckFyOrderUseTime.php.html#48"><abbr title="App\Console\Commands\CheckFyOrderUseTime::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Commands/DeleteLogTable.php.html#90"><abbr title="App\Console\Commands\DeleteLogTable::deleteTable">deleteTable</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Commands/PullPriceForCzb.php.html#47"><abbr title="App\Console\Commands\PullPriceForCzb::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Commands/CreateStationPushRecordTable.php.html#71"><abbr title="App\Console\Commands\CreateStationPushRecordTable::createTable">createTable</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Commands/CreateLogTable.php.html#87"><abbr title="App\Console\Commands\CreateLogTable::createTable">createTable</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Commands/QueueMonitoringAlarm.php.html#49"><abbr title="App\Console\Commands\QueueMonitoringAlarm::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Commands/CreateLogTable.php.html#59"><abbr title="App\Console\Commands\CreateLogTable::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:21:05 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../.js/d3.min.js" type="text/javascript"></script>
  <script src="../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([58,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([124,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,5,"<a href=\"Commands\/CheckFyOrderUseTime.php.html#17\">App\\Console\\Commands\\CheckFyOrderUseTime<\/a>"],[0,8,"<a href=\"Commands\/CheckPushOrderStatus.php.html#20\">App\\Console\\Commands\\CheckPushOrderStatus<\/a>"],[0,9,"<a href=\"Commands\/CheckReservationOrderStatus.php.html#22\">App\\Console\\Commands\\CheckReservationOrderStatus<\/a>"],[0,6,"<a href=\"Commands\/CreateLogTable.php.html#20\">App\\Console\\Commands\\CreateLogTable<\/a>"],[0,5,"<a href=\"Commands\/CreateStationPushRecordTable.php.html#19\">App\\Console\\Commands\\CreateStationPushRecordTable<\/a>"],[0,8,"<a href=\"Commands\/DeleteLogTable.php.html#21\">App\\Console\\Commands\\DeleteLogTable<\/a>"],[0,2,"<a href=\"Commands\/PullBill.php.html#14\">App\\Console\\Commands\\PullBill<\/a>"],[0,15,"<a href=\"Commands\/PullBillForGdqp.php.html#23\">App\\Console\\Commands\\PullBillForGdqp<\/a>"],[0,15,"<a href=\"Commands\/PullBillForGs.php.html#24\">App\\Console\\Commands\\PullBillForGs<\/a>"],[0,15,"<a href=\"Commands\/PullBillForKl.php.html#24\">App\\Console\\Commands\\PullBillForKl<\/a>"],[0,17,"<a href=\"Commands\/PullBillForSc.php.html#24\">App\\Console\\Commands\\PullBillForSc<\/a>"],[0,13,"<a href=\"Commands\/PullBillForYc.php.html#23\">App\\Console\\Commands\\PullBillForYc<\/a>"],[0,22,"<a href=\"Commands\/PullOil.php.html#20\">App\\Console\\Commands\\PullOil<\/a>"],[0,23,"<a href=\"Commands\/PullOilForBdt.php.html#14\">App\\Console\\Commands\\PullOilForBdt<\/a>"],[0,16,"<a href=\"Commands\/PullOilForCy.php.html#14\">App\\Console\\Commands\\PullOilForCy<\/a>"],[0,16,"<a href=\"Commands\/PullOilForCyLng.php.html#14\">App\\Console\\Commands\\PullOilForCyLng<\/a>"],[0,18,"<a href=\"Commands\/PullOilForCzb.php.html#13\">App\\Console\\Commands\\PullOilForCzb<\/a>"],[0,17,"<a href=\"Commands\/PullOilForDt.php.html#16\">App\\Console\\Commands\\PullOilForDt<\/a>"],[0,15,"<a href=\"Commands\/PullOilForEzt.php.html#20\">App\\Console\\Commands\\PullOilForEzt<\/a>"],[0,29,"<a href=\"Commands\/PullOilForGB.php.html#15\">App\\Console\\Commands\\PullOilForGB<\/a>"],[0,25,"<a href=\"Commands\/PullOilForGaoDeng.php.html#15\">App\\Console\\Commands\\PullOilForGaoDeng<\/a>"],[0,19,"<a href=\"Commands\/PullOilForHsy.php.html#15\">App\\Console\\Commands\\PullOilForHsy<\/a>"],[0,20,"<a href=\"Commands\/PullOilForHyt.php.html#14\">App\\Console\\Commands\\PullOilForHyt<\/a>"],[0,33,"<a href=\"Commands\/PullOilForJH.php.html#16\">App\\Console\\Commands\\PullOilForJH<\/a>"],[0,29,"<a href=\"Commands\/PullOilForJQ.php.html#16\">App\\Console\\Commands\\PullOilForJQ<\/a>"],[0,33,"<a href=\"Commands\/PullOilForJT.php.html#16\">App\\Console\\Commands\\PullOilForJT<\/a>"],[0,33,"<a href=\"Commands\/PullOilForJTX.php.html#16\">App\\Console\\Commands\\PullOilForJTX<\/a>"],[0,28,"<a href=\"Commands\/PullOilForMj.php.html#16\">App\\Console\\Commands\\PullOilForMj<\/a>"],[0,20,"<a href=\"Commands\/PullOilForMtlSy.php.html#15\">App\\Console\\Commands\\PullOilForMtlSy<\/a>"],[0,33,"<a href=\"Commands\/PullOilForQK.php.html#16\">App\\Console\\Commands\\PullOilForQK<\/a>"],[0,37,"<a href=\"Commands\/PullOilForSaic.php.html#18\">App\\Console\\Commands\\PullOilForSaic<\/a>"],[0,33,"<a href=\"Commands\/PullOilForSg.php.html#15\">App\\Console\\Commands\\PullOilForSg<\/a>"],[0,19,"<a href=\"Commands\/PullOilForSh.php.html#14\">App\\Console\\Commands\\PullOilForSh<\/a>"],[0,18,"<a href=\"Commands\/PullOilForShSx.php.html#14\">App\\Console\\Commands\\PullOilForShSx<\/a>"],[0,26,"<a href=\"Commands\/PullOilForSqZsh.php.html#15\">App\\Console\\Commands\\PullOilForSqZsh<\/a>"],[0,15,"<a href=\"Commands\/PullOilForSqzl.php.html#15\">App\\Console\\Commands\\PullOilForSqzl<\/a>"],[0,15,"<a href=\"Commands\/PullOilForTBJX.php.html#14\">App\\Console\\Commands\\PullOilForTBJX<\/a>"],[0,19,"<a href=\"Commands\/PullOilForWjy.php.html#16\">App\\Console\\Commands\\PullOilForWjy<\/a>"],[0,28,"<a href=\"Commands\/PullOilForXYN.php.html#16\">App\\Console\\Commands\\PullOilForXYN<\/a>"],[0,31,"<a href=\"Commands\/PullOilForYunDaTong.php.html#15\">App\\Console\\Commands\\PullOilForYunDaTong<\/a>"],[0,25,"<a href=\"Commands\/PullOilForZHUOYIQ.php.html#15\">App\\Console\\Commands\\PullOilForZHUOYIQ<\/a>"],[0,25,"<a href=\"Commands\/PullOilForZHUOYIY.php.html#15\">App\\Console\\Commands\\PullOilForZHUOYIY<\/a>"],[0,26,"<a href=\"Commands\/PullOilForZdc.php.html#15\">App\\Console\\Commands\\PullOilForZdc<\/a>"],[0,30,"<a href=\"Commands\/PullOilForZhyk.php.html#15\">App\\Console\\Commands\\PullOilForZhyk<\/a>"],[0,13,"<a href=\"Commands\/PullOilForZwl.php.html#14\">App\\Console\\Commands\\PullOilForZwl<\/a>"],[0,37,"<a href=\"Commands\/PullOilForZy.php.html#16\">App\\Console\\Commands\\PullOilForZy<\/a>"],[0,2,"<a href=\"Commands\/PullPrice.php.html#14\">App\\Console\\Commands\\PullPrice<\/a>"],[0,4,"<a href=\"Commands\/PullPriceForCzb.php.html#12\">App\\Console\\Commands\\PullPriceForCzb<\/a>"],[0,5,"<a href=\"Commands\/PullPriceForGS.php.html#13\">App\\Console\\Commands\\PullPriceForGS<\/a>"],[0,8,"<a href=\"Commands\/PullVerificationResultForCnpc.php.html#23\">App\\Console\\Commands\\PullVerificationResultForCnpc<\/a>"],[0,9,"<a href=\"Commands\/PullVerificationResultForHbkj.php.html#24\">App\\Console\\Commands\\PullVerificationResultForHbkj<\/a>"],[0,8,"<a href=\"Commands\/PullVerificationResultForHsy.php.html#24\">App\\Console\\Commands\\PullVerificationResultForHsy<\/a>"],[0,2,"<a href=\"Commands\/PushAccountBalanceToAD.php.html#19\">App\\Console\\Commands\\PushAccountBalanceToAD<\/a>"],[0,12,"<a href=\"Commands\/PushBillToSFFY.php.html#24\">App\\Console\\Commands\\PushBillToSFFY<\/a>"],[0,4,"<a href=\"Commands\/QueueMonitoringAlarm.php.html#17\">App\\Console\\Commands\\QueueMonitoringAlarm<\/a>"],[0,5,"<a href=\"Commands\/ReleaseFrozenQuotaForDt.php.html#17\">App\\Console\\Commands\\ReleaseFrozenQuotaForDt<\/a>"],[0,8,"<a href=\"Commands\/RunSpecialCode.php.html#10\">App\\Console\\Commands\\RunSpecialCode<\/a>"],[0,1,"<a href=\"Kernel.php.html#60\">App\\Console\\Kernel<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Commands\/CheckFyOrderUseTime.php.html#37\">App\\Console\\Commands\\CheckFyOrderUseTime::__construct<\/a>"],[0,4,"<a href=\"Commands\/CheckFyOrderUseTime.php.html#48\">App\\Console\\Commands\\CheckFyOrderUseTime::handle<\/a>"],[0,1,"<a href=\"Commands\/CheckPushOrderStatus.php.html#40\">App\\Console\\Commands\\CheckPushOrderStatus::__construct<\/a>"],[0,7,"<a href=\"Commands\/CheckPushOrderStatus.php.html#51\">App\\Console\\Commands\\CheckPushOrderStatus::handle<\/a>"],[0,1,"<a href=\"Commands\/CheckReservationOrderStatus.php.html#43\">App\\Console\\Commands\\CheckReservationOrderStatus::__construct<\/a>"],[0,8,"<a href=\"Commands\/CheckReservationOrderStatus.php.html#54\">App\\Console\\Commands\\CheckReservationOrderStatus::handle<\/a>"],[0,1,"<a href=\"Commands\/CreateLogTable.php.html#48\">App\\Console\\Commands\\CreateLogTable::__construct<\/a>"],[0,2,"<a href=\"Commands\/CreateLogTable.php.html#59\">App\\Console\\Commands\\CreateLogTable::handle<\/a>"],[0,3,"<a href=\"Commands\/CreateLogTable.php.html#87\">App\\Console\\Commands\\CreateLogTable::createTable<\/a>"],[0,1,"<a href=\"Commands\/CreateStationPushRecordTable.php.html#39\">App\\Console\\Commands\\CreateStationPushRecordTable::__construct<\/a>"],[0,1,"<a href=\"Commands\/CreateStationPushRecordTable.php.html#50\">App\\Console\\Commands\\CreateStationPushRecordTable::handle<\/a>"],[0,3,"<a href=\"Commands\/CreateStationPushRecordTable.php.html#71\">App\\Console\\Commands\\CreateStationPushRecordTable::createTable<\/a>"],[0,1,"<a href=\"Commands\/DeleteLogTable.php.html#50\">App\\Console\\Commands\\DeleteLogTable::__construct<\/a>"],[0,4,"<a href=\"Commands\/DeleteLogTable.php.html#61\">App\\Console\\Commands\\DeleteLogTable::handle<\/a>"],[0,3,"<a href=\"Commands\/DeleteLogTable.php.html#90\">App\\Console\\Commands\\DeleteLogTable::deleteTable<\/a>"],[0,1,"<a href=\"Commands\/PullBill.php.html#37\">App\\Console\\Commands\\PullBill::__construct<\/a>"],[0,1,"<a href=\"Commands\/PullBill.php.html#47\">App\\Console\\Commands\\PullBill::handle<\/a>"],[0,1,"<a href=\"Commands\/PullBillForGdqp.php.html#46\">App\\Console\\Commands\\PullBillForGdqp::__construct<\/a>"],[0,14,"<a href=\"Commands\/PullBillForGdqp.php.html#58\">App\\Console\\Commands\\PullBillForGdqp::handle<\/a>"],[0,1,"<a href=\"Commands\/PullBillForGs.php.html#51\">App\\Console\\Commands\\PullBillForGs::__construct<\/a>"],[0,14,"<a href=\"Commands\/PullBillForGs.php.html#63\">App\\Console\\Commands\\PullBillForGs::handle<\/a>"],[0,1,"<a href=\"Commands\/PullBillForKl.php.html#47\">App\\Console\\Commands\\PullBillForKl::__construct<\/a>"],[0,14,"<a href=\"Commands\/PullBillForKl.php.html#59\">App\\Console\\Commands\\PullBillForKl::handle<\/a>"],[0,1,"<a href=\"Commands\/PullBillForSc.php.html#47\">App\\Console\\Commands\\PullBillForSc::__construct<\/a>"],[0,16,"<a href=\"Commands\/PullBillForSc.php.html#59\">App\\Console\\Commands\\PullBillForSc::handle<\/a>"],[0,1,"<a href=\"Commands\/PullBillForYc.php.html#46\">App\\Console\\Commands\\PullBillForYc::__construct<\/a>"],[0,12,"<a href=\"Commands\/PullBillForYc.php.html#58\">App\\Console\\Commands\\PullBillForYc::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOil.php.html#48\">App\\Console\\Commands\\PullOil::__construct<\/a>"],[0,1,"<a href=\"Commands\/PullOil.php.html#58\">App\\Console\\Commands\\PullOil::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOil.php.html#63\">App\\Console\\Commands\\PullOil::getExistsStation<\/a>"],[0,6,"<a href=\"Commands\/PullOil.php.html#79\">App\\Console\\Commands\\PullOil::dealNotExistsStation<\/a>"],[0,13,"<a href=\"Commands\/PullOil.php.html#153\">App\\Console\\Commands\\PullOil::stopStation<\/a>"],[0,1,"<a href=\"Commands\/PullOilForBdt.php.html#37\">App\\Console\\Commands\\PullOilForBdt::__construct<\/a>"],[0,22,"<a href=\"Commands\/PullOilForBdt.php.html#49\">App\\Console\\Commands\\PullOilForBdt::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForCy.php.html#37\">App\\Console\\Commands\\PullOilForCy::__construct<\/a>"],[0,15,"<a href=\"Commands\/PullOilForCy.php.html#49\">App\\Console\\Commands\\PullOilForCy::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForCyLng.php.html#37\">App\\Console\\Commands\\PullOilForCyLng::__construct<\/a>"],[0,15,"<a href=\"Commands\/PullOilForCyLng.php.html#49\">App\\Console\\Commands\\PullOilForCyLng::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForCzb.php.html#38\">App\\Console\\Commands\\PullOilForCzb::__construct<\/a>"],[0,1,"<a href=\"Commands\/PullOilForCzb.php.html#44\">App\\Console\\Commands\\PullOilForCzb::getNameAbbreviations<\/a>"],[0,16,"<a href=\"Commands\/PullOilForCzb.php.html#55\">App\\Console\\Commands\\PullOilForCzb::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForDt.php.html#43\">App\\Console\\Commands\\PullOilForDt::__construct<\/a>"],[0,16,"<a href=\"Commands\/PullOilForDt.php.html#55\">App\\Console\\Commands\\PullOilForDt::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForEzt.php.html#43\">App\\Console\\Commands\\PullOilForEzt::__construct<\/a>"],[0,14,"<a href=\"Commands\/PullOilForEzt.php.html#55\">App\\Console\\Commands\\PullOilForEzt::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForGB.php.html#39\">App\\Console\\Commands\\PullOilForGB::__construct<\/a>"],[0,28,"<a href=\"Commands\/PullOilForGB.php.html#50\">App\\Console\\Commands\\PullOilForGB::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForGaoDeng.php.html#38\">App\\Console\\Commands\\PullOilForGaoDeng::__construct<\/a>"],[0,24,"<a href=\"Commands\/PullOilForGaoDeng.php.html#52\">App\\Console\\Commands\\PullOilForGaoDeng::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForHsy.php.html#38\">App\\Console\\Commands\\PullOilForHsy::__construct<\/a>"],[0,18,"<a href=\"Commands\/PullOilForHsy.php.html#52\">App\\Console\\Commands\\PullOilForHsy::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForHyt.php.html#42\">App\\Console\\Commands\\PullOilForHyt::__construct<\/a>"],[0,19,"<a href=\"Commands\/PullOilForHyt.php.html#57\">App\\Console\\Commands\\PullOilForHyt::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForJH.php.html#41\">App\\Console\\Commands\\PullOilForJH::__construct<\/a>"],[0,32,"<a href=\"Commands\/PullOilForJH.php.html#55\">App\\Console\\Commands\\PullOilForJH::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForJQ.php.html#39\">App\\Console\\Commands\\PullOilForJQ::__construct<\/a>"],[0,28,"<a href=\"Commands\/PullOilForJQ.php.html#50\">App\\Console\\Commands\\PullOilForJQ::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForJT.php.html#44\">App\\Console\\Commands\\PullOilForJT::__construct<\/a>"],[0,32,"<a href=\"Commands\/PullOilForJT.php.html#58\">App\\Console\\Commands\\PullOilForJT::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForJTX.php.html#41\">App\\Console\\Commands\\PullOilForJTX::__construct<\/a>"],[0,32,"<a href=\"Commands\/PullOilForJTX.php.html#55\">App\\Console\\Commands\\PullOilForJTX::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForMj.php.html#42\">App\\Console\\Commands\\PullOilForMj::__construct<\/a>"],[0,27,"<a href=\"Commands\/PullOilForMj.php.html#63\">App\\Console\\Commands\\PullOilForMj::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForMtlSy.php.html#40\">App\\Console\\Commands\\PullOilForMtlSy::__construct<\/a>"],[0,19,"<a href=\"Commands\/PullOilForMtlSy.php.html#56\">App\\Console\\Commands\\PullOilForMtlSy::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForQK.php.html#41\">App\\Console\\Commands\\PullOilForQK::__construct<\/a>"],[0,32,"<a href=\"Commands\/PullOilForQK.php.html#55\">App\\Console\\Commands\\PullOilForQK::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForSaic.php.html#42\">App\\Console\\Commands\\PullOilForSaic::__construct<\/a>"],[0,36,"<a href=\"Commands\/PullOilForSaic.php.html#53\">App\\Console\\Commands\\PullOilForSaic::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForSg.php.html#42\">App\\Console\\Commands\\PullOilForSg::__construct<\/a>"],[0,32,"<a href=\"Commands\/PullOilForSg.php.html#54\">App\\Console\\Commands\\PullOilForSg::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForSh.php.html#37\">App\\Console\\Commands\\PullOilForSh::__construct<\/a>"],[0,18,"<a href=\"Commands\/PullOilForSh.php.html#51\">App\\Console\\Commands\\PullOilForSh::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForShSx.php.html#37\">App\\Console\\Commands\\PullOilForShSx::__construct<\/a>"],[0,17,"<a href=\"Commands\/PullOilForShSx.php.html#51\">App\\Console\\Commands\\PullOilForShSx::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForSqZsh.php.html#40\">App\\Console\\Commands\\PullOilForSqZsh::__construct<\/a>"],[0,25,"<a href=\"Commands\/PullOilForSqZsh.php.html#55\">App\\Console\\Commands\\PullOilForSqZsh::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForSqzl.php.html#38\">App\\Console\\Commands\\PullOilForSqzl::__construct<\/a>"],[0,14,"<a href=\"Commands\/PullOilForSqzl.php.html#52\">App\\Console\\Commands\\PullOilForSqzl::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForTBJX.php.html#37\">App\\Console\\Commands\\PullOilForTBJX::__construct<\/a>"],[0,14,"<a href=\"Commands\/PullOilForTBJX.php.html#51\">App\\Console\\Commands\\PullOilForTBJX::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForWjy.php.html#39\">App\\Console\\Commands\\PullOilForWjy::__construct<\/a>"],[0,18,"<a href=\"Commands\/PullOilForWjy.php.html#54\">App\\Console\\Commands\\PullOilForWjy::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForXYN.php.html#39\">App\\Console\\Commands\\PullOilForXYN::__construct<\/a>"],[0,27,"<a href=\"Commands\/PullOilForXYN.php.html#53\">App\\Console\\Commands\\PullOilForXYN::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForYunDaTong.php.html#38\">App\\Console\\Commands\\PullOilForYunDaTong::__construct<\/a>"],[0,30,"<a href=\"Commands\/PullOilForYunDaTong.php.html#52\">App\\Console\\Commands\\PullOilForYunDaTong::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForZHUOYIQ.php.html#40\">App\\Console\\Commands\\PullOilForZHUOYIQ::__construct<\/a>"],[0,1,"<a href=\"Commands\/PullOilForZHUOYIQ.php.html#48\">App\\Console\\Commands\\PullOilForZHUOYIQ::getStatusMapping<\/a>"],[0,23,"<a href=\"Commands\/PullOilForZHUOYIQ.php.html#59\">App\\Console\\Commands\\PullOilForZHUOYIQ::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForZHUOYIY.php.html#40\">App\\Console\\Commands\\PullOilForZHUOYIY::__construct<\/a>"],[0,1,"<a href=\"Commands\/PullOilForZHUOYIY.php.html#48\">App\\Console\\Commands\\PullOilForZHUOYIY::getStatusMapping<\/a>"],[0,23,"<a href=\"Commands\/PullOilForZHUOYIY.php.html#59\">App\\Console\\Commands\\PullOilForZHUOYIY::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForZdc.php.html#38\">App\\Console\\Commands\\PullOilForZdc::__construct<\/a>"],[0,25,"<a href=\"Commands\/PullOilForZdc.php.html#49\">App\\Console\\Commands\\PullOilForZdc::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForZhyk.php.html#38\">App\\Console\\Commands\\PullOilForZhyk::__construct<\/a>"],[0,29,"<a href=\"Commands\/PullOilForZhyk.php.html#52\">App\\Console\\Commands\\PullOilForZhyk::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForZwl.php.html#37\">App\\Console\\Commands\\PullOilForZwl::__construct<\/a>"],[0,12,"<a href=\"Commands\/PullOilForZwl.php.html#51\">App\\Console\\Commands\\PullOilForZwl::handle<\/a>"],[0,1,"<a href=\"Commands\/PullOilForZy.php.html#42\">App\\Console\\Commands\\PullOilForZy::__construct<\/a>"],[0,36,"<a href=\"Commands\/PullOilForZy.php.html#59\">App\\Console\\Commands\\PullOilForZy::handle<\/a>"],[0,1,"<a href=\"Commands\/PullPrice.php.html#40\">App\\Console\\Commands\\PullPrice::__construct<\/a>"],[0,1,"<a href=\"Commands\/PullPrice.php.html#50\">App\\Console\\Commands\\PullPrice::handle<\/a>"],[0,1,"<a href=\"Commands\/PullPriceForCzb.php.html#35\">App\\Console\\Commands\\PullPriceForCzb::__construct<\/a>"],[0,3,"<a href=\"Commands\/PullPriceForCzb.php.html#47\">App\\Console\\Commands\\PullPriceForCzb::handle<\/a>"],[0,1,"<a href=\"Commands\/PullPriceForGS.php.html#36\">App\\Console\\Commands\\PullPriceForGS::__construct<\/a>"],[0,4,"<a href=\"Commands\/PullPriceForGS.php.html#48\">App\\Console\\Commands\\PullPriceForGS::handle<\/a>"],[0,1,"<a href=\"Commands\/PullVerificationResultForCnpc.php.html#43\">App\\Console\\Commands\\PullVerificationResultForCnpc::__construct<\/a>"],[0,7,"<a href=\"Commands\/PullVerificationResultForCnpc.php.html#55\">App\\Console\\Commands\\PullVerificationResultForCnpc::handle<\/a>"],[0,1,"<a href=\"Commands\/PullVerificationResultForHbkj.php.html#44\">App\\Console\\Commands\\PullVerificationResultForHbkj::__construct<\/a>"],[0,8,"<a href=\"Commands\/PullVerificationResultForHbkj.php.html#56\">App\\Console\\Commands\\PullVerificationResultForHbkj::handle<\/a>"],[0,1,"<a href=\"Commands\/PullVerificationResultForHsy.php.html#44\">App\\Console\\Commands\\PullVerificationResultForHsy::__construct<\/a>"],[0,7,"<a href=\"Commands\/PullVerificationResultForHsy.php.html#56\">App\\Console\\Commands\\PullVerificationResultForHsy::handle<\/a>"],[0,1,"<a href=\"Commands\/PushAccountBalanceToAD.php.html#39\">App\\Console\\Commands\\PushAccountBalanceToAD::__construct<\/a>"],[0,1,"<a href=\"Commands\/PushAccountBalanceToAD.php.html#50\">App\\Console\\Commands\\PushAccountBalanceToAD::handle<\/a>"],[0,1,"<a href=\"Commands\/PushBillToSFFY.php.html#50\">App\\Console\\Commands\\PushBillToSFFY::__construct<\/a>"],[0,11,"<a href=\"Commands\/PushBillToSFFY.php.html#61\">App\\Console\\Commands\\PushBillToSFFY::handle<\/a>"],[0,1,"<a href=\"Commands\/QueueMonitoringAlarm.php.html#38\">App\\Console\\Commands\\QueueMonitoringAlarm::__construct<\/a>"],[0,3,"<a href=\"Commands\/QueueMonitoringAlarm.php.html#49\">App\\Console\\Commands\\QueueMonitoringAlarm::handle<\/a>"],[0,1,"<a href=\"Commands\/ReleaseFrozenQuotaForDt.php.html#38\">App\\Console\\Commands\\ReleaseFrozenQuotaForDt::__construct<\/a>"],[0,4,"<a href=\"Commands\/ReleaseFrozenQuotaForDt.php.html#49\">App\\Console\\Commands\\ReleaseFrozenQuotaForDt::handle<\/a>"],[0,1,"<a href=\"Commands\/RunSpecialCode.php.html#30\">App\\Console\\Commands\\RunSpecialCode::__construct<\/a>"],[0,7,"<a href=\"Commands\/RunSpecialCode.php.html#41\">App\\Console\\Commands\\RunSpecialCode::handle<\/a>"],[0,1,"<a href=\"Kernel.php.html#127\">App\\Console\\Kernel::schedule<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
