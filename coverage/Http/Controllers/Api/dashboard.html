<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Http/Controllers/Api</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Controllers</a></li>
         <li class="breadcrumb-item"><a href="index.html">Api</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AccountController.php.html#13">App\Http\Controllers\Api\AccountController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BasicController.php.html#10">App\Http\Controllers\Api\BasicController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillController.php.html#11">App\Http\Controllers\Api\BillController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CallbackController.php.html#22">App\Http\Controllers\Api\CallbackController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CodeController.php.html#12">App\Http\Controllers\Api\CodeController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#14">App\Http\Controllers\Api\CommonController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CouponController.php.html#10">App\Http\Controllers\Api\CouponController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataController.php.html#16">App\Http\Controllers\Api\DataController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilController.php.html#10">App\Http\Controllers\Api\OilController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#23">App\Http\Controllers\Api\OrderController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SignController.php.html#11">App\Http\Controllers\Api\SignController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationController.php.html#13">App\Http\Controllers\Api\StationController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TradeController.php.html#15">App\Http\Controllers\Api\TradeController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#6">App\Http\Controllers\Api\UserController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZshController.php.html#16">App\Http\Controllers\Api\ZshController</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CallbackController.php.html#22">App\Http\Controllers\Api\CallbackController</a></td><td class="text-right">870</td></tr>
       <tr><td><a href="DataController.php.html#16">App\Http\Controllers\Api\DataController</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="StationController.php.html#13">App\Http\Controllers\Api\StationController</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="OrderController.php.html#23">App\Http\Controllers\Api\OrderController</a></td><td class="text-right">380</td></tr>
       <tr><td><a href="BasicController.php.html#10">App\Http\Controllers\Api\BasicController</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="CommonController.php.html#14">App\Http\Controllers\Api\CommonController</a></td><td class="text-right">72</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AccountController.php.html#78"><abbr title="App\Http\Controllers\Api\AccountController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#1137"><abbr title="App\Http\Controllers\Api\OrderController::refundApplication">refundApplication</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationController.php.html#1140"><abbr title="App\Http\Controllers\Api\StationController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SignController.php.html#44"><abbr title="App\Http\Controllers\Api\SignController::make">make</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SignController.php.html#31"><abbr title="App\Http\Controllers\Api\SignController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#1178"><abbr title="App\Http\Controllers\Api\OrderController::split">split</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#1168"><abbr title="App\Http\Controllers\Api\OrderController::refundCustomerForOrderCenter">refundCustomerForOrderCenter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#1159"><abbr title="App\Http\Controllers\Api\OrderController::refundCallback">refundCallback</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#1148"><abbr title="App\Http\Controllers\Api\OrderController::statusChange">statusChange</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#1124"><abbr title="App\Http\Controllers\Api\OrderController::queryFlow">queryFlow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationController.php.html#1162"><abbr title="App\Http\Controllers\Api\StationController::loadValidateCallback">loadValidateCallback</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#1102"><abbr title="App\Http\Controllers\Api\OrderController::getSecondaryPaymentCertificate">getSecondaryPaymentCertificate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#1089"><abbr title="App\Http\Controllers\Api\OrderController::cancel">cancel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#1073"><abbr title="App\Http\Controllers\Api\OrderController::query">query</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#1058"><abbr title="App\Http\Controllers\Api\OrderController::reToBePaid">reToBePaid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#1033"><abbr title="App\Http\Controllers\Api\OrderController::toBePaid">toBePaid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#1015"><abbr title="App\Http\Controllers\Api\OrderController::loadInputNoData">loadInputNoData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#950"><abbr title="App\Http\Controllers\Api\OrderController::loadValidateCallback">loadValidateCallback</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationController.php.html#1147"><abbr title="App\Http\Controllers\Api\StationController::loadInputNoData">loadInputNoData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationController.php.html#1229"><abbr title="App\Http\Controllers\Api\StationController::queryOne">queryOne</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilController.php.html#63"><abbr title="App\Http\Controllers\Api\OilController::getGunNosByStationIds">getGunNosByStationIds</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TradeController.php.html#965"><abbr title="App\Http\Controllers\Api\TradeController::reToBePaidCallback">reToBePaidCallback</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZshController.php.html#151"><abbr title="App\Http\Controllers\Api\ZshController::couponQuery">couponQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZshController.php.html#137"><abbr title="App\Http\Controllers\Api\ZshController::orderQuery">orderQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZshController.php.html#123"><abbr title="App\Http\Controllers\Api\ZshController::couponPurchase">couponPurchase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZshController.php.html#109"><abbr title="App\Http\Controllers\Api\ZshController::prePayBalanceQuery">prePayBalanceQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZshController.php.html#96"><abbr title="App\Http\Controllers\Api\ZshController::couponList">couponList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZshController.php.html#84"><abbr title="App\Http\Controllers\Api\ZshController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#48"><abbr title="App\Http\Controllers\Api\UserController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TradeController.php.html#951"><abbr title="App\Http\Controllers\Api\TradeController::query">query</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationController.php.html#1244"><abbr title="App\Http\Controllers\Api\StationController::queryAll">queryAll</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TradeController.php.html#945"><abbr title="App\Http\Controllers\Api\TradeController::refund">refund</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TradeController.php.html#931"><abbr title="App\Http\Controllers\Api\TradeController::pay">pay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TradeController.php.html#917"><abbr title="App\Http\Controllers\Api\TradeController::payBySn">payBySn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TradeController.php.html#803"><abbr title="App\Http\Controllers\Api\TradeController::loadValidateCallback">loadValidateCallback</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TradeController.php.html#780"><abbr title="App\Http\Controllers\Api\TradeController::loadInputNoData">loadInputNoData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TradeController.php.html#773"><abbr title="App\Http\Controllers\Api\TradeController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationController.php.html#1259"><abbr title="App\Http\Controllers\Api\StationController::receive">receive</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#943"><abbr title="App\Http\Controllers\Api\OrderController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilController.php.html#57"><abbr title="App\Http\Controllers\Api\OilController::getGunNosByStation">getGunNosByStation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#84"><abbr title="App\Http\Controllers\Api\AccountController::loadInputNoData">loadInputNoData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillController.php.html#71"><abbr title="App\Http\Controllers\Api\BillController::push">push</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CallbackController.php.html#297"><abbr title="App\Http\Controllers\Api\CallbackController::jhcx">jhcx</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CallbackController.php.html#285"><abbr title="App\Http\Controllers\Api\CallbackController::dt">dt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CallbackController.php.html#248"><abbr title="App\Http\Controllers\Api\CallbackController::hyt">hyt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CallbackController.php.html#235"><abbr title="App\Http\Controllers\Api\CallbackController::healthCheck">healthCheck</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CallbackController.php.html#150"><abbr title="App\Http\Controllers\Api\CallbackController::refundForCzb">refundForCzb</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CallbackController.php.html#145"><abbr title="App\Http\Controllers\Api\CallbackController::gos">gos</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CallbackController.php.html#107"><abbr title="App\Http\Controllers\Api\CallbackController::bdt">bdt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillController.php.html#62"><abbr title="App\Http\Controllers\Api\BillController::query">query</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CallbackController.php.html#346"><abbr title="App\Http\Controllers\Api\CallbackController::jh">jh</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillController.php.html#49"><abbr title="App\Http\Controllers\Api\BillController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BasicController.php.html#74"><abbr title="App\Http\Controllers\Api\BasicController::initAuthDataByRoleCode">initAuthDataByRoleCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BasicController.php.html#32"><abbr title="App\Http\Controllers\Api\BasicController::validateRequestParam">validateRequestParam</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BasicController.php.html#19"><abbr title="App\Http\Controllers\Api\BasicController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#127"><abbr title="App\Http\Controllers\Api\AccountController::companyReceive">companyReceive</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#113"><abbr title="App\Http\Controllers\Api\AccountController::change">change</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#99"><abbr title="App\Http\Controllers\Api\AccountController::query">query</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CallbackController.php.html#326"><abbr title="App\Http\Controllers\Api\CallbackController::jt">jt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CallbackController.php.html#366"><abbr title="App\Http\Controllers\Api\CallbackController::qk">qk</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OilController.php.html#51"><abbr title="App\Http\Controllers\Api\OilController::getGunNosByStationAndOil">getGunNosByStationAndOil</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CouponController.php.html#27"><abbr title="App\Http\Controllers\Api\CouponController::proxy">proxy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataController.php.html#365"><abbr title="App\Http\Controllers\Api\DataController::receiveStationPushRule">receiveStationPushRule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataController.php.html#359"><abbr title="App\Http\Controllers\Api\DataController::receiveStationBlackAndWhiteList">receiveStationBlackAndWhiteList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataController.php.html#351"><abbr title="App\Http\Controllers\Api\DataController::getStopStationAndAvailableStationForCustomerConfig">getStopStationAndAvailableStationForCustomerConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataController.php.html#338"><abbr title="App\Http\Controllers\Api\DataController::getAvailableStationListForCustomer">getAvailableStationListForCustomer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataController.php.html#332"><abbr title="App\Http\Controllers\Api\DataController::getStationWhiteList">getStationWhiteList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataController.php.html#87"><abbr title="App\Http\Controllers\Api\DataController::push">push</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataController.php.html#74"><abbr title="App\Http\Controllers\Api\DataController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#66"><abbr title="App\Http\Controllers\Api\CommonController::sign">sign</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CallbackController.php.html#386"><abbr title="App\Http\Controllers\Api\CallbackController::jtx">jtx</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#43"><abbr title="App\Http\Controllers\Api\CommonController::imitateWrittenOff">imitateWrittenOff</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#24"><abbr title="App\Http\Controllers\Api\CommonController::cancelZwlOrder">cancelZwlOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#19"><abbr title="App\Http\Controllers\Api\CommonController::test">test</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CodeController.php.html#97"><abbr title="App\Http\Controllers\Api\CodeController::getSecondaryPaymentQrCode">getSecondaryPaymentQrCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CodeController.php.html#83"><abbr title="App\Http\Controllers\Api\CodeController::parse">parse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CodeController.php.html#73"><abbr title="App\Http\Controllers\Api\CodeController::loadInputNoData">loadInputNoData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CodeController.php.html#62"><abbr title="App\Http\Controllers\Api\CodeController::loadValidateCallback">loadValidateCallback</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CodeController.php.html#55"><abbr title="App\Http\Controllers\Api\CodeController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZshController.php.html#165"><abbr title="App\Http\Controllers\Api\ZshController::getPreAmount">getPreAmount</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DataController.php.html#87"><abbr title="App\Http\Controllers\Api\DataController::push">push</abbr></a></td><td class="text-right">380</td></tr>
       <tr><td><a href="StationController.php.html#1259"><abbr title="App\Http\Controllers\Api\StationController::receive">receive</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="BasicController.php.html#32"><abbr title="App\Http\Controllers\Api\BasicController::validateRequestParam">validateRequestParam</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="CallbackController.php.html#150"><abbr title="App\Http\Controllers\Api\CallbackController::refundForCzb">refundForCzb</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="CallbackController.php.html#107"><abbr title="App\Http\Controllers\Api\CallbackController::bdt">bdt</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="CallbackController.php.html#248"><abbr title="App\Http\Controllers\Api\CallbackController::hyt">hyt</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CallbackController.php.html#326"><abbr title="App\Http\Controllers\Api\CallbackController::jt">jt</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CallbackController.php.html#346"><abbr title="App\Http\Controllers\Api\CallbackController::jh">jh</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CallbackController.php.html#366"><abbr title="App\Http\Controllers\Api\CallbackController::qk">qk</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CommonController.php.html#43"><abbr title="App\Http\Controllers\Api\CommonController::imitateWrittenOff">imitateWrittenOff</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OrderController.php.html#1033"><abbr title="App\Http\Controllers\Api\OrderController::toBePaid">toBePaid</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OrderController.php.html#1089"><abbr title="App\Http\Controllers\Api\OrderController::cancel">cancel</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CallbackController.php.html#386"><abbr title="App\Http\Controllers\Api\CallbackController::jtx">jtx</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CallbackController.php.html#297"><abbr title="App\Http\Controllers\Api\CallbackController::jhcx">jhcx</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CommonController.php.html#24"><abbr title="App\Http\Controllers\Api\CommonController::cancelZwlOrder">cancelZwlOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CommonController.php.html#66"><abbr title="App\Http\Controllers\Api\CommonController::sign">sign</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OrderController.php.html#1102"><abbr title="App\Http\Controllers\Api\OrderController::getSecondaryPaymentCertificate">getSecondaryPaymentCertificate</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:44:39 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([15,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([80,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,5,"<a href=\"AccountController.php.html#13\">App\\Http\\Controllers\\Api\\AccountController<\/a>"],[0,14,"<a href=\"BasicController.php.html#10\">App\\Http\\Controllers\\Api\\BasicController<\/a>"],[0,3,"<a href=\"BillController.php.html#11\">App\\Http\\Controllers\\Api\\BillController<\/a>"],[0,29,"<a href=\"CallbackController.php.html#22\">App\\Http\\Controllers\\Api\\CallbackController<\/a>"],[0,5,"<a href=\"CodeController.php.html#12\">App\\Http\\Controllers\\Api\\CodeController<\/a>"],[0,8,"<a href=\"CommonController.php.html#14\">App\\Http\\Controllers\\Api\\CommonController<\/a>"],[0,1,"<a href=\"CouponController.php.html#10\">App\\Http\\Controllers\\Api\\CouponController<\/a>"],[0,25,"<a href=\"DataController.php.html#16\">App\\Http\\Controllers\\Api\\DataController<\/a>"],[0,3,"<a href=\"OilController.php.html#10\">App\\Http\\Controllers\\Api\\OilController<\/a>"],[0,19,"<a href=\"OrderController.php.html#23\">App\\Http\\Controllers\\Api\\OrderController<\/a>"],[0,2,"<a href=\"SignController.php.html#11\">App\\Http\\Controllers\\Api\\SignController<\/a>"],[0,22,"<a href=\"StationController.php.html#13\">App\\Http\\Controllers\\Api\\StationController<\/a>"],[0,8,"<a href=\"TradeController.php.html#15\">App\\Http\\Controllers\\Api\\TradeController<\/a>"],[0,1,"<a href=\"UserController.php.html#6\">App\\Http\\Controllers\\Api\\UserController<\/a>"],[0,7,"<a href=\"ZshController.php.html#16\">App\\Http\\Controllers\\Api\\ZshController<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AccountController.php.html#78\">App\\Http\\Controllers\\Api\\AccountController::__construct<\/a>"],[0,1,"<a href=\"AccountController.php.html#84\">App\\Http\\Controllers\\Api\\AccountController::loadInputNoData<\/a>"],[0,1,"<a href=\"AccountController.php.html#99\">App\\Http\\Controllers\\Api\\AccountController::query<\/a>"],[0,1,"<a href=\"AccountController.php.html#113\">App\\Http\\Controllers\\Api\\AccountController::change<\/a>"],[0,1,"<a href=\"AccountController.php.html#127\">App\\Http\\Controllers\\Api\\AccountController::companyReceive<\/a>"],[0,1,"<a href=\"BasicController.php.html#19\">App\\Http\\Controllers\\Api\\BasicController::__construct<\/a>"],[0,12,"<a href=\"BasicController.php.html#32\">App\\Http\\Controllers\\Api\\BasicController::validateRequestParam<\/a>"],[0,1,"<a href=\"BasicController.php.html#74\">App\\Http\\Controllers\\Api\\BasicController::initAuthDataByRoleCode<\/a>"],[0,1,"<a href=\"BillController.php.html#49\">App\\Http\\Controllers\\Api\\BillController::__construct<\/a>"],[0,1,"<a href=\"BillController.php.html#62\">App\\Http\\Controllers\\Api\\BillController::query<\/a>"],[0,1,"<a href=\"BillController.php.html#71\">App\\Http\\Controllers\\Api\\BillController::push<\/a>"],[0,4,"<a href=\"CallbackController.php.html#107\">App\\Http\\Controllers\\Api\\CallbackController::bdt<\/a>"],[0,1,"<a href=\"CallbackController.php.html#145\">App\\Http\\Controllers\\Api\\CallbackController::gos<\/a>"],[0,5,"<a href=\"CallbackController.php.html#150\">App\\Http\\Controllers\\Api\\CallbackController::refundForCzb<\/a>"],[0,1,"<a href=\"CallbackController.php.html#235\">App\\Http\\Controllers\\Api\\CallbackController::healthCheck<\/a>"],[0,3,"<a href=\"CallbackController.php.html#248\">App\\Http\\Controllers\\Api\\CallbackController::hyt<\/a>"],[0,1,"<a href=\"CallbackController.php.html#285\">App\\Http\\Controllers\\Api\\CallbackController::dt<\/a>"],[0,2,"<a href=\"CallbackController.php.html#297\">App\\Http\\Controllers\\Api\\CallbackController::jhcx<\/a>"],[0,3,"<a href=\"CallbackController.php.html#326\">App\\Http\\Controllers\\Api\\CallbackController::jt<\/a>"],[0,3,"<a href=\"CallbackController.php.html#346\">App\\Http\\Controllers\\Api\\CallbackController::jh<\/a>"],[0,3,"<a href=\"CallbackController.php.html#366\">App\\Http\\Controllers\\Api\\CallbackController::qk<\/a>"],[0,3,"<a href=\"CallbackController.php.html#386\">App\\Http\\Controllers\\Api\\CallbackController::jtx<\/a>"],[0,1,"<a href=\"CodeController.php.html#55\">App\\Http\\Controllers\\Api\\CodeController::__construct<\/a>"],[0,1,"<a href=\"CodeController.php.html#62\">App\\Http\\Controllers\\Api\\CodeController::loadValidateCallback<\/a>"],[0,1,"<a href=\"CodeController.php.html#73\">App\\Http\\Controllers\\Api\\CodeController::loadInputNoData<\/a>"],[0,1,"<a href=\"CodeController.php.html#83\">App\\Http\\Controllers\\Api\\CodeController::parse<\/a>"],[0,1,"<a href=\"CodeController.php.html#97\">App\\Http\\Controllers\\Api\\CodeController::getSecondaryPaymentQrCode<\/a>"],[0,1,"<a href=\"CommonController.php.html#19\">App\\Http\\Controllers\\Api\\CommonController::test<\/a>"],[0,2,"<a href=\"CommonController.php.html#24\">App\\Http\\Controllers\\Api\\CommonController::cancelZwlOrder<\/a>"],[0,3,"<a href=\"CommonController.php.html#43\">App\\Http\\Controllers\\Api\\CommonController::imitateWrittenOff<\/a>"],[0,2,"<a href=\"CommonController.php.html#66\">App\\Http\\Controllers\\Api\\CommonController::sign<\/a>"],[0,1,"<a href=\"CouponController.php.html#27\">App\\Http\\Controllers\\Api\\CouponController::proxy<\/a>"],[0,1,"<a href=\"DataController.php.html#74\">App\\Http\\Controllers\\Api\\DataController::__construct<\/a>"],[0,19,"<a href=\"DataController.php.html#87\">App\\Http\\Controllers\\Api\\DataController::push<\/a>"],[0,1,"<a href=\"DataController.php.html#332\">App\\Http\\Controllers\\Api\\DataController::getStationWhiteList<\/a>"],[0,1,"<a href=\"DataController.php.html#338\">App\\Http\\Controllers\\Api\\DataController::getAvailableStationListForCustomer<\/a>"],[0,1,"<a href=\"DataController.php.html#351\">App\\Http\\Controllers\\Api\\DataController::getStopStationAndAvailableStationForCustomerConfig<\/a>"],[0,1,"<a href=\"DataController.php.html#359\">App\\Http\\Controllers\\Api\\DataController::receiveStationBlackAndWhiteList<\/a>"],[0,1,"<a href=\"DataController.php.html#365\">App\\Http\\Controllers\\Api\\DataController::receiveStationPushRule<\/a>"],[0,1,"<a href=\"OilController.php.html#51\">App\\Http\\Controllers\\Api\\OilController::getGunNosByStationAndOil<\/a>"],[0,1,"<a href=\"OilController.php.html#57\">App\\Http\\Controllers\\Api\\OilController::getGunNosByStation<\/a>"],[0,1,"<a href=\"OilController.php.html#63\">App\\Http\\Controllers\\Api\\OilController::getGunNosByStationIds<\/a>"],[0,1,"<a href=\"OrderController.php.html#943\">App\\Http\\Controllers\\Api\\OrderController::__construct<\/a>"],[0,1,"<a href=\"OrderController.php.html#950\">App\\Http\\Controllers\\Api\\OrderController::loadValidateCallback<\/a>"],[0,1,"<a href=\"OrderController.php.html#1015\">App\\Http\\Controllers\\Api\\OrderController::loadInputNoData<\/a>"],[0,3,"<a href=\"OrderController.php.html#1033\">App\\Http\\Controllers\\Api\\OrderController::toBePaid<\/a>"],[0,1,"<a href=\"OrderController.php.html#1058\">App\\Http\\Controllers\\Api\\OrderController::reToBePaid<\/a>"],[0,1,"<a href=\"OrderController.php.html#1073\">App\\Http\\Controllers\\Api\\OrderController::query<\/a>"],[0,3,"<a href=\"OrderController.php.html#1089\">App\\Http\\Controllers\\Api\\OrderController::cancel<\/a>"],[0,2,"<a href=\"OrderController.php.html#1102\">App\\Http\\Controllers\\Api\\OrderController::getSecondaryPaymentCertificate<\/a>"],[0,1,"<a href=\"OrderController.php.html#1124\">App\\Http\\Controllers\\Api\\OrderController::queryFlow<\/a>"],[0,1,"<a href=\"OrderController.php.html#1137\">App\\Http\\Controllers\\Api\\OrderController::refundApplication<\/a>"],[0,1,"<a href=\"OrderController.php.html#1148\">App\\Http\\Controllers\\Api\\OrderController::statusChange<\/a>"],[0,1,"<a href=\"OrderController.php.html#1159\">App\\Http\\Controllers\\Api\\OrderController::refundCallback<\/a>"],[0,1,"<a href=\"OrderController.php.html#1168\">App\\Http\\Controllers\\Api\\OrderController::refundCustomerForOrderCenter<\/a>"],[0,1,"<a href=\"OrderController.php.html#1178\">App\\Http\\Controllers\\Api\\OrderController::split<\/a>"],[0,1,"<a href=\"SignController.php.html#31\">App\\Http\\Controllers\\Api\\SignController::__construct<\/a>"],[0,1,"<a href=\"SignController.php.html#44\">App\\Http\\Controllers\\Api\\SignController::make<\/a>"],[0,1,"<a href=\"StationController.php.html#1140\">App\\Http\\Controllers\\Api\\StationController::__construct<\/a>"],[0,1,"<a href=\"StationController.php.html#1147\">App\\Http\\Controllers\\Api\\StationController::loadInputNoData<\/a>"],[0,1,"<a href=\"StationController.php.html#1162\">App\\Http\\Controllers\\Api\\StationController::loadValidateCallback<\/a>"],[0,1,"<a href=\"StationController.php.html#1229\">App\\Http\\Controllers\\Api\\StationController::queryOne<\/a>"],[0,1,"<a href=\"StationController.php.html#1244\">App\\Http\\Controllers\\Api\\StationController::queryAll<\/a>"],[0,17,"<a href=\"StationController.php.html#1259\">App\\Http\\Controllers\\Api\\StationController::receive<\/a>"],[0,1,"<a href=\"TradeController.php.html#773\">App\\Http\\Controllers\\Api\\TradeController::__construct<\/a>"],[0,1,"<a href=\"TradeController.php.html#780\">App\\Http\\Controllers\\Api\\TradeController::loadInputNoData<\/a>"],[0,1,"<a href=\"TradeController.php.html#803\">App\\Http\\Controllers\\Api\\TradeController::loadValidateCallback<\/a>"],[0,1,"<a href=\"TradeController.php.html#917\">App\\Http\\Controllers\\Api\\TradeController::payBySn<\/a>"],[0,1,"<a href=\"TradeController.php.html#931\">App\\Http\\Controllers\\Api\\TradeController::pay<\/a>"],[0,1,"<a href=\"TradeController.php.html#945\">App\\Http\\Controllers\\Api\\TradeController::refund<\/a>"],[0,1,"<a href=\"TradeController.php.html#951\">App\\Http\\Controllers\\Api\\TradeController::query<\/a>"],[0,1,"<a href=\"TradeController.php.html#965\">App\\Http\\Controllers\\Api\\TradeController::reToBePaidCallback<\/a>"],[0,1,"<a href=\"UserController.php.html#48\">App\\Http\\Controllers\\Api\\UserController::__construct<\/a>"],[0,1,"<a href=\"ZshController.php.html#84\">App\\Http\\Controllers\\Api\\ZshController::__construct<\/a>"],[0,1,"<a href=\"ZshController.php.html#96\">App\\Http\\Controllers\\Api\\ZshController::couponList<\/a>"],[0,1,"<a href=\"ZshController.php.html#109\">App\\Http\\Controllers\\Api\\ZshController::prePayBalanceQuery<\/a>"],[0,1,"<a href=\"ZshController.php.html#123\">App\\Http\\Controllers\\Api\\ZshController::couponPurchase<\/a>"],[0,1,"<a href=\"ZshController.php.html#137\">App\\Http\\Controllers\\Api\\ZshController::orderQuery<\/a>"],[0,1,"<a href=\"ZshController.php.html#151\">App\\Http\\Controllers\\Api\\ZshController::couponQuery<\/a>"],[0,1,"<a href=\"ZshController.php.html#165\">App\\Http\\Controllers\\Api\\ZshController::getPreAmount<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
