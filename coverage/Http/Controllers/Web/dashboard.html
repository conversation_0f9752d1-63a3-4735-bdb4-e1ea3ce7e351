<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Http/Controllers/Web</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Controllers</a></li>
         <li class="breadcrumb-item"><a href="index.html">Web</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BasicController.php.html#13">App\Http\Controllers\Web\BasicController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogController.php.html#13">App\Http\Controllers\Web\RequestLogController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolController.php.html#14">App\Http\Controllers\Web\ToolController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierController.php.html#13">App\Http\Controllers\Web\SupplierController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitchController.php.html#14">App\Http\Controllers\Web\StationPushSwitchController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushRecordController.php.html#12">App\Http\Controllers\Web\StationPushRecordController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushConditionController.php.html#13">App\Http\Controllers\Web\StationPushConditionController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPriceController.php.html#14">App\Http\Controllers\Web\StationPriceController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePermissionController.php.html#13">App\Http\Controllers\Web\RolePermissionController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#13">App\Http\Controllers\Web\RoleController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResponseLogController.php.html#13">App\Http\Controllers\Web\ResponseLogController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReceiveLogController.php.html#13">App\Http\Controllers\Web\ReceiveLogController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#13">App\Http\Controllers\Web\CommonController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueueLogController.php.html#13">App\Http\Controllers\Web\QueueLogController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PermissionController.php.html#13">App\Http\Controllers\Web\PermissionController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#14">App\Http\Controllers\Web\OrderController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssocController.php.html#12">App\Http\Controllers\Web\OrderAssocController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#18">App\Http\Controllers\Web\LoginController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HomeController.php.html#7">App\Http\Controllers\Web\HomeController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FailedJobsController.php.html#12">App\Http\Controllers\Web\FailedJobsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformController.php.html#13">App\Http\Controllers\Web\DockingPlatformController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfigController.php.html#12">App\Http\Controllers\Web\ConfigController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#13">App\Http\Controllers\Web\UserController</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BasicController.php.html#13">App\Http\Controllers\Web\BasicController</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="CommonController.php.html#13">App\Http\Controllers\Web\CommonController</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="LoginController.php.html#18">App\Http\Controllers\Web\LoginController</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="UserController.php.html#13">App\Http\Controllers\Web\UserController</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="QueueLogController.php.html#13">App\Http\Controllers\Web\QueueLogController</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ReceiveLogController.php.html#13">App\Http\Controllers\Web\ReceiveLogController</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RequestLogController.php.html#13">App\Http\Controllers\Web\RequestLogController</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ResponseLogController.php.html#13">App\Http\Controllers\Web\ResponseLogController</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ToolController.php.html#14">App\Http\Controllers\Web\ToolController</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BasicController.php.html#20"><abbr title="App\Http\Controllers\Web\BasicController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePermissionController.php.html#75"><abbr title="App\Http\Controllers\Web\RolePermissionController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushConditionController.php.html#86"><abbr title="App\Http\Controllers\Web\StationPushConditionController::stationPushAll">stationPushAll</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushConditionController.php.html#72"><abbr title="App\Http\Controllers\Web\StationPushConditionController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushConditionController.php.html#59"><abbr title="App\Http\Controllers\Web\StationPushConditionController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPriceController.php.html#66"><abbr title="App\Http\Controllers\Web\StationPriceController::getSelect">getSelect</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPriceController.php.html#54"><abbr title="App\Http\Controllers\Web\StationPriceController::stationPull">stationPull</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPriceController.php.html#40"><abbr title="App\Http\Controllers\Web\StationPriceController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPriceController.php.html#34"><abbr title="App\Http\Controllers\Web\StationPriceController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePermissionController.php.html#105"><abbr title="App\Http\Controllers\Web\RolePermissionController::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePermissionController.php.html#90"><abbr title="App\Http\Controllers\Web\RolePermissionController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePermissionController.php.html#61"><abbr title="App\Http\Controllers\Web\RolePermissionController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushConditionController.php.html#115"><abbr title="App\Http\Controllers\Web\StationPushConditionController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#113"><abbr title="App\Http\Controllers\Web\RoleController::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#98"><abbr title="App\Http\Controllers\Web\RoleController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#83"><abbr title="App\Http\Controllers\Web\RoleController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#69"><abbr title="App\Http\Controllers\Web\RoleController::getSelect">getSelect</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#55"><abbr title="App\Http\Controllers\Web\RoleController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResponseLogController.php.html#48"><abbr title="App\Http\Controllers\Web\ResponseLogController::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResponseLogController.php.html#42"><abbr title="App\Http\Controllers\Web\ResponseLogController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResponseLogController.php.html#29"><abbr title="App\Http\Controllers\Web\ResponseLogController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogController.php.html#40"><abbr title="App\Http\Controllers\Web\RequestLogController::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushConditionController.php.html#100"><abbr title="App\Http\Controllers\Web\StationPushConditionController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushConditionController.php.html#130"><abbr title="App\Http\Controllers\Web\StationPushConditionController::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogController.php.html#21"><abbr title="App\Http\Controllers\Web\RequestLogController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierController.php.html#135"><abbr title="App\Http\Controllers\Web\SupplierController::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#156"><abbr title="App\Http\Controllers\Web\UserController::updateSecret">updateSecret</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#145"><abbr title="App\Http\Controllers\Web\UserController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#126"><abbr title="App\Http\Controllers\Web\UserController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#112"><abbr title="App\Http\Controllers\Web\UserController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#99"><abbr title="App\Http\Controllers\Web\UserController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolController.php.html#95"><abbr title="App\Http\Controllers\Web\ToolController::pushBill">pushBill</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolController.php.html#81"><abbr title="App\Http\Controllers\Web\ToolController::queryBillCheckResult">queryBillCheckResult</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolController.php.html#67"><abbr title="App\Http\Controllers\Web\ToolController::queryDriverInfo">queryDriverInfo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierController.php.html#150"><abbr title="App\Http\Controllers\Web\SupplierController::getSupplierNameBySupplierCodes">getSupplierNameBySupplierCodes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierController.php.html#120"><abbr title="App\Http\Controllers\Web\SupplierController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushRecordController.php.html#36"><abbr title="App\Http\Controllers\Web\StationPushRecordController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierController.php.html#105"><abbr title="App\Http\Controllers\Web\SupplierController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierController.php.html#91"><abbr title="App\Http\Controllers\Web\SupplierController::getAllData">getAllData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierController.php.html#78"><abbr title="App\Http\Controllers\Web\SupplierController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplierController.php.html#65"><abbr title="App\Http\Controllers\Web\SupplierController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitchController.php.html#138"><abbr title="App\Http\Controllers\Web\StationPushSwitchController::getPushedStationList">getPushedStationList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitchController.php.html#127"><abbr title="App\Http\Controllers\Web\StationPushSwitchController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitchController.php.html#113"><abbr title="App\Http\Controllers\Web\StationPushSwitchController::stationPushAll">stationPushAll</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitchController.php.html#98"><abbr title="App\Http\Controllers\Web\StationPushSwitchController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitchController.php.html#84"><abbr title="App\Http\Controllers\Web\StationPushSwitchController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StationPushSwitchController.php.html#71"><abbr title="App\Http\Controllers\Web\StationPushSwitchController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogController.php.html#34"><abbr title="App\Http\Controllers\Web\RequestLogController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReceiveLogController.php.html#48"><abbr title="App\Http\Controllers\Web\ReceiveLogController::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BasicController.php.html#96"><abbr title="App\Http\Controllers\Web\BasicController::validateRequestParam">validateRequestParam</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfigController.php.html#81"><abbr title="App\Http\Controllers\Web\ConfigController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FailedJobsController.php.html#20"><abbr title="App\Http\Controllers\Web\FailedJobsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformController.php.html#141"><abbr title="App\Http\Controllers\Web\DockingPlatformController::getPlatformNameByNameAbbreviations">getPlatformNameByNameAbbreviations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformController.php.html#127"><abbr title="App\Http\Controllers\Web\DockingPlatformController::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformController.php.html#112"><abbr title="App\Http\Controllers\Web\DockingPlatformController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformController.php.html#97"><abbr title="App\Http\Controllers\Web\DockingPlatformController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformController.php.html#83"><abbr title="App\Http\Controllers\Web\DockingPlatformController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DockingPlatformController.php.html#70"><abbr title="App\Http\Controllers\Web\DockingPlatformController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfigController.php.html#111"><abbr title="App\Http\Controllers\Web\ConfigController::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfigController.php.html#96"><abbr title="App\Http\Controllers\Web\ConfigController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfigController.php.html#67"><abbr title="App\Http\Controllers\Web\ConfigController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HomeController.php.html#15"><abbr title="App\Http\Controllers\Web\HomeController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConfigController.php.html#54"><abbr title="App\Http\Controllers\Web\ConfigController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#152"><abbr title="App\Http\Controllers\Web\CommonController::getOrderRefundReason">getOrderRefundReason</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#132"><abbr title="App\Http\Controllers\Web\CommonController::getOilUnitList">getOilUnitList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#112"><abbr title="App\Http\Controllers\Web\CommonController::getTradeTypeList">getTradeTypeList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#99"><abbr title="App\Http\Controllers\Web\CommonController::getCurrentDayLogCountByType">getCurrentDayLogCountByType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#83"><abbr title="App\Http\Controllers\Web\CommonController::getStationSourceList">getStationSourceList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#64"><abbr title="App\Http\Controllers\Web\CommonController::getQueueLogTypeList">getQueueLogTypeList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#47"><abbr title="App\Http\Controllers\Web\CommonController::getLogLevelList">getLogLevelList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CommonController.php.html#35"><abbr title="App\Http\Controllers\Web\CommonController::getThirdPartyPlatformList">getThirdPartyPlatformList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FailedJobsController.php.html#33"><abbr title="App\Http\Controllers\Web\FailedJobsController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#37"><abbr title="App\Http\Controllers\Web\LoginController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReceiveLogController.php.html#42"><abbr title="App\Http\Controllers\Web\ReceiveLogController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PermissionController.php.html#62"><abbr title="App\Http\Controllers\Web\PermissionController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReceiveLogController.php.html#29"><abbr title="App\Http\Controllers\Web\ReceiveLogController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueueLogController.php.html#40"><abbr title="App\Http\Controllers\Web\QueueLogController::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueueLogController.php.html#34"><abbr title="App\Http\Controllers\Web\QueueLogController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueueLogController.php.html#21"><abbr title="App\Http\Controllers\Web\QueueLogController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PermissionController.php.html#134"><abbr title="App\Http\Controllers\Web\PermissionController::getTree">getTree</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PermissionController.php.html#121"><abbr title="App\Http\Controllers\Web\PermissionController::getSelect">getSelect</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PermissionController.php.html#106"><abbr title="App\Http\Controllers\Web\PermissionController::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PermissionController.php.html#91"><abbr title="App\Http\Controllers\Web\PermissionController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PermissionController.php.html#76"><abbr title="App\Http\Controllers\Web\PermissionController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#94"><abbr title="App\Http\Controllers\Web\OrderController::getSecondaryPaymentQrCode">getSecondaryPaymentQrCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#47"><abbr title="App\Http\Controllers\Web\LoginController::getCaptcha">getCaptcha</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#81"><abbr title="App\Http\Controllers\Web\OrderController::retryFailedOrder">retryFailedOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#72"><abbr title="App\Http\Controllers\Web\OrderController::doRefund">doRefund</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#58"><abbr title="App\Http\Controllers\Web\OrderController::getRefundList">getRefundList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderController.php.html#43"><abbr title="App\Http\Controllers\Web\OrderController::refund">refund</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssocController.php.html#39"><abbr title="App\Http\Controllers\Web\OrderAssocController::getChart">getChart</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssocController.php.html#33"><abbr title="App\Http\Controllers\Web\OrderAssocController::getData">getData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderAssocController.php.html#20"><abbr title="App\Http\Controllers\Web\OrderAssocController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#117"><abbr title="App\Http\Controllers\Web\LoginController::getLoginUserInfo">getLoginUserInfo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#103"><abbr title="App\Http\Controllers\Web\LoginController::out">out</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#89"><abbr title="App\Http\Controllers\Web\LoginController::in">in</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#173"><abbr title="App\Http\Controllers\Web\UserController::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BasicController.php.html#20"><abbr title="App\Http\Controllers\Web\BasicController::__construct">__construct</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="BasicController.php.html#96"><abbr title="App\Http\Controllers\Web\BasicController::validateRequestParam">validateRequestParam</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="LoginController.php.html#47"><abbr title="App\Http\Controllers\Web\LoginController::getCaptcha">getCaptcha</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CommonController.php.html#112"><abbr title="App\Http\Controllers\Web\CommonController::getTradeTypeList">getTradeTypeList</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CommonController.php.html#132"><abbr title="App\Http\Controllers\Web\CommonController::getOilUnitList">getOilUnitList</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CommonController.php.html#152"><abbr title="App\Http\Controllers\Web\CommonController::getOrderRefundReason">getOrderRefundReason</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="LoginController.php.html#37"><abbr title="App\Http\Controllers\Web\LoginController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QueueLogController.php.html#40"><abbr title="App\Http\Controllers\Web\QueueLogController::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReceiveLogController.php.html#48"><abbr title="App\Http\Controllers\Web\ReceiveLogController::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RequestLogController.php.html#40"><abbr title="App\Http\Controllers\Web\RequestLogController::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ResponseLogController.php.html#48"><abbr title="App\Http\Controllers\Web\ResponseLogController::getCurDayCount">getCurDayCount</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToolController.php.html#95"><abbr title="App\Http\Controllers\Web\ToolController::pushBill">pushBill</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserController.php.html#126"><abbr title="App\Http\Controllers\Web\UserController::create">create</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserController.php.html#145"><abbr title="App\Http\Controllers\Web\UserController::update">update</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:44:39 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([23,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([97,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,14,"<a href=\"BasicController.php.html#13\">App\\Http\\Controllers\\Web\\BasicController<\/a>"],[0,11,"<a href=\"CommonController.php.html#13\">App\\Http\\Controllers\\Web\\CommonController<\/a>"],[0,5,"<a href=\"ConfigController.php.html#12\">App\\Http\\Controllers\\Web\\ConfigController<\/a>"],[0,6,"<a href=\"DockingPlatformController.php.html#13\">App\\Http\\Controllers\\Web\\DockingPlatformController<\/a>"],[0,2,"<a href=\"FailedJobsController.php.html#12\">App\\Http\\Controllers\\Web\\FailedJobsController<\/a>"],[0,1,"<a href=\"HomeController.php.html#7\">App\\Http\\Controllers\\Web\\HomeController<\/a>"],[0,8,"<a href=\"LoginController.php.html#18\">App\\Http\\Controllers\\Web\\LoginController<\/a>"],[0,3,"<a href=\"OrderAssocController.php.html#12\">App\\Http\\Controllers\\Web\\OrderAssocController<\/a>"],[0,5,"<a href=\"OrderController.php.html#14\">App\\Http\\Controllers\\Web\\OrderController<\/a>"],[0,6,"<a href=\"PermissionController.php.html#13\">App\\Http\\Controllers\\Web\\PermissionController<\/a>"],[0,4,"<a href=\"QueueLogController.php.html#13\">App\\Http\\Controllers\\Web\\QueueLogController<\/a>"],[0,4,"<a href=\"ReceiveLogController.php.html#13\">App\\Http\\Controllers\\Web\\ReceiveLogController<\/a>"],[0,4,"<a href=\"RequestLogController.php.html#13\">App\\Http\\Controllers\\Web\\RequestLogController<\/a>"],[0,4,"<a href=\"ResponseLogController.php.html#13\">App\\Http\\Controllers\\Web\\ResponseLogController<\/a>"],[0,5,"<a href=\"RoleController.php.html#13\">App\\Http\\Controllers\\Web\\RoleController<\/a>"],[0,4,"<a href=\"RolePermissionController.php.html#13\">App\\Http\\Controllers\\Web\\RolePermissionController<\/a>"],[0,4,"<a href=\"StationPriceController.php.html#14\">App\\Http\\Controllers\\Web\\StationPriceController<\/a>"],[0,6,"<a href=\"StationPushConditionController.php.html#13\">App\\Http\\Controllers\\Web\\StationPushConditionController<\/a>"],[0,1,"<a href=\"StationPushRecordController.php.html#12\">App\\Http\\Controllers\\Web\\StationPushRecordController<\/a>"],[0,6,"<a href=\"StationPushSwitchController.php.html#14\">App\\Http\\Controllers\\Web\\StationPushSwitchController<\/a>"],[0,7,"<a href=\"SupplierController.php.html#13\">App\\Http\\Controllers\\Web\\SupplierController<\/a>"],[0,4,"<a href=\"ToolController.php.html#14\">App\\Http\\Controllers\\Web\\ToolController<\/a>"],[0,8,"<a href=\"UserController.php.html#13\">App\\Http\\Controllers\\Web\\UserController<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,8,"<a href=\"BasicController.php.html#20\">App\\Http\\Controllers\\Web\\BasicController::__construct<\/a>"],[0,6,"<a href=\"BasicController.php.html#96\">App\\Http\\Controllers\\Web\\BasicController::validateRequestParam<\/a>"],[0,1,"<a href=\"CommonController.php.html#35\">App\\Http\\Controllers\\Web\\CommonController::getThirdPartyPlatformList<\/a>"],[0,1,"<a href=\"CommonController.php.html#47\">App\\Http\\Controllers\\Web\\CommonController::getLogLevelList<\/a>"],[0,1,"<a href=\"CommonController.php.html#64\">App\\Http\\Controllers\\Web\\CommonController::getQueueLogTypeList<\/a>"],[0,1,"<a href=\"CommonController.php.html#83\">App\\Http\\Controllers\\Web\\CommonController::getStationSourceList<\/a>"],[0,1,"<a href=\"CommonController.php.html#99\">App\\Http\\Controllers\\Web\\CommonController::getCurrentDayLogCountByType<\/a>"],[0,2,"<a href=\"CommonController.php.html#112\">App\\Http\\Controllers\\Web\\CommonController::getTradeTypeList<\/a>"],[0,2,"<a href=\"CommonController.php.html#132\">App\\Http\\Controllers\\Web\\CommonController::getOilUnitList<\/a>"],[0,2,"<a href=\"CommonController.php.html#152\">App\\Http\\Controllers\\Web\\CommonController::getOrderRefundReason<\/a>"],[0,1,"<a href=\"ConfigController.php.html#54\">App\\Http\\Controllers\\Web\\ConfigController::index<\/a>"],[0,1,"<a href=\"ConfigController.php.html#67\">App\\Http\\Controllers\\Web\\ConfigController::getData<\/a>"],[0,1,"<a href=\"ConfigController.php.html#81\">App\\Http\\Controllers\\Web\\ConfigController::create<\/a>"],[0,1,"<a href=\"ConfigController.php.html#96\">App\\Http\\Controllers\\Web\\ConfigController::update<\/a>"],[0,1,"<a href=\"ConfigController.php.html#111\">App\\Http\\Controllers\\Web\\ConfigController::delete<\/a>"],[0,1,"<a href=\"DockingPlatformController.php.html#70\">App\\Http\\Controllers\\Web\\DockingPlatformController::index<\/a>"],[0,1,"<a href=\"DockingPlatformController.php.html#83\">App\\Http\\Controllers\\Web\\DockingPlatformController::getData<\/a>"],[0,1,"<a href=\"DockingPlatformController.php.html#97\">App\\Http\\Controllers\\Web\\DockingPlatformController::create<\/a>"],[0,1,"<a href=\"DockingPlatformController.php.html#112\">App\\Http\\Controllers\\Web\\DockingPlatformController::update<\/a>"],[0,1,"<a href=\"DockingPlatformController.php.html#127\">App\\Http\\Controllers\\Web\\DockingPlatformController::delete<\/a>"],[0,1,"<a href=\"DockingPlatformController.php.html#141\">App\\Http\\Controllers\\Web\\DockingPlatformController::getPlatformNameByNameAbbreviations<\/a>"],[0,1,"<a href=\"FailedJobsController.php.html#20\">App\\Http\\Controllers\\Web\\FailedJobsController::index<\/a>"],[0,1,"<a href=\"FailedJobsController.php.html#33\">App\\Http\\Controllers\\Web\\FailedJobsController::getData<\/a>"],[0,1,"<a href=\"HomeController.php.html#15\">App\\Http\\Controllers\\Web\\HomeController::index<\/a>"],[0,2,"<a href=\"LoginController.php.html#37\">App\\Http\\Controllers\\Web\\LoginController::index<\/a>"],[0,3,"<a href=\"LoginController.php.html#47\">App\\Http\\Controllers\\Web\\LoginController::getCaptcha<\/a>"],[0,1,"<a href=\"LoginController.php.html#89\">App\\Http\\Controllers\\Web\\LoginController::in<\/a>"],[0,1,"<a href=\"LoginController.php.html#103\">App\\Http\\Controllers\\Web\\LoginController::out<\/a>"],[0,1,"<a href=\"LoginController.php.html#117\">App\\Http\\Controllers\\Web\\LoginController::getLoginUserInfo<\/a>"],[0,1,"<a href=\"OrderAssocController.php.html#20\">App\\Http\\Controllers\\Web\\OrderAssocController::index<\/a>"],[0,1,"<a href=\"OrderAssocController.php.html#33\">App\\Http\\Controllers\\Web\\OrderAssocController::getData<\/a>"],[0,1,"<a href=\"OrderAssocController.php.html#39\">App\\Http\\Controllers\\Web\\OrderAssocController::getChart<\/a>"],[0,1,"<a href=\"OrderController.php.html#43\">App\\Http\\Controllers\\Web\\OrderController::refund<\/a>"],[0,1,"<a href=\"OrderController.php.html#58\">App\\Http\\Controllers\\Web\\OrderController::getRefundList<\/a>"],[0,1,"<a href=\"OrderController.php.html#72\">App\\Http\\Controllers\\Web\\OrderController::doRefund<\/a>"],[0,1,"<a href=\"OrderController.php.html#81\">App\\Http\\Controllers\\Web\\OrderController::retryFailedOrder<\/a>"],[0,1,"<a href=\"OrderController.php.html#94\">App\\Http\\Controllers\\Web\\OrderController::getSecondaryPaymentQrCode<\/a>"],[0,1,"<a href=\"PermissionController.php.html#62\">App\\Http\\Controllers\\Web\\PermissionController::getData<\/a>"],[0,1,"<a href=\"PermissionController.php.html#76\">App\\Http\\Controllers\\Web\\PermissionController::create<\/a>"],[0,1,"<a href=\"PermissionController.php.html#91\">App\\Http\\Controllers\\Web\\PermissionController::update<\/a>"],[0,1,"<a href=\"PermissionController.php.html#106\">App\\Http\\Controllers\\Web\\PermissionController::delete<\/a>"],[0,1,"<a href=\"PermissionController.php.html#121\">App\\Http\\Controllers\\Web\\PermissionController::getSelect<\/a>"],[0,1,"<a href=\"PermissionController.php.html#134\">App\\Http\\Controllers\\Web\\PermissionController::getTree<\/a>"],[0,1,"<a href=\"QueueLogController.php.html#21\">App\\Http\\Controllers\\Web\\QueueLogController::index<\/a>"],[0,1,"<a href=\"QueueLogController.php.html#34\">App\\Http\\Controllers\\Web\\QueueLogController::getData<\/a>"],[0,2,"<a href=\"QueueLogController.php.html#40\">App\\Http\\Controllers\\Web\\QueueLogController::getCurDayCount<\/a>"],[0,1,"<a href=\"ReceiveLogController.php.html#29\">App\\Http\\Controllers\\Web\\ReceiveLogController::index<\/a>"],[0,1,"<a href=\"ReceiveLogController.php.html#42\">App\\Http\\Controllers\\Web\\ReceiveLogController::getData<\/a>"],[0,2,"<a href=\"ReceiveLogController.php.html#48\">App\\Http\\Controllers\\Web\\ReceiveLogController::getCurDayCount<\/a>"],[0,1,"<a href=\"RequestLogController.php.html#21\">App\\Http\\Controllers\\Web\\RequestLogController::index<\/a>"],[0,1,"<a href=\"RequestLogController.php.html#34\">App\\Http\\Controllers\\Web\\RequestLogController::getData<\/a>"],[0,2,"<a href=\"RequestLogController.php.html#40\">App\\Http\\Controllers\\Web\\RequestLogController::getCurDayCount<\/a>"],[0,1,"<a href=\"ResponseLogController.php.html#29\">App\\Http\\Controllers\\Web\\ResponseLogController::index<\/a>"],[0,1,"<a href=\"ResponseLogController.php.html#42\">App\\Http\\Controllers\\Web\\ResponseLogController::getData<\/a>"],[0,2,"<a href=\"ResponseLogController.php.html#48\">App\\Http\\Controllers\\Web\\ResponseLogController::getCurDayCount<\/a>"],[0,1,"<a href=\"RoleController.php.html#55\">App\\Http\\Controllers\\Web\\RoleController::getData<\/a>"],[0,1,"<a href=\"RoleController.php.html#69\">App\\Http\\Controllers\\Web\\RoleController::getSelect<\/a>"],[0,1,"<a href=\"RoleController.php.html#83\">App\\Http\\Controllers\\Web\\RoleController::create<\/a>"],[0,1,"<a href=\"RoleController.php.html#98\">App\\Http\\Controllers\\Web\\RoleController::update<\/a>"],[0,1,"<a href=\"RoleController.php.html#113\">App\\Http\\Controllers\\Web\\RoleController::delete<\/a>"],[0,1,"<a href=\"RolePermissionController.php.html#61\">App\\Http\\Controllers\\Web\\RolePermissionController::getData<\/a>"],[0,1,"<a href=\"RolePermissionController.php.html#75\">App\\Http\\Controllers\\Web\\RolePermissionController::create<\/a>"],[0,1,"<a href=\"RolePermissionController.php.html#90\">App\\Http\\Controllers\\Web\\RolePermissionController::update<\/a>"],[0,1,"<a href=\"RolePermissionController.php.html#105\">App\\Http\\Controllers\\Web\\RolePermissionController::delete<\/a>"],[0,1,"<a href=\"StationPriceController.php.html#34\">App\\Http\\Controllers\\Web\\StationPriceController::index<\/a>"],[0,1,"<a href=\"StationPriceController.php.html#40\">App\\Http\\Controllers\\Web\\StationPriceController::getData<\/a>"],[0,1,"<a href=\"StationPriceController.php.html#54\">App\\Http\\Controllers\\Web\\StationPriceController::stationPull<\/a>"],[0,1,"<a href=\"StationPriceController.php.html#66\">App\\Http\\Controllers\\Web\\StationPriceController::getSelect<\/a>"],[0,1,"<a href=\"StationPushConditionController.php.html#59\">App\\Http\\Controllers\\Web\\StationPushConditionController::index<\/a>"],[0,1,"<a href=\"StationPushConditionController.php.html#72\">App\\Http\\Controllers\\Web\\StationPushConditionController::getData<\/a>"],[0,1,"<a href=\"StationPushConditionController.php.html#86\">App\\Http\\Controllers\\Web\\StationPushConditionController::stationPushAll<\/a>"],[0,1,"<a href=\"StationPushConditionController.php.html#100\">App\\Http\\Controllers\\Web\\StationPushConditionController::create<\/a>"],[0,1,"<a href=\"StationPushConditionController.php.html#115\">App\\Http\\Controllers\\Web\\StationPushConditionController::update<\/a>"],[0,1,"<a href=\"StationPushConditionController.php.html#130\">App\\Http\\Controllers\\Web\\StationPushConditionController::delete<\/a>"],[0,1,"<a href=\"StationPushRecordController.php.html#36\">App\\Http\\Controllers\\Web\\StationPushRecordController::getData<\/a>"],[0,1,"<a href=\"StationPushSwitchController.php.html#71\">App\\Http\\Controllers\\Web\\StationPushSwitchController::index<\/a>"],[0,1,"<a href=\"StationPushSwitchController.php.html#84\">App\\Http\\Controllers\\Web\\StationPushSwitchController::getData<\/a>"],[0,1,"<a href=\"StationPushSwitchController.php.html#98\">App\\Http\\Controllers\\Web\\StationPushSwitchController::create<\/a>"],[0,1,"<a href=\"StationPushSwitchController.php.html#113\">App\\Http\\Controllers\\Web\\StationPushSwitchController::stationPushAll<\/a>"],[0,1,"<a href=\"StationPushSwitchController.php.html#127\">App\\Http\\Controllers\\Web\\StationPushSwitchController::update<\/a>"],[0,1,"<a href=\"StationPushSwitchController.php.html#138\">App\\Http\\Controllers\\Web\\StationPushSwitchController::getPushedStationList<\/a>"],[0,1,"<a href=\"SupplierController.php.html#65\">App\\Http\\Controllers\\Web\\SupplierController::index<\/a>"],[0,1,"<a href=\"SupplierController.php.html#78\">App\\Http\\Controllers\\Web\\SupplierController::getData<\/a>"],[0,1,"<a href=\"SupplierController.php.html#91\">App\\Http\\Controllers\\Web\\SupplierController::getAllData<\/a>"],[0,1,"<a href=\"SupplierController.php.html#105\">App\\Http\\Controllers\\Web\\SupplierController::create<\/a>"],[0,1,"<a href=\"SupplierController.php.html#120\">App\\Http\\Controllers\\Web\\SupplierController::update<\/a>"],[0,1,"<a href=\"SupplierController.php.html#135\">App\\Http\\Controllers\\Web\\SupplierController::delete<\/a>"],[0,1,"<a href=\"SupplierController.php.html#150\">App\\Http\\Controllers\\Web\\SupplierController::getSupplierNameBySupplierCodes<\/a>"],[0,1,"<a href=\"ToolController.php.html#67\">App\\Http\\Controllers\\Web\\ToolController::queryDriverInfo<\/a>"],[0,1,"<a href=\"ToolController.php.html#81\">App\\Http\\Controllers\\Web\\ToolController::queryBillCheckResult<\/a>"],[0,2,"<a href=\"ToolController.php.html#95\">App\\Http\\Controllers\\Web\\ToolController::pushBill<\/a>"],[0,1,"<a href=\"UserController.php.html#99\">App\\Http\\Controllers\\Web\\UserController::index<\/a>"],[0,1,"<a href=\"UserController.php.html#112\">App\\Http\\Controllers\\Web\\UserController::getData<\/a>"],[0,2,"<a href=\"UserController.php.html#126\">App\\Http\\Controllers\\Web\\UserController::create<\/a>"],[0,2,"<a href=\"UserController.php.html#145\">App\\Http\\Controllers\\Web\\UserController::update<\/a>"],[0,1,"<a href=\"UserController.php.html#156\">App\\Http\\Controllers\\Web\\UserController::updateSecret<\/a>"],[0,1,"<a href=\"UserController.php.html#173\">App\\Http\\Controllers\\Web\\UserController::delete<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
