<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /Users/<USER>/PhpstormProjects/G7/oil-adapter/app/Http/Middleware/Callback</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../.css/bootstrap.min.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/nv.d3.min.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/style.css" rel="stylesheet" type="text/css">
  <link href="../../../.css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/Users/<USER>/PhpstormProjects/G7/oil-adapter/app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Middleware</a></li>
         <li class="breadcrumb-item"><a href="index.html">Callback</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ADAuthenticate.php.html#17">App\Http\Middleware\Callback\ADAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XYNAuthenticate.php.html#16">App\Http\Middleware\Callback\XYNAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHSXAuthenticate.php.html#15">App\Http\Middleware\Callback\SHSXAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZLAuthenticate.php.html#15">App\Http\Middleware\Callback\SQZLAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZSHAuthenticate.php.html#15">App\Http\Middleware\Callback\SQZSHAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TBJXAuthenticate.php.html#14">App\Http\Middleware\Callback\TBJXAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WJYAuthenticate.php.html#16">App\Http\Middleware\Callback\WJYAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XMSKAuthenticate.php.html#17">App\Http\Middleware\Callback\XMSKAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YCQPAuthenticate.php.html#14">App\Http\Middleware\Callback\YCQPAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SCQPAuthenticate.php.html#15">App\Http\Middleware\Callback\SCQPAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGYAuthenticate.php.html#16">App\Http\Middleware\Callback\YGYAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YUNDATONGAuthenticate.php.html#16">App\Http\Middleware\Callback\YUNDATONGAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZDCAuthenticate.php.html#16">App\Http\Middleware\Callback\ZDCAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIQAuthenticate.php.html#15">App\Http\Middleware\Callback\ZHUOYIQAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIYAuthenticate.php.html#15">App\Http\Middleware\Callback\ZHUOYIYAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHYKAuthenticate.php.html#16">App\Http\Middleware\Callback\ZHYKAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHAuthenticate.php.html#15">App\Http\Middleware\Callback\SHAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAICSTRADEAuthenticate.php.html#16">App\Http\Middleware\Callback\SAICSTRADEAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CNPCAuthenticate.php.html#16">App\Http\Middleware\Callback\CNPCAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HBKJAuthenticate.php.html#17">App\Http\Middleware\Callback\HBKJAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DHAuthenticate.php.html#16">App\Http\Middleware\Callback\DHAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTAuthenticate.php.html#16">App\Http\Middleware\Callback\DTAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GAODENGAuthenticate.php.html#15">App\Http\Middleware\Callback\GAODENGAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBAuthenticate.php.html#15">App\Http\Middleware\Callback\GBAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBTJAuthenticate.php.html#15">App\Http\Middleware\Callback\GBTJAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GDQPAuthenticate.php.html#15">App\Http\Middleware\Callback\GDQPAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSYAuthenticate.php.html#15">App\Http\Middleware\Callback\HSYAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAICSTATIONAuthenticate.php.html#16">App\Http\Middleware\Callback\SAICSTATIONAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JHAuthenticate.php.html#14">App\Http\Middleware\Callback\JHAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JHCXAuthenticate.php.html#16">App\Http\Middleware\Callback\JHCXAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JQAuthenticate.php.html#16">App\Http\Middleware\Callback\JQAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTAuthenticate.php.html#15">App\Http\Middleware\Callback\JTAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTXAuthenticate.php.html#14">App\Http\Middleware\Callback\JTXAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MTLSYAuthenticate.php.html#14">App\Http\Middleware\Callback\MTLSYAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QKAuthenticate.php.html#14">App\Http\Middleware\Callback\QKAuthenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZWLAuthenticate.php.html#14">App\Http\Middleware\Callback\ZWLAuthenticate</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DTAuthenticate.php.html#16">App\Http\Middleware\Callback\DTAuthenticate</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="HSYAuthenticate.php.html#15">App\Http\Middleware\Callback\HSYAuthenticate</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ZDCAuthenticate.php.html#16">App\Http\Middleware\Callback\ZDCAuthenticate</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="SAICSTATIONAuthenticate.php.html#16">App\Http\Middleware\Callback\SAICSTATIONAuthenticate</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="JQAuthenticate.php.html#16">App\Http\Middleware\Callback\JQAuthenticate</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="SAICSTRADEAuthenticate.php.html#16">App\Http\Middleware\Callback\SAICSTRADEAuthenticate</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="CNPCAuthenticate.php.html#16">App\Http\Middleware\Callback\CNPCAuthenticate</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ZHYKAuthenticate.php.html#16">App\Http\Middleware\Callback\ZHYKAuthenticate</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ZHUOYIQAuthenticate.php.html#15">App\Http\Middleware\Callback\ZHUOYIQAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="YUNDATONGAuthenticate.php.html#16">App\Http\Middleware\Callback\YUNDATONGAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="YGYAuthenticate.php.html#16">App\Http\Middleware\Callback\YGYAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SHAuthenticate.php.html#15">App\Http\Middleware\Callback\SHAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="YCQPAuthenticate.php.html#14">App\Http\Middleware\Callback\YCQPAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="XYNAuthenticate.php.html#16">App\Http\Middleware\Callback\XYNAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ZHUOYIYAuthenticate.php.html#15">App\Http\Middleware\Callback\ZHUOYIYAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WJYAuthenticate.php.html#16">App\Http\Middleware\Callback\WJYAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SQZSHAuthenticate.php.html#15">App\Http\Middleware\Callback\SQZSHAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SQZLAuthenticate.php.html#15">App\Http\Middleware\Callback\SQZLAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SHSXAuthenticate.php.html#15">App\Http\Middleware\Callback\SHSXAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ADAuthenticate.php.html#17">App\Http\Middleware\Callback\ADAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SCQPAuthenticate.php.html#15">App\Http\Middleware\Callback\SCQPAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="QKAuthenticate.php.html#14">App\Http\Middleware\Callback\QKAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="JTXAuthenticate.php.html#14">App\Http\Middleware\Callback\JTXAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="JTAuthenticate.php.html#15">App\Http\Middleware\Callback\JTAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="JHCXAuthenticate.php.html#16">App\Http\Middleware\Callback\JHCXAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="JHAuthenticate.php.html#14">App\Http\Middleware\Callback\JHAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="HBKJAuthenticate.php.html#17">App\Http\Middleware\Callback\HBKJAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="GDQPAuthenticate.php.html#15">App\Http\Middleware\Callback\GDQPAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="GBTJAuthenticate.php.html#15">App\Http\Middleware\Callback\GBTJAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="GBAuthenticate.php.html#15">App\Http\Middleware\Callback\GBAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="GAODENGAuthenticate.php.html#15">App\Http\Middleware\Callback\GAODENGAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="DHAuthenticate.php.html#16">App\Http\Middleware\Callback\DHAuthenticate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="MTLSYAuthenticate.php.html#14">App\Http\Middleware\Callback\MTLSYAuthenticate</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="TBJXAuthenticate.php.html#14">App\Http\Middleware\Callback\TBJXAuthenticate</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="XMSKAuthenticate.php.html#17">App\Http\Middleware\Callback\XMSKAuthenticate</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZWLAuthenticate.php.html#14">App\Http\Middleware\Callback\ZWLAuthenticate</a></td><td class="text-right">30</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ADAuthenticate.php.html#31"><abbr title="App\Http\Middleware\Callback\ADAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZLAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\SQZLAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WJYAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\WJYAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WJYAuthenticate.php.html#30"><abbr title="App\Http\Middleware\Callback\WJYAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TBJXAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\TBJXAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TBJXAuthenticate.php.html#28"><abbr title="App\Http\Middleware\Callback\TBJXAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZSHAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\SQZSHAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZSHAuthenticate.php.html#29"><abbr title="App\Http\Middleware\Callback\SQZSHAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SQZLAuthenticate.php.html#29"><abbr title="App\Http\Middleware\Callback\SQZLAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XMSKAuthenticate.php.html#43"><abbr title="App\Http\Middleware\Callback\XMSKAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHSXAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\SHSXAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHSXAuthenticate.php.html#29"><abbr title="App\Http\Middleware\Callback\SHSXAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\SHAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SHAuthenticate.php.html#29"><abbr title="App\Http\Middleware\Callback\SHAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SCQPAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\SCQPAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SCQPAuthenticate.php.html#29"><abbr title="App\Http\Middleware\Callback\SCQPAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAICSTRADEAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\SAICSTRADEAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XMSKAuthenticate.php.html#31"><abbr title="App\Http\Middleware\Callback\XMSKAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XYNAuthenticate.php.html#30"><abbr title="App\Http\Middleware\Callback\XYNAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAICSTATIONAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\SAICSTATIONAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIQAuthenticate.php.html#29"><abbr title="App\Http\Middleware\Callback\ZHUOYIQAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZWLAuthenticate.php.html#28"><abbr title="App\Http\Middleware\Callback\ZWLAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHYKAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\ZHYKAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHYKAuthenticate.php.html#30"><abbr title="App\Http\Middleware\Callback\ZHYKAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIYAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\ZHUOYIYAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIYAuthenticate.php.html#29"><abbr title="App\Http\Middleware\Callback\ZHUOYIYAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZHUOYIQAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\ZHUOYIQAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZDCAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\ZDCAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="XYNAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\XYNAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZDCAuthenticate.php.html#30"><abbr title="App\Http\Middleware\Callback\ZDCAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YUNDATONGAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\YUNDATONGAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YUNDATONGAuthenticate.php.html#30"><abbr title="App\Http\Middleware\Callback\YUNDATONGAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGYAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\YGYAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YGYAuthenticate.php.html#30"><abbr title="App\Http\Middleware\Callback\YGYAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YCQPAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\YCQPAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YCQPAuthenticate.php.html#28"><abbr title="App\Http\Middleware\Callback\YCQPAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAICSTRADEAuthenticate.php.html#30"><abbr title="App\Http\Middleware\Callback\SAICSTRADEAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SAICSTATIONAuthenticate.php.html#30"><abbr title="App\Http\Middleware\Callback\SAICSTATIONAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ADAuthenticate.php.html#43"><abbr title="App\Http\Middleware\Callback\ADAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GAODENGAuthenticate.php.html#29"><abbr title="App\Http\Middleware\Callback\GAODENGAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GDQPAuthenticate.php.html#29"><abbr title="App\Http\Middleware\Callback\GDQPAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBTJAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\GBTJAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBTJAuthenticate.php.html#29"><abbr title="App\Http\Middleware\Callback\GBTJAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\GBAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GBAuthenticate.php.html#29"><abbr title="App\Http\Middleware\Callback\GBAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GAODENGAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\GAODENGAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTAuthenticate.php.html#89"><abbr title="App\Http\Middleware\Callback\DTAuthenticate::verifySign">verifySign</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HBKJAuthenticate.php.html#31"><abbr title="App\Http\Middleware\Callback\HBKJAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\DTAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTAuthenticate.php.html#30"><abbr title="App\Http\Middleware\Callback\DTAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DHAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\DHAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DHAuthenticate.php.html#30"><abbr title="App\Http\Middleware\Callback\DHAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CNPCAuthenticate.php.html#105"><abbr title="App\Http\Middleware\Callback\CNPCAuthenticate::sign">sign</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CNPCAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\CNPCAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CNPCAuthenticate.php.html#30"><abbr title="App\Http\Middleware\Callback\CNPCAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GDQPAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\GDQPAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HBKJAuthenticate.php.html#42"><abbr title="App\Http\Middleware\Callback\HBKJAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QKAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\QKAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTAuthenticate.php.html#29"><abbr title="App\Http\Middleware\Callback\JTAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QKAuthenticate.php.html#28"><abbr title="App\Http\Middleware\Callback\QKAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MTLSYAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\MTLSYAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MTLSYAuthenticate.php.html#28"><abbr title="App\Http\Middleware\Callback\MTLSYAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTXAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\JTXAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTXAuthenticate.php.html#28"><abbr title="App\Http\Middleware\Callback\JTXAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JTAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\JTAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JQAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\JQAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSYAuthenticate.php.html#29"><abbr title="App\Http\Middleware\Callback\HSYAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JQAuthenticate.php.html#30"><abbr title="App\Http\Middleware\Callback\JQAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JHCXAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\JHCXAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JHCXAuthenticate.php.html#30"><abbr title="App\Http\Middleware\Callback\JHCXAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JHAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\JHAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JHAuthenticate.php.html#28"><abbr title="App\Http\Middleware\Callback\JHAuthenticate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSYAuthenticate.php.html#97"><abbr title="App\Http\Middleware\Callback\HSYAuthenticate::sign">sign</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HSYAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\HSYAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZWLAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\ZWLAuthenticate::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ZDCAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\ZDCAuthenticate::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="JQAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\JQAuthenticate::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="SAICSTATIONAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\SAICSTATIONAuthenticate::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="SAICSTRADEAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\SAICSTRADEAuthenticate::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ZHYKAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\ZHYKAuthenticate::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="DTAuthenticate.php.html#89"><abbr title="App\Http\Middleware\Callback\DTAuthenticate::verifySign">verifySign</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SQZSHAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\SQZSHAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SCQPAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\SCQPAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SHAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\SHAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SHSXAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\SHSXAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SQZLAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\SQZLAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ADAuthenticate.php.html#43"><abbr title="App\Http\Middleware\Callback\ADAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WJYAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\WJYAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="XYNAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\XYNAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="YGYAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\YGYAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="YUNDATONGAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\YUNDATONGAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZHUOYIQAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\ZHUOYIQAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZHUOYIYAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\ZHUOYIYAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="YCQPAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\YCQPAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="QKAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\QKAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="CNPCAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\CNPCAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="GAODENGAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\GAODENGAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="JTXAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\JTXAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="JTAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\JTAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="JHCXAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\JHCXAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="JHAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\JHAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="HSYAuthenticate.php.html#97"><abbr title="App\Http\Middleware\Callback\HSYAuthenticate::sign">sign</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="DHAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\DHAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="HSYAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\HSYAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="DTAuthenticate.php.html#41"><abbr title="App\Http\Middleware\Callback\DTAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="HBKJAuthenticate.php.html#42"><abbr title="App\Http\Middleware\Callback\HBKJAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="GDQPAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\GDQPAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="GBTJAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\GBTJAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="GBAuthenticate.php.html#40"><abbr title="App\Http\Middleware\Callback\GBAuthenticate::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="XMSKAuthenticate.php.html#43"><abbr title="App\Http\Middleware\Callback\XMSKAuthenticate::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="MTLSYAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\MTLSYAuthenticate::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TBJXAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\TBJXAuthenticate::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ZWLAuthenticate.php.html#39"><abbr title="App\Http\Middleware\Callback\ZWLAuthenticate::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="CNPCAuthenticate.php.html#105"><abbr title="App\Http\Middleware\Callback\CNPCAuthenticate::sign">sign</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 6.1.4</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> with <a href="https://xdebug.org/">Xdebug 3.1.6</a> and <a href="https://phpunit.de/">PHPUnit 7.5.20</a> at Thu Jul 31 17:44:39 CST 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../.js/jquery.min.js" type="text/javascript"></script>
  <script src="../../../.js/d3.min.js" type="text/javascript"></script>
  <script src="../../../.js/nv.d3.min.js" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([36,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([75,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,6,"<a href=\"ADAuthenticate.php.html#17\">App\\Http\\Middleware\\Callback\\ADAuthenticate<\/a>"],[0,8,"<a href=\"CNPCAuthenticate.php.html#16\">App\\Http\\Middleware\\Callback\\CNPCAuthenticate<\/a>"],[0,6,"<a href=\"DHAuthenticate.php.html#16\">App\\Http\\Middleware\\Callback\\DHAuthenticate<\/a>"],[0,12,"<a href=\"DTAuthenticate.php.html#16\">App\\Http\\Middleware\\Callback\\DTAuthenticate<\/a>"],[0,6,"<a href=\"GAODENGAuthenticate.php.html#15\">App\\Http\\Middleware\\Callback\\GAODENGAuthenticate<\/a>"],[0,6,"<a href=\"GBAuthenticate.php.html#15\">App\\Http\\Middleware\\Callback\\GBAuthenticate<\/a>"],[0,6,"<a href=\"GBTJAuthenticate.php.html#15\">App\\Http\\Middleware\\Callback\\GBTJAuthenticate<\/a>"],[0,6,"<a href=\"GDQPAuthenticate.php.html#15\">App\\Http\\Middleware\\Callback\\GDQPAuthenticate<\/a>"],[0,6,"<a href=\"HBKJAuthenticate.php.html#17\">App\\Http\\Middleware\\Callback\\HBKJAuthenticate<\/a>"],[0,11,"<a href=\"HSYAuthenticate.php.html#15\">App\\Http\\Middleware\\Callback\\HSYAuthenticate<\/a>"],[0,6,"<a href=\"JHAuthenticate.php.html#14\">App\\Http\\Middleware\\Callback\\JHAuthenticate<\/a>"],[0,6,"<a href=\"JHCXAuthenticate.php.html#16\">App\\Http\\Middleware\\Callback\\JHCXAuthenticate<\/a>"],[0,9,"<a href=\"JQAuthenticate.php.html#16\">App\\Http\\Middleware\\Callback\\JQAuthenticate<\/a>"],[0,6,"<a href=\"JTAuthenticate.php.html#15\">App\\Http\\Middleware\\Callback\\JTAuthenticate<\/a>"],[0,6,"<a href=\"JTXAuthenticate.php.html#14\">App\\Http\\Middleware\\Callback\\JTXAuthenticate<\/a>"],[0,5,"<a href=\"MTLSYAuthenticate.php.html#14\">App\\Http\\Middleware\\Callback\\MTLSYAuthenticate<\/a>"],[0,6,"<a href=\"QKAuthenticate.php.html#14\">App\\Http\\Middleware\\Callback\\QKAuthenticate<\/a>"],[0,9,"<a href=\"SAICSTATIONAuthenticate.php.html#16\">App\\Http\\Middleware\\Callback\\SAICSTATIONAuthenticate<\/a>"],[0,8,"<a href=\"SAICSTRADEAuthenticate.php.html#16\">App\\Http\\Middleware\\Callback\\SAICSTRADEAuthenticate<\/a>"],[0,6,"<a href=\"SCQPAuthenticate.php.html#15\">App\\Http\\Middleware\\Callback\\SCQPAuthenticate<\/a>"],[0,6,"<a href=\"SHAuthenticate.php.html#15\">App\\Http\\Middleware\\Callback\\SHAuthenticate<\/a>"],[0,6,"<a href=\"SHSXAuthenticate.php.html#15\">App\\Http\\Middleware\\Callback\\SHSXAuthenticate<\/a>"],[0,6,"<a href=\"SQZLAuthenticate.php.html#15\">App\\Http\\Middleware\\Callback\\SQZLAuthenticate<\/a>"],[0,6,"<a href=\"SQZSHAuthenticate.php.html#15\">App\\Http\\Middleware\\Callback\\SQZSHAuthenticate<\/a>"],[0,5,"<a href=\"TBJXAuthenticate.php.html#14\">App\\Http\\Middleware\\Callback\\TBJXAuthenticate<\/a>"],[0,6,"<a href=\"WJYAuthenticate.php.html#16\">App\\Http\\Middleware\\Callback\\WJYAuthenticate<\/a>"],[0,5,"<a href=\"XMSKAuthenticate.php.html#17\">App\\Http\\Middleware\\Callback\\XMSKAuthenticate<\/a>"],[0,6,"<a href=\"XYNAuthenticate.php.html#16\">App\\Http\\Middleware\\Callback\\XYNAuthenticate<\/a>"],[0,6,"<a href=\"YCQPAuthenticate.php.html#14\">App\\Http\\Middleware\\Callback\\YCQPAuthenticate<\/a>"],[0,6,"<a href=\"YGYAuthenticate.php.html#16\">App\\Http\\Middleware\\Callback\\YGYAuthenticate<\/a>"],[0,6,"<a href=\"YUNDATONGAuthenticate.php.html#16\">App\\Http\\Middleware\\Callback\\YUNDATONGAuthenticate<\/a>"],[0,9,"<a href=\"ZDCAuthenticate.php.html#16\">App\\Http\\Middleware\\Callback\\ZDCAuthenticate<\/a>"],[0,6,"<a href=\"ZHUOYIQAuthenticate.php.html#15\">App\\Http\\Middleware\\Callback\\ZHUOYIQAuthenticate<\/a>"],[0,6,"<a href=\"ZHUOYIYAuthenticate.php.html#15\">App\\Http\\Middleware\\Callback\\ZHUOYIYAuthenticate<\/a>"],[0,7,"<a href=\"ZHYKAuthenticate.php.html#16\">App\\Http\\Middleware\\Callback\\ZHYKAuthenticate<\/a>"],[0,5,"<a href=\"ZWLAuthenticate.php.html#14\">App\\Http\\Middleware\\Callback\\ZWLAuthenticate<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ADAuthenticate.php.html#31\">App\\Http\\Middleware\\Callback\\ADAuthenticate::__construct<\/a>"],[0,5,"<a href=\"ADAuthenticate.php.html#43\">App\\Http\\Middleware\\Callback\\ADAuthenticate::handle<\/a>"],[0,1,"<a href=\"CNPCAuthenticate.php.html#30\">App\\Http\\Middleware\\Callback\\CNPCAuthenticate::__construct<\/a>"],[0,5,"<a href=\"CNPCAuthenticate.php.html#41\">App\\Http\\Middleware\\Callback\\CNPCAuthenticate::handle<\/a>"],[0,2,"<a href=\"CNPCAuthenticate.php.html#105\">App\\Http\\Middleware\\Callback\\CNPCAuthenticate::sign<\/a>"],[0,1,"<a href=\"DHAuthenticate.php.html#30\">App\\Http\\Middleware\\Callback\\DHAuthenticate::__construct<\/a>"],[0,5,"<a href=\"DHAuthenticate.php.html#41\">App\\Http\\Middleware\\Callback\\DHAuthenticate::handle<\/a>"],[0,1,"<a href=\"DTAuthenticate.php.html#30\">App\\Http\\Middleware\\Callback\\DTAuthenticate::__construct<\/a>"],[0,5,"<a href=\"DTAuthenticate.php.html#41\">App\\Http\\Middleware\\Callback\\DTAuthenticate::handle<\/a>"],[0,6,"<a href=\"DTAuthenticate.php.html#89\">App\\Http\\Middleware\\Callback\\DTAuthenticate::verifySign<\/a>"],[0,1,"<a href=\"GAODENGAuthenticate.php.html#29\">App\\Http\\Middleware\\Callback\\GAODENGAuthenticate::__construct<\/a>"],[0,5,"<a href=\"GAODENGAuthenticate.php.html#40\">App\\Http\\Middleware\\Callback\\GAODENGAuthenticate::handle<\/a>"],[0,1,"<a href=\"GBAuthenticate.php.html#29\">App\\Http\\Middleware\\Callback\\GBAuthenticate::__construct<\/a>"],[0,5,"<a href=\"GBAuthenticate.php.html#40\">App\\Http\\Middleware\\Callback\\GBAuthenticate::handle<\/a>"],[0,1,"<a href=\"GBTJAuthenticate.php.html#29\">App\\Http\\Middleware\\Callback\\GBTJAuthenticate::__construct<\/a>"],[0,5,"<a href=\"GBTJAuthenticate.php.html#40\">App\\Http\\Middleware\\Callback\\GBTJAuthenticate::handle<\/a>"],[0,1,"<a href=\"GDQPAuthenticate.php.html#29\">App\\Http\\Middleware\\Callback\\GDQPAuthenticate::__construct<\/a>"],[0,5,"<a href=\"GDQPAuthenticate.php.html#40\">App\\Http\\Middleware\\Callback\\GDQPAuthenticate::handle<\/a>"],[0,1,"<a href=\"HBKJAuthenticate.php.html#31\">App\\Http\\Middleware\\Callback\\HBKJAuthenticate::__construct<\/a>"],[0,5,"<a href=\"HBKJAuthenticate.php.html#42\">App\\Http\\Middleware\\Callback\\HBKJAuthenticate::handle<\/a>"],[0,1,"<a href=\"HSYAuthenticate.php.html#29\">App\\Http\\Middleware\\Callback\\HSYAuthenticate::__construct<\/a>"],[0,5,"<a href=\"HSYAuthenticate.php.html#40\">App\\Http\\Middleware\\Callback\\HSYAuthenticate::handle<\/a>"],[0,5,"<a href=\"HSYAuthenticate.php.html#97\">App\\Http\\Middleware\\Callback\\HSYAuthenticate::sign<\/a>"],[0,1,"<a href=\"JHAuthenticate.php.html#28\">App\\Http\\Middleware\\Callback\\JHAuthenticate::__construct<\/a>"],[0,5,"<a href=\"JHAuthenticate.php.html#39\">App\\Http\\Middleware\\Callback\\JHAuthenticate::handle<\/a>"],[0,1,"<a href=\"JHCXAuthenticate.php.html#30\">App\\Http\\Middleware\\Callback\\JHCXAuthenticate::__construct<\/a>"],[0,5,"<a href=\"JHCXAuthenticate.php.html#41\">App\\Http\\Middleware\\Callback\\JHCXAuthenticate::handle<\/a>"],[0,1,"<a href=\"JQAuthenticate.php.html#30\">App\\Http\\Middleware\\Callback\\JQAuthenticate::__construct<\/a>"],[0,8,"<a href=\"JQAuthenticate.php.html#41\">App\\Http\\Middleware\\Callback\\JQAuthenticate::handle<\/a>"],[0,1,"<a href=\"JTAuthenticate.php.html#29\">App\\Http\\Middleware\\Callback\\JTAuthenticate::__construct<\/a>"],[0,5,"<a href=\"JTAuthenticate.php.html#40\">App\\Http\\Middleware\\Callback\\JTAuthenticate::handle<\/a>"],[0,1,"<a href=\"JTXAuthenticate.php.html#28\">App\\Http\\Middleware\\Callback\\JTXAuthenticate::__construct<\/a>"],[0,5,"<a href=\"JTXAuthenticate.php.html#39\">App\\Http\\Middleware\\Callback\\JTXAuthenticate::handle<\/a>"],[0,1,"<a href=\"MTLSYAuthenticate.php.html#28\">App\\Http\\Middleware\\Callback\\MTLSYAuthenticate::__construct<\/a>"],[0,4,"<a href=\"MTLSYAuthenticate.php.html#39\">App\\Http\\Middleware\\Callback\\MTLSYAuthenticate::handle<\/a>"],[0,1,"<a href=\"QKAuthenticate.php.html#28\">App\\Http\\Middleware\\Callback\\QKAuthenticate::__construct<\/a>"],[0,5,"<a href=\"QKAuthenticate.php.html#39\">App\\Http\\Middleware\\Callback\\QKAuthenticate::handle<\/a>"],[0,1,"<a href=\"SAICSTATIONAuthenticate.php.html#30\">App\\Http\\Middleware\\Callback\\SAICSTATIONAuthenticate::__construct<\/a>"],[0,8,"<a href=\"SAICSTATIONAuthenticate.php.html#41\">App\\Http\\Middleware\\Callback\\SAICSTATIONAuthenticate::handle<\/a>"],[0,1,"<a href=\"SAICSTRADEAuthenticate.php.html#30\">App\\Http\\Middleware\\Callback\\SAICSTRADEAuthenticate::__construct<\/a>"],[0,7,"<a href=\"SAICSTRADEAuthenticate.php.html#41\">App\\Http\\Middleware\\Callback\\SAICSTRADEAuthenticate::handle<\/a>"],[0,1,"<a href=\"SCQPAuthenticate.php.html#29\">App\\Http\\Middleware\\Callback\\SCQPAuthenticate::__construct<\/a>"],[0,5,"<a href=\"SCQPAuthenticate.php.html#40\">App\\Http\\Middleware\\Callback\\SCQPAuthenticate::handle<\/a>"],[0,1,"<a href=\"SHAuthenticate.php.html#29\">App\\Http\\Middleware\\Callback\\SHAuthenticate::__construct<\/a>"],[0,5,"<a href=\"SHAuthenticate.php.html#40\">App\\Http\\Middleware\\Callback\\SHAuthenticate::handle<\/a>"],[0,1,"<a href=\"SHSXAuthenticate.php.html#29\">App\\Http\\Middleware\\Callback\\SHSXAuthenticate::__construct<\/a>"],[0,5,"<a href=\"SHSXAuthenticate.php.html#40\">App\\Http\\Middleware\\Callback\\SHSXAuthenticate::handle<\/a>"],[0,1,"<a href=\"SQZLAuthenticate.php.html#29\">App\\Http\\Middleware\\Callback\\SQZLAuthenticate::__construct<\/a>"],[0,5,"<a href=\"SQZLAuthenticate.php.html#40\">App\\Http\\Middleware\\Callback\\SQZLAuthenticate::handle<\/a>"],[0,1,"<a href=\"SQZSHAuthenticate.php.html#29\">App\\Http\\Middleware\\Callback\\SQZSHAuthenticate::__construct<\/a>"],[0,5,"<a href=\"SQZSHAuthenticate.php.html#40\">App\\Http\\Middleware\\Callback\\SQZSHAuthenticate::handle<\/a>"],[0,1,"<a href=\"TBJXAuthenticate.php.html#28\">App\\Http\\Middleware\\Callback\\TBJXAuthenticate::__construct<\/a>"],[0,4,"<a href=\"TBJXAuthenticate.php.html#39\">App\\Http\\Middleware\\Callback\\TBJXAuthenticate::handle<\/a>"],[0,1,"<a href=\"WJYAuthenticate.php.html#30\">App\\Http\\Middleware\\Callback\\WJYAuthenticate::__construct<\/a>"],[0,5,"<a href=\"WJYAuthenticate.php.html#41\">App\\Http\\Middleware\\Callback\\WJYAuthenticate::handle<\/a>"],[0,1,"<a href=\"XMSKAuthenticate.php.html#31\">App\\Http\\Middleware\\Callback\\XMSKAuthenticate::__construct<\/a>"],[0,4,"<a href=\"XMSKAuthenticate.php.html#43\">App\\Http\\Middleware\\Callback\\XMSKAuthenticate::handle<\/a>"],[0,1,"<a href=\"XYNAuthenticate.php.html#30\">App\\Http\\Middleware\\Callback\\XYNAuthenticate::__construct<\/a>"],[0,5,"<a href=\"XYNAuthenticate.php.html#41\">App\\Http\\Middleware\\Callback\\XYNAuthenticate::handle<\/a>"],[0,1,"<a href=\"YCQPAuthenticate.php.html#28\">App\\Http\\Middleware\\Callback\\YCQPAuthenticate::__construct<\/a>"],[0,5,"<a href=\"YCQPAuthenticate.php.html#39\">App\\Http\\Middleware\\Callback\\YCQPAuthenticate::handle<\/a>"],[0,1,"<a href=\"YGYAuthenticate.php.html#30\">App\\Http\\Middleware\\Callback\\YGYAuthenticate::__construct<\/a>"],[0,5,"<a href=\"YGYAuthenticate.php.html#41\">App\\Http\\Middleware\\Callback\\YGYAuthenticate::handle<\/a>"],[0,1,"<a href=\"YUNDATONGAuthenticate.php.html#30\">App\\Http\\Middleware\\Callback\\YUNDATONGAuthenticate::__construct<\/a>"],[0,5,"<a href=\"YUNDATONGAuthenticate.php.html#41\">App\\Http\\Middleware\\Callback\\YUNDATONGAuthenticate::handle<\/a>"],[0,1,"<a href=\"ZDCAuthenticate.php.html#30\">App\\Http\\Middleware\\Callback\\ZDCAuthenticate::__construct<\/a>"],[0,8,"<a href=\"ZDCAuthenticate.php.html#41\">App\\Http\\Middleware\\Callback\\ZDCAuthenticate::handle<\/a>"],[0,1,"<a href=\"ZHUOYIQAuthenticate.php.html#29\">App\\Http\\Middleware\\Callback\\ZHUOYIQAuthenticate::__construct<\/a>"],[0,5,"<a href=\"ZHUOYIQAuthenticate.php.html#40\">App\\Http\\Middleware\\Callback\\ZHUOYIQAuthenticate::handle<\/a>"],[0,1,"<a href=\"ZHUOYIYAuthenticate.php.html#29\">App\\Http\\Middleware\\Callback\\ZHUOYIYAuthenticate::__construct<\/a>"],[0,5,"<a href=\"ZHUOYIYAuthenticate.php.html#40\">App\\Http\\Middleware\\Callback\\ZHUOYIYAuthenticate::handle<\/a>"],[0,1,"<a href=\"ZHYKAuthenticate.php.html#30\">App\\Http\\Middleware\\Callback\\ZHYKAuthenticate::__construct<\/a>"],[0,6,"<a href=\"ZHYKAuthenticate.php.html#41\">App\\Http\\Middleware\\Callback\\ZHYKAuthenticate::handle<\/a>"],[0,1,"<a href=\"ZWLAuthenticate.php.html#28\">App\\Http\\Middleware\\Callback\\ZWLAuthenticate::__construct<\/a>"],[0,4,"<a href=\"ZWLAuthenticate.php.html#39\">App\\Http\\Middleware\\Callback\\ZWLAuthenticate::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
