<?php

namespace Unit\Commands;

use App\Console\Commands\PullOilForCy;
use Illuminate\Console\Command;
use Mockery;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

class PullOilForCyTest extends TestCase
{
    protected $pullOilForCy;

    protected $stationPriceDataMock;

    protected function setUp(): void
    {
        parent::setUp();
        $this->stationPriceDataMock = Mockery::mock('alias:App\Models\Data\StationPrice');
        $this->stationPriceDataMock->shouldReceive('create')
                             ->once()
                             ->andReturn(true);
        // 创建PullOilForCy实例
        $this->pullOilForCy = new PullOilForCy();
        
        // 使用反射设置受保护属性
        $reflection = new ReflectionClass($this->pullOilForCy);
        
        $nameAbbreviationProperty = $reflection->getProperty('nameAbbreviation');
        $nameAbbreviationProperty->setAccessible(true);
        $nameAbbreviationProperty->setValue($this->pullOilForCy, 'cy');
        
        $platformCodeProperty = $reflection->getProperty('platformCode');
        $platformCodeProperty->setAccessible(true);
        $platformCodeProperty->setValue($this->pullOilForCy, 'CY_PLATFORM_CODE');
    }

    /**
     * @covers \App\Console\Commands\PullOilForCy::__construct
     */
    public function testConstruct()
    {
        $reflection = new ReflectionClass($this->pullOilForCy);
        $property = $reflection->getProperty('nameAbbreviation');
        $property->setAccessible(true);
        $abbreviation = $property->getValue($this->pullOilForCy);
        
        $this->assertInstanceOf(Command::class, $this->pullOilForCy);
        $this->assertEquals('cy', $abbreviation);
    }

    /**
     * @covers \App\Console\Commands\PullOilForCy::handle
     */
    public function testHandleWithSuccessResponse()
    {
        // Mock CYRequest using alias
        $cyRequestMock = Mockery::mock('overload:Request\CY');
        $cyRequestMock->shouldReceive('handle')
            ->with("/thirdApi/station/list", Mockery::any())
            ->andReturn([
                "data" => [
                    "count" => 1,
                    "list" => [
                        [
                            "id" => "test_station_1",
                            "station_name" => "Test Station 1",
                            "province_code" => "110000",
                            "city_code" => "110100",
                            "station_type" => 1,
                            "trade_type" => 1,
                            "is_highway" => 0,
                            "contact_phone" => "12345678901",
                            "station_brand_name" => "中石油",
                            "oil_uint" => "升",
                            "beginTime" => "08:00",
                            "endTime" => "20:00",
                            "lng" => "116.12345678",
                            "lat" => "39.12345678",
                            "address" => "Test Address",
                            "is_stop" => 0,
                            "price_list" => [
                                [
                                    "oil_number" => "oil_92",
                                    "price" => "6.50",
                                    "gun_price" => "6.60",
                                    "gun_numbers" => ["1", "2"]
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
        
        // Mock config function
        if (!function_exists('config')) {
            function config($key) {
                $configValues = [
                    "oil.oil_mapping.cy.oil_92" => "汽油_92_V",
                    "oil.oil_type" => ["汽油" => "gasoline"],
                    "oil.oil_no" => ["汽油" => "92"],
                    "oil.oil_level" => ["V" => "5"]
                ];
                return $configValues[$key] ?? null;
            }
        }
        
        if (!function_exists('checkIsMunicipality')) {
            function checkIsMunicipality($cityCode) {
                return false;
            }
        }
        
        if (!function_exists('bcmul')) {
            function bcmul($num1, $num2, $scale = 0) {
                return (string)($num1 * $num2);
            }
        }
        
        if (!function_exists('array_flip')) {
            function array_flip($array) {
                return array_flip($array);
            }
        }

        $this->stationPriceDataMock->shouldReceive('getDataByWhere')
            ->andReturn([
                ["station_id" => "test_station_1"]
            ]);
        $this->stationPriceDataMock->shouldReceive('updateByWhere')
            ->andReturn(true);

        // Mock BasicJob using alias
        $basicJobMock = Mockery::mock('overload:App\Jobs\BasicJob');
        $basicJobMock->shouldReceive('pushStationToStationHub')
            ->once()
            ->andReturn(true);

        // Mock Log using alias
        $logMock = Mockery::mock('overload:App\Models\Data\Log\QueueLog');
        $logMock->shouldReceive('handle')
            ->andReturn(true);

        // Mock Redis
        $redisMock = Mockery::mock(\Redis::class);
        $redisMock->shouldReceive('hmget')
            ->andReturn([]);
        $redisMock->shouldReceive('hdel')
            ->andReturn(true);

        // Mock app function
        if (!function_exists('app')) {
            function app($key = null) {
                $redisMock = Mockery::mock(\Redis::class);
                $redisMock->shouldReceive('hmget')
                    ->andReturn([]);
                $redisMock->shouldReceive('hdel')
                    ->andReturn(true);
                return $redisMock;
            }
        }

        // 执行测试
        $result = $this->pullOilForCy->handle();
        
        // 断言
        $this->assertNull($result);
    }

    /**
     * @covers \App\Console\Commands\PullOilForCy::handle
     */
    public function testHandleWithEmptyPriceList()
    {
        // Mock CYRequest
        $cyRequestMock = Mockery::mock('overload:Request\CY');
        $cyRequestMock->shouldReceive('handle')
            ->with("/thirdApi/station/list", Mockery::any())
            ->andReturn([
                "data" => [
                    "count" => 1,
                    "list" => [
                        [
                            "id" => "test_station_2",
                            "station_name" => "Test Station 2",
                            "province_code" => "110000",
                            "city_code" => "110100",
                            "station_type" => 1,
                            "trade_type" => 1,
                            "is_highway" => 0,
                            "contact_phone" => "12345678901",
                            "station_brand_name" => "中石化",
                            "oil_uint" => "升",
                            "beginTime" => "08:00",
                            "endTime" => "20:00",
                            "lng" => "116.12345678",
                            "lat" => "39.12345678",
                            "address" => "Test Address 2",
                            "is_stop" => 0
                            // Empty price_list
                        ]
                    ]
                ]
            ]);
        
        // Mock config function
        if (!function_exists('config')) {
            function config($key) {
                $configValues = [
                    "oil.oil_mapping.cy.oil_92" => "汽油_92_V",
                    "oil.oil_type" => ["汽油" => "gasoline"],
                    "oil.oil_no" => ["汽油" => "92"],
                    "oil.oil_level" => ["V" => "5"]
                ];
                return $configValues[$key] ?? null;
            }
        }
        
        if (!function_exists('checkIsMunicipality')) {
            function checkIsMunicipality($cityCode) {
                return false;
            }
        }
        
        if (!function_exists('bcmul')) {
            function bcmul($num1, $num2, $scale = 0) {
                return (string)($num1 * $num2);
            }
        }

        // Mock StationPriceData
        $this->stationPriceDataMock->shouldReceive('create')
            ->once()
            ->andReturn(true);
        $this->stationPriceDataMock->shouldReceive('getDataByWhere')
            ->andReturn([
                ["station_id" => "test_station_2"]
            ]);
        $this->stationPriceDataMock->shouldReceive('updateByWhere')
            ->andReturn(true);

        // Mock BasicJob
        $basicJobMock = Mockery::mock('overload:App\Jobs\BasicJob');
        $basicJobMock->shouldReceive('pushStationToStationHub')
            ->once()
            ->andReturn(true);

        // Mock Log
        $logMock = Mockery::mock('overload:App\Models\Data\Log\QueueLog');
        $logMock->shouldReceive('handle')
            ->andReturn(true);

        // Mock Redis
        $redisMock = Mockery::mock(\Redis::class);
        $redisMock->shouldReceive('hmget')
            ->andReturn([]);
        $redisMock->shouldReceive('hdel')
            ->andReturn(true);

        // Mock app function
        if (!function_exists('app')) {
            function app($key = null) {
                $redisMock = Mockery::mock(\Redis::class);
                $redisMock->shouldReceive('hmget')
                    ->andReturn([]);
                $redisMock->shouldReceive('hdel')
                    ->andReturn(true);
                return $redisMock;
            }
        }

        // 执行测试
        $result = $this->pullOilForCy->handle();
        
        // 断言
        $this->assertNull($result);
    }

    /**
     * @covers \App\Console\Commands\PullOilForCy::handle
     */
    public function testHandleWithException()
    {
        // Mock CYRequest to throw exception
        $cyRequestMock = Mockery::mock('overload:Request\CY');
        $cyRequestMock->shouldReceive('handle')
            ->with("/thirdApi/station/list", Mockery::any())
            ->andThrow(new \Exception("API Error"));

        // Mock Log
        $logMock = Mockery::mock('overload:App\Models\Data\Log\QueueLog');
        $logMock->shouldReceive('handle')
            ->andReturn(true);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('API Error');

        // 执行测试
        $this->pullOilForCy->handle();
    }

    public function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}